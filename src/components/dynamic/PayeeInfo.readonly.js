/**************************************************
 * Created by nanyuantingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import PayeeAccountCardBill from '../../elements/payee-account/PayeeAccountCardBill'

@EnhanceField({
  descriptor: {
    type: 'payeeInfo'
  },
  wrapper: wrapper(true)
})
export default class PayeeInfo extends PureComponent {
  render() {
    let { value, emptyPlaceholder = i18n.get('无'), dataSource } = this.props
    if (!value) return emptyPlaceholder
    return (
      <PayeeAccountCardBill
        hiddenActions
        {...value}
        dynamicCard="readonly"
        receivingCurrencyNumCode={dataSource?.form?.receivingCurrency}
      />
    )
  }
}
