import { get } from "lodash"
import { BillActionBase } from "./base-action"
import { message } from "@hose/eui"
import { app } from "@ekuaibao/whispered"
import { Fetch } from "@ekuaibao/fetch"

export class ExportAction extends BillActionBase {
  label = i18n.get('导出全部附件')

  onAction = () => {
    return this.handleExportAllAttachment()
  }

  handleExportAllAttachment = async () => {
    const { backlog } = this.props
    const hasAttachment = this.checkAttachment()
    if (hasAttachment) {
      const val = await app.open<any>('@layout:AsyncExportModal')
      const flowId = get(backlog, 'flowId.id')
      const { taskName } = val
      await Fetch.GET(`/api/v2/easyexcel/annex/export/async?taskName=${taskName}&flowId=${flowId}`)
      message.info(i18n.get('附件异步导出中,请稍后到导出管理中查看!'))
    } else {
      message.info(i18n.get('无可导出附件或附件是钉盘上传'))
    }
  }

  checkAttachment = () => {
    const { backlog } = this.props
    const form = get(backlog, 'flowId.form', {})
    const formComponents = get(backlog, 'flowId.form.specificationId.components', [])
    const hasAttachment = this.checkAttachmentFn(form, formComponents)
    return hasAttachment || this.checkDetailsAttachment()
  }

  checkAttachmentFn = (form: any, components: any) => {
    const attachmentsArr = components.filter(it => it?.type === 'attachments')
    let hasAttachment = false
    if (attachmentsArr.length > 0) {
      for (let i = 0; i < attachmentsArr.length; i++) {
        const el = attachmentsArr[i]
        const attachments = form?.[el?.field]
        if (attachments?.length > 0) {
          for (let j = 0; j < attachments.length; j++) {
            const item = attachments[j]
            // 必须要有非钉盘的文件才能导出,都是钉盘文件不到处
            if (!item?.key?.startsWith('DP:{')) {
              hasAttachment = true
              break
            }
          }
          if (hasAttachment) {
            break
          }
        }
      }
    }
    return hasAttachment
  }

   /**
    * 检测 明细是否包含附件
   */
   checkDetailsAttachment = () => {
    const { backlog } = this.props
    const details = get(backlog, 'flowId.form.details', [])
    let hasAttachment = false
    for (let i = 0; i < details.length; i++) {
      const el = details[i]
      const form = get(el, 'feeTypeForm', {})
      const components = get(el, 'specificationId.components', [])
      const hasAttachment2 = this.checkAttachmentFn(form, components)
      if (hasAttachment2) {
        hasAttachment = true
        break
      }
    }
    return hasAttachment
  }
}