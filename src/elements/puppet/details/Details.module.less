@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.details {
  // padding-bottom: 16px;
  height: auto;
  &.details-read {
    padding-bottom: 16px;
  }
  :global {
    .item-wrapper {
      &:hover {
        border: 1px solid rgba(0, 0, 0, 0);
        background: @gray-2;
      }
    }
    .item-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      .amount {
        font-size: 16px;
        color: #333333;
      }
    }
    .fold-btn,
    .expansion-btn {
      cursor: pointer;
      .font-size-2;
      line-height: @space-7;
      margin-left: @space-5;
      color: @color-brand-5;
      svg {
        margin-left: @space-2;
      }
    }
    .fold-btn {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
      margin: @space-4 0;
    }
  }
}

.details-fullscreen-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  z-index: 999;
  padding: 20px;
}

.details-permit-form {
  :global {
    .detail-content {
      .item-main {
        .money {
          display: none !important;
        }
      }
    }
    .action.action5 {
      padding-left: 0;
    }
  }
}

.fields-group-details-wrapper-container {
  .details-wrapper-container:nth-child(n + 1) {
    margin-top: 8px;
  }
  .details-wrapper-container {
    .field-group-title {
      color: var(--eui-text-title);
      font: var(--eui-font-body-b1);
      padding: 4px 8px;
      margin: 12px 0;
      border-radius: 8px;
      background: var(--eui-bg-body-overlay);
    }
  }
}

.fields-group-details-collapse-wrapper {
  border: none;
  :global {
    .ant-collapse-item {
      border: none;
      .ant-collapse-header {
        .font-weight-3;
      }
      .ant-collapse-content {
        border: none;
        .ant-collapse-content-box {
          padding: 0;
        }
      }
    }
  }
}
