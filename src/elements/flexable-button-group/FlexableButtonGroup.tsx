import React, { forwardRef, useMemo, useImperativeHandle, useState } from 'react'
import Overflow, { OverflowProps } from 'rc-overflow'
import { omit } from 'lodash'
import { Button, ButtonProps, Dropdown, MenuProps } from '@hose/eui'
import styles from './FlexableButtonGroup.module.less'

interface FlexableButtonGroupProps extends OverflowProps<ButtonGroupItem> {
  buttons: ButtonGroupItem[]
  moreText?: string
  useFakeDisabled?: boolean
}

export interface ButtonGroupItem {
  id?: string
  label?: string
  main?: boolean
  dangerous?: boolean
  onClick: () => void
  disabled?: boolean
  minorGroup?: boolean
  // 兼容老的按钮组
  buttonProps?: ButtonProps
  actionName?: string
  category?: ButtonProps['category']
  theme?: ButtonProps['theme']
  loading?: boolean
}

const isMain = (button: ButtonGroupItem) => {
  return button.main
}

const isNoGroup = (button: ButtonGroupItem) => {
  return button.minorGroup !== undefined && !button.minorGroup
}

export const getActionType = (item: ButtonGroupItem) => {
  let actionType = item.id || item.label
  // @ts-ignore
  const action = item.actionName || item.action // item.action 兼容旧版本的按钮标识
  if (action && action?.includes('.')) {
    actionType = action.split('.').slice(1).join('')
  }
  return actionType
}

/**
 * FlexableButtonGroup 是一个灵活的按钮组，它可以根据按钮的数量自动调整按钮的宽度，
 * 当按钮数量超过一定数量时，会自动显示一个下拉菜单，点击下拉菜单可以显示所有按钮。
 *
 * 但这个有一个限制，不能设置最大显示个数，但可以设置 max-width 来限制容器
 * 宽度来达到限制现实元素个数的目的，默认宽度为600px，大概最多5、6个按钮的样子。
 */
export const FlexableButtonGroup = forwardRef(({
  buttons,
  moreText = i18n.get('更多'),
  useFakeDisabled,
  ...overflowProps
}: FlexableButtonGroupProps, ref) => {
  const [lastVisibleIndex, setLastVisibleIndex] = useState(0)
  const [, setForceUpdate] = useState({})
  const sortedButtons = useMemo(
    () =>
      buttons.sort((a, b) => {
        if (isMain(a) && !isMain(b)) {
          return -1
        }
        if (!isMain(a) && isMain(b)) {
          return 1
        }
        if (isNoGroup(a) && !isNoGroup(b)) {
          return -1
        }
        if (!isNoGroup(a) && isNoGroup(b)) {
          return 1
        }
        return 0
      }),
    [buttons]
  )

  useImperativeHandle(ref, () => ({
    forceUpdate: () => setForceUpdate({})
  }))

  const renderButtonItem: OverflowProps<ButtonGroupItem>['renderItem'] = item => {
    const { label, main, category, theme, loading, dangerous, onClick, disabled, buttonProps } = item
    const _disabled = disabled || buttonProps?.disabled
    const _loading = loading || buttonProps?.loading
    return (
      <div className={styles['flexable-button-group-item']}>
        <Button
          category={ category || (main ? 'primary' : 'secondary')}
          theme={theme || (dangerous ? 'danger' : 'default')}
          data-testid={buttonProps?.['data-testid'] || `flexable-button-${getActionType(item)}`}
          disabled={_disabled}
          onClick={() => {
            if (useFakeDisabled && _disabled) {
              // 不操作
            } else {
              onClick()
            }
          }}
          loading={_loading}
          {...omit(buttonProps, ['children', 'loading', 'disabled'])}
        >
          {buttonProps?.children || label}
        </Button>
      </div>
    )
  }

  const renderRest = () => {
    const items: MenuProps['items'] = sortedButtons.slice(lastVisibleIndex + 1).map(btn => ({
      key: btn.id || btn.label,
      label: btn.label,
      onClick: btn.onClick,
      'data-testid': btn.buttonProps?.['data-testid'] || `flexable-button-${getActionType(btn)}`
    })) as MenuProps['items']

    return (
      <Dropdown menu={{ items }}>
        <Button category="secondary" theme="default" data-testid="flexable-button-more">
          {moreText}
        </Button>
      </Dropdown>
    )
  }

  return (
    <Overflow
      data={sortedButtons}
      maxCount="responsive"
      className={styles['flexable-button-group']}
      renderItem={renderButtonItem}
      renderRest={renderRest}
      onVisibleChange={newLastIndex => {
        setLastVisibleIndex(newLastIndex)
      }}
      {...overflowProps}
    />
  )
})
