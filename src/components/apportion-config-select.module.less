.apportion-config-select {
 
}

.apportion-config-comp-wrapper {
  background-color: var(--eui-bg-float-overlay);
  padding: 16px;
  margin-top: 8px;
  border-radius: 8px;
  :global {
    .eui-image{
      display: none;
    }
    .eui-form-item-explain {
      margin-bottom: 0;
    }
    .eui-space-horizontal {
      align-items: flex-start;
      .eui-input-number {
        height: 32px;
      }
    }

    .eui-space-item {
      .eui-form-item {
        margin-bottom: 0;
      }
    }
    .flow-template-width {
      width: 220px;
    }
    .apportion-rule-width {
      width: 284px;
      display: flex;
      gap: 8px;
      >.eui-form-item,  >.eui-select{
        flex: 1;
        overflow: hidden;
      }
    }
    .apportion-limit-width{
      width: 210px;
      display: flex;
      gap: 8px;
      >.eui-form-item, >.eui-select{
        flex: 1;
        overflow: hidden;
      }
    }
    .apportion-label-width {
      flex: 1;
    }
    .form-list-label {
      display: flex;
      align-items: center;
      color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
      font: var(--eui-font-body-r1);
      margin-top: 8px;
      margin-bottom: 8px;

      .apportion-label-width,.apportion-limit-width{
        gap:0;
      }

      .required{
        color: var(--eui-function-danger-500, #F53F3F);
        font: var(--eui-font-body-b1);
        margin-left: 2px;
      }
    }
  }
}


.image-preview {
  width: 100% !important;
}