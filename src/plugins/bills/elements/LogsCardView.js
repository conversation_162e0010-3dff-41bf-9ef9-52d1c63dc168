/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/7.
 */
import React, { PureComponent } from 'react'
import { Popover } from 'antd'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './LogsCardView.module.less'
import { stateMap, skipTypeMap } from '@ekuaibao/lib/lib/enums'
import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import SVG_FILE from '../../../elements/puppet/details/images/icon-file.svg'
import IconTag from '../../../elements/puppet/IconTag'
import AttachmentList from '../../../elements/puppet/attachmentList/AttachmentList'

import COMMON_MARK from '../../../images/common_mark.svg'

function fnFormatDate(timeStamp) {
  let now = moment().format('YYYY-MM-DD')
  let day = moment(timeStamp).format('YYYY-MM-DD')
  let diff = moment(day).diff(now, 'days', true)
  let time = moment(timeStamp).format('HH:mm')
  return diff < -6
    ? moment(timeStamp).format(i18n.get('MM月DD日'))
    : diff < -2
    ? moment(timeStamp).format('ddd HH:mm')
    : diff === -2
    ? `${i18n.get('前天')} ${time}`
    : diff === -1
    ? `${i18n.get('昨天')} ${time}`
    : diff === 0
    ? `${i18n.get('今天')}${time}`
    : ''
}

const autoAgree = function(item) {
  let { operatorId, attributes, nextOperatorId, attachments, time } = item
  let data = formatAttachments(item)
  let hasAttachments = attachments && attachments.length
  return attributes.counterSign ? (
    <div>
      <div className="line-2">
        {renderDate(item)}
        <span className="txt mr-5">{operatorId.name}</span>
        {attributes.complete && attributes.nextName ? (
          <span className="action">{i18n.get('会签审批同意')}</span>
        ) : (
          <span className="action">{i18n.get('会签同意')}</span>
        )}
      </div>
      {attributes.comment && <div className="comment">{attributes.comment}</div>}
      {!!hasAttachments && (
        <Popover title={data.label} content={data.attachmentsContent}>
          <IconTag
            className="mr-10 ml-8 mt-10 tag-style"
            src={SVG_FILE}
            text={i18n.get('附件%1', { length: v.leng, label: v.label })}
          />
        </Popover>
      )}
    </div>
  ) : (
    <div>
      <div className="line-2">
        {renderDate(item)}
        <span className="txt mr-5">{item.operatorId.name}</span>
        {nextOperatorId ? (
          <span className="action">{i18n.get('审批同意')}</span>
        ) : (
          <span className="action">{i18n.get('同意并审批通过')}</span>
        )}
      </div>
      {attributes.comment && <div className="comment">{attributes.comment}</div>}
      {!!hasAttachments && (
        <Popover title={data.label} content={data.attachmentsContent}>
          <IconTag
            className="mr-10 ml-8 mt-10 tag-style"
            src={SVG_FILE}
            text={i18n.get(`{__k0} 个{__k1}`, { __k0: data.length, __k1: data.label })}
          />
        </Popover>
      )}
    </div>
  )
}

function fnClickAttachment(attachmentsData, clickItem) {
  api.emit('@vendor:preview:images', attachmentsData, clickItem.url)
}

//TODO 冗余代码，合并后删掉
function fnFormatAttachment(list = []) {
  let fileList = list.map(v => {
    let { fileId } = v
    if (fileId && typeof fileId === 'object') {
      return {
        key: fileId.key,
        fileName: v.fileName,
        url: fileId.url,
        thumbUrl: fileId.thumbUrl,
        fileId: fileId.id
      }
    }
    return v
  })
  return fileList
}

const formatAttachments = item => {
  if (item.attachments) {
    let items = fnFormatAttachment(item.attachments)
    let popoverContent = {
      length: items.length,
      attachmentsContent: <AttachmentList onClickAttachment={fnClickAttachment.bind(this, items)} fileList={items} />,
      label: i18n.get('附件')
    }

    return popoverContent
  }
}

const renderDate = item => {
  return <span className="date mr-5">{fnFormatDate(item.time)}</span>
}

const flowStateMap = {
  submit: {
    render(item) {
      return (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId.name}</span>
          <span className="action">{i18n.get('送审给')}</span>
          <span className="txt ml-5">
            {item.attributes.nextCounterSign || !item.nextOperatorId
              ? `${item.attributes.nextName}` + i18n.get('会签')
              : item.nextOperatorId.name}
          </span>
        </div>
      )
    }
  },
  agree: {
    render(item) {
      return autoAgree(item)
    }
  },
  retract: {
    render(item) {
      return (
        <div>
          <div className="line-2">
            {renderDate(item)}
            <span className="txt mr-5">{item.operatorId.name}</span>
            <span className="action">{i18n.get('撤回了该单据')}</span>
          </div>
        </div>
      )
    }
  },
  reject: {
    render(item) {
      const { attachments } = item
      const data = formatAttachments(item)
      const hasAttachments = attachments && attachments.length
      return (
        <div>
          <div className="line-2">
            {renderDate(item)}
            <span className="txt mr-5">{item.operatorId.name}</span>
            <span className="action">{i18n.get('驳回了该单据')}</span>
          </div>
          {item.attributes.comment && <div className="comment translate-ignore-class">{item.attributes.comment}</div>}
          {!!hasAttachments && (
            <Popover title={data.label} content={data.attachmentsContent}>
              <IconTag
                className="mr-10 ml-8 mt-10 tag-style"
                src={SVG_FILE}
                text={i18n.get(`{__k0} 个{__k1}`, { __k0: data.length, __k1: data.label })}
              />
            </Popover>
          )}
        </div>
      )
    }
  },
  pay: {
    render(item) {
      return item.attributes.accountCompany ? (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId.name}</span>
          <span className="action">{i18n.get('使用')}</span>
          <span className="txt ml-5 mr-5">{item.attributes.accountCompany && item.attributes.accountCompany.name}</span>
          <span className="action">{i18n.get('支付完成')}</span>
        </div>
      ) : (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId.name}</span>
          <span className="action">{i18n.get('确认')}</span>
          <span className="txt ml-5 mr-5"> {window.IS_SMG ? i18n.get('预支核销') : i18n.get('借款核销')}</span>
        </div>
      )
    }
  },
  paying: {
    render(item) {
      return (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId.name}</span>
          <span className="action">{i18n.get('使用')}</span>
          <span className="txt ml-5 mr-5">{item.attributes.accountCompany && item.attributes.accountCompany.name}</span>
          <span className="action">{i18n.get('发起支付')}</span>
        </div>
      )
    }
  },
  failure: {
    render(item) {
      return (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId.name}</span>
          <span className="action">{i18n.get('使用')}</span>
          <span className="txt ml-5 mr-5">{item.attributes.accountCompany && item.attributes.accountCompany.name}</span>
          <span className="action">{i18n.get('支付失败')}</span>
        </div>
      )
    }
  },
  'select.approver': {
    render(item) {
      return (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId.name}</span>
          <span className="action">{i18n.get('将')}</span>
          <span className="txt ml-5 ml-5"> {item.attributes.name}</span>
          <span className="action">{i18n.get('由')}</span>
          <span className="txt mr-5">{item.attributes.oldApproverId && item.attributes.oldApproverId.name}</span>
          <span className="action">{i18n.get('改为')}</span>
          <span className="txt ml-5 mr-5"> {item.attributes.newApproverId && item.attributes.newApproverId.name} </span>
        </div>
      )
    }
  },
  skipped: {
    render(item) {
      let skipDetail = skipTypeMap()[item.attributes.skippedType]
      return (
        <div className="line-2">
          {renderDate(item)}
          <span className="action mr-5">{i18n.get('由于')}</span>
          <span className="txt mr-5">{skipDetail}</span>
          <span className="action">{i18n.get('自动跳过')}</span>
          <span className="txt ml-5 mr-5"> {item.attributes.name || i18n.get('此')}</span>
          <span className="action">{i18n.get('环节')}</span>
        </div>
      )
    }
  },
  autoAgree: {
    render(item) {
      return autoAgree(item)
    }
  },
  'admin.skipnode': {
    render(item) {
      return (
        <div className="line-2">
          {renderDate(item)}
          <span className="txt mr-5">{item.operatorId?.name}</span>
          <span className="txt">{i18n.get('跳过审批')}</span>
          <span className="txt"> {i18n.get(`原因：管理员({__k0})手动跳过`, { __k0: item.operatorId?.name })} </span>

        </div>
      )
    }
  }
}
export default class LogsCardView extends PureComponent {
  render() {
    let { dataSource = {}, changeTab } = this.props
    let logs = dataSource.logs || []
    let lastLog = logs[logs.length - 1]

    if (!lastLog) return <div />
    const commentCount = logs.filter(e => !!(e.attributes && e.attributes.comment)).length
    let action = lastLog.action.replace('freeflow.', '')
    let state = stateMap()[dataSource.state] || {}
    let label = state.text
    if (dataSource.state === 'paying' && (action === 'paying' || action === 'repaying')) {
      label = i18n.get('支付中')
    }

    const handleClick = () => {
      changeTab && changeTab('flowPlan')
    }

    const text = i18n.get('个审批意见')

    return (
      <div className={styles['logs-card']}>
        <div className="wrapper">
          <div className="line-1">
            <div className="label">
              <span className="circle-icon" style={{ background: state.color }} />
              <span style={{ color: state.textColor }}>{i18n.get(label)}</span>
            </div>
            {!!commentCount && (
              <div className="comment-count" onClick={handleClick}>
                <img className="w-12 h-12" src={COMMON_MARK} />
                <span className="ml-5">{`${commentCount}${text}`}</span>
              </div>
            )}
          </div>
          {flowStateMap[action].render(lastLog)}
        </div>
      </div>
    )
  }
}
