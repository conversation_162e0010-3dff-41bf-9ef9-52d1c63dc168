/*************************************************************
 * AUTHOR : nanyuantingfeng DATE : 3/28/16 TIME : 18:08
 * UPDATE : wangguanjia DATE: 21/09/2022
 ************************************************************/
const { RetryChunkLoadPlugin } = require('@ekuaibao/webpack-retry-chunk-load-plugin')
const config = require('./webpack.basic')
const path = require('path')
const ScriptExtHtmlWebpackPlugin = require('script-ext-html-webpack-plugin')
const configExternals = require('./configs/externals')

config.output.path(path.resolve(process.cwd(), 'build'))
config.output.chunkFilename('[name].[chunkhash:8].js')
const uploadMonio = require('./upload-monio')
require('@ekuaibao/vendor-lodash/patch')(config)
require('@ekuaibao/vendor-common/patch')(config)
require('@ekuaibao/vendor-whispered/patch')(config)
require('@ekuaibao/vendor-antd/patch')(config)
config.patch.vendors([
  '*.min.js',
  ['@ekuaibao/vendor-lodash', '@ekuaibao/vendor-common', '@ekuaibao/vendor-whispered', '@ekuaibao/vendor-antd']
])

configExternals(config)

const gitTag = process.env.CI_BUILD_TAG || '' // 获取提交版本号

if (process.env.NODE_ENV === 'production' && !process.env.IS_STANDALONE) {
  config.plugin('RetryChunkLoadPlugin').use(RetryChunkLoadPlugin)
}

config.patch.defines({
  'process.env.RELEASE_VERSION': JSON.stringify(gitTag),
  IS_SZJL: JSON.stringify(gitTag.includes('standalone-szjl'))
})

config.patch.entry({
  app: './src/hosting/app/index.ts',
  group: './src/hosting/group/index.ts',
  ldap: './src/hosting/ldap/index.ts',
  debugger: './src/hosting/browser/index.ts',
  huawei: './src/hosting/huawei/index.ts',
  dingtalk: './src/hosting/dingtalk/index.ts',
  kdcloud: './src/hosting/kdcloud/index.ts',
  shareexpinfo: './src/hosting/shareexpinfo/index.ts',
  wx: './src/hosting/weixin/index.ts',
  'wx-message': './src/hosting/weixin/message.ts',
  payment: './src/hosting/payment/index.ts',
  thirdparty: './src/hosting/thirdparty/index.ts',
  feishu: './src/hosting/feishu/index.ts',
  'fs-message': './src/hosting/feishu/message.ts',
  nbbank: './src/hosting/nbbank/index.ts',
  'nbbank-message': './src/hosting/nbbank/message.ts',
  mc: './src/hosting/mc/index.ts',
  hybrid: './src/hosting/hybrid/index.ts',
  hwly: './src/hosting/hwly/index.ts',
  cgbpayment: './src/hosting/cgbbank/index.ts',
})
uploadMonio(config)
// add custom callback to plugin javascript
config.plugin(`ScriptExtHtmlWebpackPlugin::app`).use(ScriptExtHtmlWebpackPlugin, [
  {
    custom: [
      {
        test: /\.js$/,
        attribute: 'onload',
        value: 'void(window.ekbLoader && ekbLoader.resLoaded())'
      },
      {
        test: /\.js$/,
        attribute: 'onerror',
        value: 'void(window.ekbLoader && ekbLoader.resErrorReport())'
      },
      {
        test: /assets\/plugin\-web\-(.+)\.js$/,
        attribute: 'defer',
        value: true,
      },
    ]
  }
])

const env = process.env.NODE_ENV || 'development'
const isProduction = env === 'production'

config.optimization.merge({
  // an additional chunk to each entrypoint containing only the runtime
  runtimeChunk: true,
  // 会增加chunk体积，再考虑
  // moduleIds: isProduction ? 'hashed' : 'named',
  // chunkIds: isProduction ? 'natural' : 'named',
  splitChunks: {
    chunks: 'all',
    minSize: 20000,
    minChunks: 1,
    maxAsyncRequests: 10, // 适中的异步请求数量
    maxInitialRequests: 6, // 严格控制首屏请求数量
    enforceSizeThreshold: 200000 * 1000,
    cacheGroups: {
      // 初始化资源中node_modules中的模块，
      // 区分的原因在于总的initial资源过大 gzipped 之后大于 1M。
      // 为了更好的利用浏览器的并行加载，将其分为两个部分。
      initialVendors: {
        test: /[\\/]node_modules[\\/]/,
        priority: -4,
        chunks: 'initial',
        minSize: 1000 * 1000,
        maxSize: 2000 * 1000,
        name: 'initialVendors'
      },
      initialAssets: {
        priority: -5,
        chunks: 'initial',
        minSize: 1000 * 1000,
        maxSize: 2000 * 1000,
        name: 'initialAssets'
      },

      // 合并小的异步vendor文件
      smallAsyncVendors: {
        test: /[\\/]node_modules[\\/]/,
        priority: -6,
        chunks: 'async', // 只处理异步chunk，不影响首屏
        minSize: 1000, // 1KB以上就合并
        maxSize: 500000, // 最大500KB
        name: 'vendors-async-small',
        enforce: true
      },

      // 合并小的初始vendor文件
      smallInitialVendors: {
        test: /[\\/]node_modules[\\/]/,
        priority: -7,
        chunks: 'initial',
        minSize: 1000,
        maxSize: 800000,
        name: 'vendors-initial-small',
        enforce: true
      },

      defaultVendors: {
        test: /[\\/]node_modules[\\/]/,
        priority: -10,
        chunks: 'async',
        name: 'defaultVendors',
        minSize: 100000
      },

      // 合并小的异步应用代码
      smallAsyncChunks: {
        priority: -12,
        chunks: 'async',
        minSize: 500,
        maxSize: 300000,
        name: 'common-async-small',
        enforce: true,
        reuseExistingChunk: true
      },

      // 合并小的初始应用代码
      smallInitialChunks: {
        priority: -13,
        chunks: 'initial',
        minSize: 500,
        maxSize: 400000,
        name: 'common-initial-small',
        enforce: true,
        reuseExistingChunk: true
      },

      default: {
        priority: -20,
        chunks: 'initial',
        minSize: 50000,
        maxSize: 2000 * 1000,
        reuseExistingChunk: true,
        name: 'default'
      },

      'iconfont-merged': { // 不经常变更，方便cdn做缓存
        test: /[\\/]src[\\/]file[\\/]icon[0-9a-zA-Z]+\.js$/,
        name: 'iconfont-merged',  // 输出文件名
        chunks: 'all',  // 应用于所有类型的块（同步和异步）
        enforce: true,  // 强制创建这个块
      }
    },
  },
});


// // 使用 babel-plugin-import 来减少打包体积
// config.patch.imports([
//   {
//     "libraryName": "@hose/eui-icons",
//     'camel2DashComponentName': false,
//     "customName": (name) => {
//       console.log(name)
//       if (/^illustration/i.test(name)) {
//         return `@hose/eui-icons/lib/es/icons/illustration/${name}`
//       } else if (/^line\surface/i.test(name)) {
//         return `@hose/eui-icons/lib/es/icons/line-surface/${name}`
//       } else if (/^filled|outlined/i.test(name)) {
//         return `@hose/eui-icons/lib/es/icons/single-tone/${name}`
//       } else if (/^twotone/i.test(name)) {
//         return `@hose/eui-icons/lib/es/icons/two-tone/${name}`
//       } else {
//         if (process.env.NODE_ENV !== 'production') {
//           console.log('请联系 @hose/eui-icons 开发者提供合理的命名规则，或者适配当前配置到最新版目录结构')
//         }
//       }
//     }
//   }
// ])

module.exports = config
