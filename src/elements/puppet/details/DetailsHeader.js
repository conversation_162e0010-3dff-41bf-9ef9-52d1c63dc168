/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/7/12.
 */
import React, { useState, useRef, useLayoutEffect, useEffect } from 'react'
import {
  OutlinedDirectionSwitchVertical,
  OutlinedDataColumnsetting,
  OutlinedTipsAdd,
  OutlinedDirectionUpload,
} from '@hose/eui-icons'
import clx from 'classnames'
import { Affix } from 'antd'
import { message, Button, Tooltip, Checkbox, Popconfirm, Dropdown as EUIDropdown } from '@hose/eui'
import styles from './DetailsHeader.module.less'
import SVG_EX_APPLICATION from './images/ex-application.svg'
import Money from '../Money'
import {
  calcDetailsAmount,
  getNodeValueByPath,
  fnFormatAttachment,
  calcDetailsReceivingAmount
} from '@ekuaibao/lib/lib/lib-util'
import { showModal } from '@ekuaibao/show-util'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import EKBIcon from '../../ekbIcon'
import { T } from '@ekuaibao/i18n'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { getApportionHideDetailCount } from '../../../components/utils/fnHideFields'
import FullScreenComp from './FullscreenComp'
import { related } from '../../feeDetailViewList/Related'
import { useViewAndGroup } from './hooks/useViewAndGroup'
import { iconsMap } from './import-icons/icons'
const IconMap = api.invokeServiceAsLazyValue('@custom-specification:import:FeeTypeImportIconMap')
const fnCheckNeedPhone = api.invokeServiceAsLazyValue('@expansion-center:import:fnCheckNeedPhone')

const LIST = {
  flight: { smallIcon: iconsMap.flight },
  train: { smallIcon: iconsMap.train },
  hotel: { smallIcon: iconsMap.hotel },
  hose_mall_flight: { smallIcon: iconsMap.flight },
  hose_mall_train: { smallIcon: iconsMap.train },
  hose_mall_hotel: { smallIcon: iconsMap.hotel },
  invoiceManual: { smallIcon: iconsMap.handWritenInvoice, action: 'onImportInputInvoiceClick' },
  invoicePdf: { smallIcon: iconsMap.pdfInvoice, action: 'onImportInvoiceClick' },
  invoiceOCR: { smallIcon: iconsMap.ocr, action: 'onImportInvoiceOCRClick' },
  medicalInvoiceOCR: { smallIcon: IconMap()['medicalInvoiceOCR'], action: 'onImportOCRMedicalClick' },
  recordExpends: { smallIcon: iconsMap.suishouji, action: 'onRecordExpendsClick' },
  invoiceAlipay: { smallIcon: iconsMap.alipay, action: 'onImportAliPayInvoiceClick' },
  aifapiao: {
    smallIcon: iconsMap.aifapiao,
    action: 'onImportAifaPiaoInvoiceClick'
  },
  didi: thirdPartyList => ({
    smallIcon: iconsMap.didi,
    menuItem: checkDiDiStatus(thirdPartyList).didi,
    hidden: !checkDiDiStatus(thirdPartyList).active,
    action: 'onThirdPartyImportClick',
  }),
  officialCard: { smallIcon: iconsMap.businessCard, action: 'onImportCSCClick' },
  excel: { smallIcon: iconsMap.excel, action: 'onImportExcelClick' },
  dataLink: data => ({ smallIcon: getImageUrl(data.dataLinkEntity.platformId), action: 'onSelectDataLink' }),
  applyDetail: { smallIcon: iconsMap.applyDetail, action: 'onImportApplyDetail' },
  overseasInvoice: { smallIcon: iconsMap.overseasInvoice, action: 'onImportOverseasInvoiceClick' }
}

function checkDiDiStatus(thirdPartyList = []) {
  let didi = thirdPartyList.filter(v => {
    let corporationBinding = v.corporationBinding || []
    let info = corporationBinding.find(o => o.platformId === 'DIDI') || {}
    return info.active
  })
  return { active: didi.length > 0, didi: didi[0] }
}

function getMenu(thirdPartyList, importList) {
  let groups = []
  for (let group of importList) {
    const menuList = []
    group?.list?.forEach(item => {
      if (item?.id?.indexOf('dataLink') === 0) {
        let data = LIST['dataLink'](item)
        if (data) {
          menuList.push({ ...item, ...data })
        }
      } else if (item?.id === 'didi') {
        let data = LIST[item?.id](thirdPartyList)
        if (data && !data.hidden) {
          menuList.push({ ...item, ...data })
        }
      } else {
        let data = LIST[item?.id]
        if (data) {
          menuList.push({ ...item, ...data })
        }
      }
    })
    groups.push({ ...group, list: menuList })
  }
  return groups
}

function getImageUrl(data) {
  let { type } = data
  if (type === 'PRIVATE_CAR') {
    return iconsMap.privateCard
  } else if (type === 'ELEM') {
    return iconsMap.elem
  }
  const fileId = getNodeValueByPath(data, 'icon.fileId')
  return fileId ? <img src={fileId.thumbUrl} width={40} height={40} /> : iconsMap.dataLink
}

export default function DetailHeader(props) {
  let {
    billType,
    dataSource,
    isEdit,
    isModify,
    fieldsGroupValue = [],
    onAddDetailClick,
    onBatchApportionClick,
    onBatchRemoveDetailClick,
    onCheckedAllClick,
    onChangeView,
    onChangeFieldsGroup,
    thirdPartyList = [],
    selectedData,
    showType = '',
    importList,
    configs,
    onCopyDetailClick,
    importAble,
    unvisibleCount,
    isEnableAffix,
    onRemoveToRecordExpendsClick,
    field,
    billState,
    isInModal,
    selectedSortMenu,
    onFilterSortMenu,
    hideSortBtn,
    onQuickExpendsClick,
    tagDataSource,
    handleCheckFeeType,
    toApportionCheck,
    handleChangeShowDetailList,
    showDetailList,
    flowId,
    submitterId,
    isInvoiceManagePermissions,
    riskData,
    sortTypeList = [],
    dataSourceOld,
    onFullscreen,
    feetypeTableEdit,
    autoExpenseWithBillStriction,
    isFullscreen = false,
    loadingStatus,
    allowSelectionReceivingCurrency
  } = props
  const [groupPanel, viewModePanel] = useViewAndGroup({
    feetypeTableEdit,
    showType,
    onChangeView,
    fieldsGroupValue,
  })
  const [isLineFeed, setIsLineFeed] = useState(false)
  const headerTitleAndActionsRef = useRef(null)
  const affixRef = useRef(null)

  let imageList = []
  let newImagesList = []
  const showValue = dataSource.reduce((preVal, curVal) => {
    const fileList = curVal?.feeTypeForm?.invoiceForm?.attachments ? curVal?.feeTypeForm?.invoiceForm?.attachments : []
    const invoiceItem = curVal?.feeTypeForm?.invoiceForm?.invoices ? curVal.feeTypeForm.invoiceForm.invoices : []
    let files = fnFormatAttachment(fileList)
    files.map(item => (item.type = 'invoicePhoto'))
    let Item = files.concat(invoiceItem)
    return preVal.concat(Item)
  }, [])
  // const supportReviewedItems = showValue.filter(item => {
  //   // 过滤掉不支持审阅模式的发票类型
  //   const { master, type: entityType } = item
  //   const entityId = master && master.entityId
  //   const entity = entityId || entityType
  //   return ReviewedInvoiceTypes.indexOf(entity) > -1
  // })
  //  根据发票中supplementInvoiceBatchId判断是否有跨单据的票存在，若存在则不显示【审阅模式】
  const ifPriview = dataSource.some(item => !!item.feeTypeForm?.invoiceForm?.supplementInvoiceBatchId)

  let money = calcDetailsAmount(dataSourceOld)
  const receivingAmount = calcDetailsReceivingAmount(dataSourceOld)
  let len = dataSource.length
  const countText =
    unvisibleCount > 0 ? i18n.get(`{__k0}（还有{__k1}条无可见权限）`, { __k0: len, __k1: unvisibleCount }) : len
  let checked = selectedData
  let isAllCheck = len && checked.length === len
  const isEBussCard = configs?.find(item => item.isEBussCard)
  const isPermitForm = tagDataSource?.openFrom === 'permit-form'


  useLayoutEffect(() => {
    let resizeObserver
    const handleDrawerWidthResize = () => {
      if (typeof ResizeObserver === 'undefined') {
        return
      }
      if (headerTitleAndActionsRef.current) {
        // even useLayoutEffect can't make sure the dom has proper styles, so we need to use ResizeObserver
        resizeObserver = new ResizeObserver(entries => {
          for (let entry of entries) {
            setIsLineFeed(entry.target.clientHeight >= 50)
          }
        })
        resizeObserver.observe(headerTitleAndActionsRef.current)
      }
    }

    window.addEventListener('drawerWidthResize', handleDrawerWidthResize)

    handleDrawerWidthResize();
    return () => {
      window.removeEventListener('drawerWidthResize', handleDrawerWidthResize)
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    }
  }, [headerTitleAndActionsRef.current])

  const fnHandleImportDetail = ({ action, data, value }) => {
    const selectedMenu = importList.find(el => el.id === value)
    const groupType = get(data, 'platformId.groupType')
    const type = get(data, 'platformId.type')
    fnCheckNeedPhone()({ groupType, type }).then(res => {
      if (res) {
        return api.open('@aikeCRM:RegisterPhoneModal', { type }).then(() => {
          showModal.info({
            title: i18n.get('数据同步中'),
            content: i18n.get('数据正在同步中，请稍候再试。'),
            okText: i18n.get('确定')
          })
        })
      }

      if (!!~['hose_mall_flight', 'hose_mall_hotel', 'hose_mall_train', 'flight', 'hotel', 'train'].indexOf(action)) {
        return props['onThirdPartyImportClick']({
          orderTypeList: [action],
          id: 'YEEGO'
        })
      }

      return (
        props[action] &&
        props[action]({ ...data, importModeName: selectedMenu && selectedMenu.title }, undefined, selectedMenu)
      )
    })
  }

  const menuList = getMenu(thirdPartyList, importList)

  const getImportType = async () => {
    if (handleCheckFeeType(handleImportModal)) return
    if (isEBussCard) {
      let result = {}
      menuList.forEach(item => {
        const recordExpends = item.list.find(ii => ii.action == 'onRecordExpendsClick' || ii.id === 'recordExpends')
        if (recordExpends) {
          result = {
            data: {
              ...recordExpends.dataLinkEntity,
              filterId: recordExpends.filterId,
              linkDataLinkEntity: recordExpends.linkDataLinkEntity,
              linkFilterId: recordExpends.linkFilterId,
              linkId: recordExpends.linkId
            },
            action: recordExpends.action || recordExpends.id,
            value: recordExpends.id
          }
        }
      })
      return result
    }
    const hideGroupHeader = tagDataSource?.dataFromOrder?.showImportInPermit
    return await api.open('@bills:ImportDetailModal', {
      groups: menuList,
      hideGroupHeader
    })
  }

  const handleImportModal = async () => {
    if (modify) {
      return
    }
    if (props?.tableVm?.isEdit && props?.tableVm?.showType === 'TABLE_NO_GROUP') {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    const staffId = api.getState()['@common'].userinfo?.data?.staff?.userId
    window?.TRACK?.('importcost_click', {
      actionName: i18n.get('费用明细导入'),
      corpId: Fetch.ekbCorpId,
      staffId
    })
    // const result = await api.open('@bills:ImportDetailModal', { groups: menuList })
    const result = await getImportType()
    if (result?.action === 'onImportExcelClick') {
      window?.TRACK?.('Importcost_excel_click', {
        actionName: i18n.get('费用明细的excel导入方式'),
        corpId: Fetch.ekbCorpId,
        staffId
      })
    }
    result && fnHandleImportDetail(result)
  }

  const tip = (title, tipContent, callback, icon, buttonProps) => {
    return (
      <Tooltip placement="bottom" title={tipContent ? tipContent : i18n.get('此操作仅在勾选明细项后可用')}>
        <Button
          category="text"
          theme="highlight"
          disabled={true}
          size="small"
          className="other-action"
          onClick={callback}
          icon={icon}
          {...buttonProps}
        >
          {title}
        </Button>
      </Tooltip>
    )
  }

  const ApportionText = i18n.get('分摊')
  const apportionCheckeds = getSameApportionSpecificationId(checked)
  const checkIfCanApportion = () => {
    if (props?.tableVm?.isEdit && props?.tableVm?.showType === 'TABLE_NO_GROUP') {
      return message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
    }
    const { bus, billSpecification } = toApportionCheck || {}
    const detailsConfig = billSpecification && billSpecification.components?.find(com => com?.field === 'details')
    const batchApportionDisabled = detailsConfig?.batchApportionDisabled
    if (bus && batchApportionDisabled && selectedData?.length) {
      bus?.getValue().then(form => {
        const billData = { ...form, specificationId: billSpecification }
        getApportionHideDetailCount(billData, selectedData).then(len => {
          if (len) {
            showModal.info({
              title: i18n.get('提示'),
              content: i18n.get(`所选明细中存在 {__k0} 条已设置隐藏分摊，不能进行批量处理`, { __k0: len }),
              okText: i18n.get('确定')
            })
          } else {
            onBatchApportionClick(apportionCheckeds)
          }
        })
      })
    } else {
      onBatchApportionClick(apportionCheckeds)
    }
  }
  let Apportion =
    apportionCheckeds.length > 0 ? (
      <Button
        category="text"
        theme="highlight"
        className="other-action"
        size="small"
        onClick={() => checkIfCanApportion()}
      >
        {ApportionText}
      </Button>
    ) : null
  // (
  //   tip(ApportionText, i18n.get('此操作仅在勾选明细项且有可见的相同分摊模板时可用'))
  // )

  const RemoveText = i18n.get('删除')
  const tripChecked = checked?.filter(v => !v.tripEdit)?.length > 0
  let txt = tripChecked ? i18n.get('明细中包含的行程已发生订购，需要先取消订单才可以进行此操作') : undefined
  const delayRemoveActionAfterConfirming = () => {}
  let Remove = txt ? (
    tip(RemoveText, txt, delayRemoveActionAfterConfirming)
  ) : checked.length > 0 ? (
    <Button category="text" theme="highlight" size="small" className="other-action" onClick={delayRemoveActionAfterConfirming}>
      {RemoveText}
    </Button>
  ) : null // tip(RemoveText)

  const recordExpendsText = i18n.get('移至随手记')
  let RemoveToRecordExpends = txt ? (
    tip(RemoveText, txt, onRemoveToRecordExpendsClick)
  ) : checked.length > 0 ? (
    <Button
      category="text"
      theme="highlight"
      size="small"
      className="other-action"
      onClick={onRemoveToRecordExpendsClick}
    >
      {recordExpendsText}
    </Button>
  ) : null //(
  //   tip(recordExpendsText)
  // )

  let modify = isModify || !menuList || menuList.length === 0 || importAble

  let renderActions = configs => {
    let blackList = ['reconciliation', 'settlement']
    let apply = configs.find(v => v.ability === 'apply') || {}
    const onlyRequisitionDetails = apply.canApply && apply.onlyRequisitionDetails

    const copyCanUse = checked.length === 1
    let copyBtn = onlyRequisitionDetails
      ? tip(i18n.get('复制'), i18n.get('只允许从关联的申请事项中导入明细'))
      : copyCanUse && (
          <Button
            category="text"
            theme="highlight"
            size="small"
            className="other-action"
            onClick={() => {
              onCopyDetailClick(checked[0])
            }}
          >
            {i18n.get('复制')}
          </Button>
        )

    if (billType === 'expense' && autoExpenseWithBillStriction && dataSource.length) {
      return (
        <div id="area" className="action action5">
          <Checkbox
            className="checkbox"
            disabled={dataSource.length === 0}
            onChange={onCheckedAllClick}
            checked={isAllCheck}
          />
          <span className="checkall">{i18n.get('全选')}</span>
          {!isPermitForm && Apportion}
          <FullScreenComp isFullscreen={isFullscreen} onFullscreen={onFullscreen} />
        </div>
      )
    }

    if (isEBussCard && dataSource.length) {
      return (
        <div id="area" className="action action5">
          <Checkbox
            className="checkbox"
            disabled={dataSource.length === 0}
            onChange={onCheckedAllClick}
            checked={isAllCheck}
          />
          <span className="checkall">{i18n.get('全选')}</span>
          {!isPermitForm && !blackList.includes(billType) && Import}
          <span className="line" />
          {!isPermitForm && Apportion}
          {billState === 'new' && Remove}
        </div>
      )
    }

    const isStandard = api.getState()['@common'].powers?.Universal

    let RemoveToQuickExpends =
      checked.length > 0 ? (
        <Button
          category="text"
          theme="highlight"
          size="small"
          className="other-action"
          onClick={onRemoveToRecordExpendsClick}
        >
          {i18n.get('移至快速报销')}
        </Button>
      ) : null
    // (
    //   tip(i18n.get('移至快速报销'))
    // )
    if (!dataSource.length) return

    const handleConfirmDelete = () => {
      onBatchRemoveDetailClick()
      message.success('删除成功')
    }

    return (
      <div id="area" className="action action5">
        <div className="detail_action_left">
          <Checkbox
            className="checkbox"
            disabled={dataSource.length === 0}
            onChange={onCheckedAllClick}
            checked={isAllCheck}
            indeterminate={checked.length > 0 && checked.length < dataSource.length}
          >
            <span className="checkall">{i18n.get('全选')}</span>
          </Checkbox>
          <span className="line" />
          <Popconfirm
            title={i18n.get('确定删除选中的费用明细吗')}
            onConfirm={handleConfirmDelete}
            onCancel={() => {}}
            okText={i18n.get('删除')}
            okButtonProps={{ theme: 'danger' }}
            content={i18n.get('删除后将不可恢复')}
            cancelText={i18n.get('取消')}
          >
            {Remove}
          </Popconfirm>
          {Apportion}
          {!field?.isQuickExpends && !blackList.includes(billType) && !isStandard && RemoveToRecordExpends}
          {field?.isQuickExpends && !blackList.includes(billType) && !isStandard && RemoveToQuickExpends}
          {!field?.isQuickExpends && !blackList.includes(billType) && copyBtn}
        </div>
        <div className="detail_action_right">{countAmount()}</div>
      </div>
    )
  }

  let countAmount = () => {
    if (!isPermitForm && len > 0) {
      return (
        <div className={showDetailList ? 'total-money' : 'total-money fold-detail'}>
          <span className="mr-4">{i18n.get('合计总额')}</span>
          <div className="amount-wrapper ignore-translate">
            {allowSelectionReceivingCurrency ? (
              renderReceivingAmount('var(--eui-text-title)', 'var(--eui-num-head-b1)')
            ) : money.foreignStrCode ? (
              <>
                {renderAmount(true, 'var(--eui-text-title)', 'var(--eui-num-head-b1)')}
                <span style={{width: 8}} />
                {renderAmount(false, 'var(--eui-text-title)', 'var(--eui-num-head-b1)')}
              </>
            ) : (
              renderAmount(false, 'var(--eui-text-title)', 'var(--eui-num-head-b1)')
            )}

            {!isEdit && !showDetailList && (
              <div className="expansion-btn" onClick={handleChangeShowDetailList}>
                {i18n.get('展开明细')}
                <EKBIcon name="#EDico-arrow-more" />
              </div>
            )}
          </div>
        </div>
      )
    }
  }

  let renderTopAction = config => {
    let blackList = ['reconciliation', 'settlement']
    let apply = configs.find(v => v.ability === 'apply') || {}
    const isPermitDisable = isPermitForm && dataSource?.length > 0
    const shouldChangeBtnText = isHongShanTestingEnterprise(Fetch.ekbCorpId)
    const addDetailBtnText = shouldChangeBtnText ? i18n.get('直接添加') : i18n.get('添加')
    const importBtnText = shouldChangeBtnText ? i18n.get('发票导入') : i18n.get('导入')
    const onlyRequisitionDetails = apply.canApply && apply.onlyRequisitionDetails
    let Add = onlyRequisitionDetails ? (
      tip(i18n.get('添加'), i18n.get('只允许从关联的申请事项中导入明细'), undefined, <OutlinedTipsAdd />)
    ) : (
      <Button
        category="secondary"
        size="small"
        className="mr-8"
        disabled={isPermitDisable}
        onClick={!isPermitDisable && onAddDetailClick}
        loading={loadingStatus}
        data-testid="field-expenseDetail-add"
        icon={<OutlinedTipsAdd />}
      >
        {isPermitForm ? i18n.get('添加明细') : addDetailBtnText}
      </Button>
    )

    // 置灰场景：成本归属单中，允许使用【关联明细】导入时，费用明细不能大于一条
    const { allowAdd } = related.specificationConfig
    const disabledImportBtn = tagDataSource?.dataFromOrder?.showImportInPermit && allowAdd && dataSource?.length > 0

    let Import = onlyRequisitionDetails ? (
      tip(i18n.get('导入'), i18n.get('只允许从关联的申请事项中导入明细'), void 0, <OutlinedDirectionUpload />, { 'data-testid': 'field-expenseDetail-import' })
    ) : (
      <Button
        category="secondary"
        size="small"
        disabled={modify || disabledImportBtn}
        onClick={handleImportModal}
        loading={loadingStatus}
        icon={<OutlinedDirectionUpload />}
        data-testid="field-expenseDetail-import"
      >
        {importBtnText}
      </Button>
    )

    const handleImportQuickExpense = !isModify && !isPermitDisable ? onQuickExpendsClick : () => {}
    let QuickExpends = onlyRequisitionDetails ? (
      tip(i18n.get('通过快速报销导入', i18n.get('只允许从关联的申请事项中导入明细')))
    ) : (
      <Button
        category="secondary"
        size="middle"
        className="other-action ml-8"
        disabled={isPermitDisable || isModify}
        onClick={handleImportQuickExpense}
      >
        {i18n.get('通过快速报销导入')}
      </Button>
    )
    if (isPermitForm) {
      return (
        <>
          {Add}
          {allowAdd && Import}
        </>
      )
    }
    return (
      <>
        {field?.isQuickExpends && !blackList.includes(billType) && QuickExpends}
        {!field?.isQuickExpends && !blackList.includes(billType) && Add}
        {!field?.isQuickExpends && !blackList.includes(billType) && Import}
      </>
    )
  }

  const renderReceivingAmount = (color, valueSize) => {
    const { standardStrCode } = receivingAmount
    return (
      <div className="amount f-fd" style={{ fontSize: valueSize, color }}>
        <span className="text">{standardStrCode}</span>
        <Money value={receivingAmount} withoutStyle={true} showSymbol={false} showForeignNum={false} />
      </div>
    )
  }

  let renderAmount = (isShowForeign, color, font) => {
    const { foreignStrCode, standardStrCode } = money
    return (
      <div className="amount f-fd" style={{ font, color }}>
        <span className="text">
          {!isShowForeign && foreignStrCode && <span className="convert-into">{i18n.get('折合')}</span>}
          {isShowForeign ? foreignStrCode || standardStrCode : standardStrCode}
        </span>
        <Money
          value={money}
          withoutStyle={true}
          showSymbol={false}
          isShowForeign={isShowForeign}
          showForeignNum={false}
        />
      </div>
    )
  }

  const getFilterSortDown = () => {
    const items = sortTypeList.map((item) => {
      return {
        label: i18n.get(item.label),
        key: item.type,
        // type: item.children?.length > 0 ? 'group' : void 0,
        children: item.children?.map((child) => {
          return {
            label: i18n.get(child.label),
            key: child.type
          }
        })
      }
    })
    return <EUIDropdown menu={{items, selectedKeys: selectedSortMenu?.type ? [selectedSortMenu?.type] : [], onClick: onFilterSortMenu }} trigger='click'>
      <Button
        category='text'
        icon={<OutlinedDirectionSwitchVertical />}
        className={clx('show-type',
          {
            ['show-type--active']: selectedSortMenu?.type !== sortTypeList[0]?.type
          })
        }
      >
        {i18n.get('排序')}
      </Button>
    </EUIDropdown>
  }

  const typeMap = {
    expense: i18n.get('费用'),
    permit: i18n.get('费用'),
    requisition: i18n.get('申请'),
    reconciliation: i18n.get('对账'),
    settlement: i18n.get('结算'),
    receipt: i18n.get('费用'),
    corpPayment: i18n.get('付款'),
    reimbursement: i18n.get('报账')
  }

  const isNotRequired = () => {
    return field?.optional || !isEdit
  }

  const filterImageList = () => {
    for (let i = 0; i < showValue.length; i++) {
      if (!showValue[i].master) {
        newImagesList.push(showValue[i])
      } else {
        const res =
          imageList.length !== 0 &&
          imageList.find(imageItem => {
            return showValue[i].invoiceId === imageItem.id
          })
        newImagesList.push(res)
      }
    }
  }

  const handlePreview = async () => {
    const ifHasPdf = showValue.some(item => {
      return item.master
    })
    if (ifHasPdf) {
      const result = await api.invokeService('@bills:get:invoice:image:by:flowId', flowId)
      if (result.error) {
        return
      }
      imageList = result.items.reduce((preVal, curVal) => {
        let imageItem = curVal?.invoiceUrls?.length ? curVal.invoiceUrls : []
        return preVal.concat(imageItem)
      }, [])
    }
    filterImageList()
    const { bus } = toApportionCheck || {}
    const modifyApproveMoney = false
    const isFlowEditable = false
    const source = 'billDirectOpen'

    api.emit(
      '@layout::open:invoiceReviewer',
      showValue, // 所有可支持审阅的发票明细
      newImagesList, // 所有预览图片
      submitterId, // props
      isInvoiceManagePermissions, // props
      showValue, // showValue
      flowId, // props
      billState, // props
      bus, // props
      riskData, // 风险数据 props
      showValue, // 所有发票数据
      modifyApproveMoney,
      isFlowEditable, // 是否可以编辑
      dataSource, // 全部费用信息
      source
    )
  }

  const detailHeaderTitle =
    i18n.currentLocale === 'en-US'
      ? `${typeMap[billType]} ${i18n.get('明细')}s`
      : `${typeMap[billType]}${i18n.get('明细')}`

  const adaptiveActions = <div className={clx(styles['adaptive-actions'], {[styles['is-line-feed']]: isLineFeed})}>
    {!ifPriview && !isEdit && showValue.length !== 0 && (
      <Button
        className="show-type show-type-preview"
        style={{ display: 'inline-flex', alignItems: 'center' }}
        onClick={handlePreview}
        icon={<OutlinedDataColumnsetting />}
        category='text'
      >
        {i18n.get('审阅模式')}
      </Button>
    )}
    {!isEdit && !hideSortBtn && getFilterSortDown()}
    {groupPanel}
    <FullScreenComp label={i18n.get('全屏')} isFullscreen={isFullscreen} onFullscreen={onFullscreen} />
  </div>

  const cmp = (
    <div className={styles['header-wrapper']} id="details-header-container">
      <>
        <div className={clx('header', isEdit ? 'edit' : '', {'header-two-line': isLineFeed})} ref={headerTitleAndActionsRef}>
          <div className="title">
            {/* <div className="left-line" /> */}
            <span className="title-type">{billType ? detailHeaderTitle : i18n.get('费用明细')}</span>
            <span className="title-type">{`（${countText}）`}</span>
            {isNotRequired() ? null : (
              <span
                style={{
                  color: 'var(--eui-function-danger-500)',
                  height: '20px',
                  marginLeft: '4px',
                  marginRight: '4px'
                }}
              >
                *
              </span>
            )}
          </div>
          {!isPermitForm && (
            <>
              {!isLineFeed && adaptiveActions}
              {viewModePanel}
              {isLineFeed && adaptiveActions}
            </>
          )}
        </div>
        <div>
          {' '}
          {isEdit ? renderTopAction(configs) : <div className="dis-f ai-c jc-e">{countAmount()}</div>}
        </div>
      </>
      {isEdit && !isPermitForm && renderActions(configs)}
    </div>
  )

  useEffect(() => {
    const resizeHandle = () => {
      if (affixRef.current) {
        affixRef.current.updatePosition({})
      }
    }

    window.addEventListener('resize', resizeHandle)
    return () => {
      window.removeEventListener('resize', resizeHandle)
    }
  }, [affixRef.current])

  if (!dataSource) {
    return <div />
  }

  return isEnableAffix && dataSource?.length ? (
    <Affix
      target={() => document.getElementById('bill-info-editable-container')}
      className={`${isInModal ? 'affix-in-modal' : ''}`} // 在弹窗中修改定位位置
      style={{ width: '100%' }}
      ref={affixRef}
    >
      {cmp}
    </Affix>
  ) : (
    cmp
  )
}

function getSameApportionSpecificationId(checkeds = []) {
  const specificationIdList = checkeds.map(line => {
    const { specificationId = {} } = line
    const apportion = specificationId?.components?.find(line => line?.field === 'apportions') || {}
    return apportion?.specificationIds || []
  })
  if (specificationIdList.length) {
    const initialValue = specificationIdList[0]
    return specificationIdList.reduce((p, c) => {
      return p.filter(id => !!~c.indexOf(id))
    }, initialValue)
  }
  return []
}
