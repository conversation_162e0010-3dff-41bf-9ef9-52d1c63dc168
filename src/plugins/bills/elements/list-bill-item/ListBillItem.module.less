@import '~@ekuaibao/eui-styles/less/token.less';
@import '../../../../styles/app.less';

.list-bill-item-wrapper {
  cursor: pointer;
  background: var(--eui-bg-body);
  display: flex;
  flex-direction: column;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--eui-line-divider-default);
  margin-top: 8px;

  &.active {
    border-color: var(--eui-primary-pri-500);
  }

  &:hover {
    background-color: var(--eui-bg-body-overlay);
  }

  &:first-child {
    margin-top: 0;
  }

  &.list-bill-item-wrapper-cn {
    :global {
      .bill-info-item-label {
        flex: 0 0 32px !important;
      }
    }
  }

  :global {
    .bill-log-wrapper {
      display: flex;
      justify-content: space-between;
      line-height: 20px;

      .bill-item-log {
        display: flex;
        flex-wrap: nowrap;
        font: var(--eui-font-note-r2);
        flex-grow: 1;
        overflow: hidden;
        padding-right: 8px;

        .operator-span {
          margin-left: 2px;
          flex-shrink: 1;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .approve-status {
          margin-left: 2px;
          flex-shrink: 0;
        }
      }
    }

    .bill-title {
      // 超过3行显示...
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;

      .eui-tag {
        vertical-align: top;
      }

      .bill-title-text {
        color: var(--eui-text-title);
        font: var(--eui-font-body-b1);
      }
    }

    .bill-info {
      display: flex;
      flex-direction: column;
      font: var(--eui-font-note-r2);

      .bill-info-item {
        display: flex;
        margin-top: 4px;

        &:first-child {
          margin-top: 0;
        }

        .bill-info-item-label {
          flex: 0 0 56px;
          color: var(--eui-text-placeholder);
          white-space: nowrap;
        }

        .bill-info-item-value {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: var(--eui-text-title);
        }
      }
    }
  }
}


.ai-tag {
  border-radius: 4px;
  background: linear-gradient(95deg, rgba(158, 128, 255, 0.15) 1.51%, rgba(72, 220, 220, 0.15) 98.17%);
  margin-right: 8px;
  width: 18px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  display: inline-block;

  .ai-tag-text {
    background: linear-gradient(275deg, #7347FF 1.83%, #0080FF 98.49%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font: var(--eui-font-note-b1);
  }
}