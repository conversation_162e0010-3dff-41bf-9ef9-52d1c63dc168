/**
 *  Created by pw on 2019-01-29 13:50.
 */
import React, { PureComponent } from 'react'
import { Spin, Icon } from 'antd'
import MagnifierFollowMouse from '../../MagnifierFollowMouse'
import '../../../../../elements/InvoiceCard/exif'
import { Fetch } from '@ekuaibao/fetch'
import get from 'lodash/get'
import { getPreviewWPS } from '../../../bills.action'

interface StateInterface {
  regionUrl: string
  attachment: any
  pdfUrl: string
}

function getFileregionUrl(keys) {
  return Fetch.POST(
    '/api/v1/attachment/attachments/region',
    {},
    {
      body: keys
    }
  )
}

export default class OCREntityImageCard extends PureComponent<any, StateInterface> {
  constructor(props: any) {
    super(props)
    this.state = { regionUrl: '', attachment: {}, pdfUrl: '' }
  }

  componentDidMount() {
    const { keys } = this.props
    if ((keys?.key || '').toLowerCase().endsWith('.pdf')) {
      getPreviewWPS({ key: keys?.key }).then((result: any) => {
        if (result && result.value) {
          this.setState({
            pdfUrl: result.value.url
          })
        }
      })
    } else {
      getFileregionUrl({ ...keys, regions: keys?.regions || [] }).then((result: any) => {
        if (result && result.value) {
          const regionUrl = get(result, 'value.regionUrl')
          this.setState({
            regionUrl: regionUrl ? regionUrl : get(result, 'value.url'),
            attachment: result.value
          })
        }
      })
    }
  }

  renderIframe = () => {
    return <iframe title={i18n.get('在线预览PDF')} src={this.state.pdfUrl} width="100%" height="100%" frameBorder="0" />
  }

  render() {
    const { keys } = this.props
    const { regionUrl, attachment, pdfUrl } = this.state
    const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />
    const hasPdf = (keys?.key || '').toLowerCase().endsWith('.pdf')

    return (
      <div className="edit-content-left">
        {hasPdf && pdfUrl && this.renderIframe()}
        {!regionUrl && !hasPdf && <Spin indicator={antIcon} tip={i18n.get('加载中...')} />}
        {regionUrl && !hasPdf && (
          <MagnifierFollowMouse
            thumbnailSrc={regionUrl}
            amplifySrc={regionUrl}
            attachment={attachment}
            multiple={1.5}
          />
        )}
      </div>
    )
  }
}
