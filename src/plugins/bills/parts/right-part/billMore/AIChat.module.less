.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

}

.ai-chat-status {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;

  .status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;

    &.connected {
      background: #10b981;
    }

    &.disconnected {
      background: #ef4444;
      animation: pulse 1.5s infinite;
    }
  }

  .status-text {
    flex: 1;
    color: #666;
  }

  .debug-controls {
    display: flex;
    gap: 8px;

    button {
      padding: 4px 8px;
      font-size: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: white;
      cursor: pointer;

      &:hover {
        background: #f5f5f5;
      }
    }
  }
}

.ai-chat-iframe-container {
  flex: 1;
  position: relative;
  transition: height 0.3s ease;
  background: linear-gradient(180deg, #F6FAFF 0%, #FFF 10.71%, #FFF 80.27%, #E6F1FF 96.98%);
}

.ai-chat-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.ai-chat-loading {
  background: linear-gradient(180deg, #F6FAFF 0%, #FFF 10.71%, #FFF 80.27%, #E6F1FF 96.98%);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 20px 16px;
  gap: 32px;
  width: 100%;
  height: 100%;

  &-left {
    width: 90%;
  }

  &-right {
    display: flex;
    justify-content: flex-end;

    :global{
      .eui-skeleton-paragraph {
        width: 90%;
      }
    }
  }
}
.ai-chat-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  border-radius: 8px;
  color: var(--eui-text-placeholder);
  font: var(--eui-font-body-r1);
  background: linear-gradient(180deg, #F6FAFF 0%, #FFF 10.71%, #FFF 80.27%, #E6F1FF 96.98%);
}

.loading-spinner {
  color: #666;
  font-size: 16px;
}

.error-message {
  color: #dc2626;
  margin-bottom: 16px;
  text-align: center;
}

.retry-button {
  padding: 8px 16px;
  background: #2555ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: #1e4ecc;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}


.createFlowModalContent {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 480px;
  overflow-y: auto;

  :global {
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
    }

    li {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      color: var(--eui-text-title, rgba(29, 33, 41, 0.90));
      font: var(--eui-font-body-r1);

      &::before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: var(--eui-decorative-neu-300, #C9CDD4);
      }

      &+li {
        margin-top: 4px;
      }
    }
  }
}