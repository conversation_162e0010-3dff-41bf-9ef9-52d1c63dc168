import { FlowAction } from "../../layers/bill-info-popup/FlowAction";

const actionsOfTransferingState: string[] = [
  FlowAction.Reject,
  FlowAction.Agree,
  FlowAction.StartSignature,
  FlowAction.ShiftNode,
  FlowAction.ShiftApprove,
  FlowAction.AddNode,
  FlowAction.AddSignNode,
  FlowAction.AdminShiftNode,
  FlowAction.Receive,
  FlowAction.ReceiveException,
  FlowAction.Send,
  FlowAction.AddExpress,
  FlowAction.CancelReceiveException,
  FlowAction.Pay,
  FlowAction.Withdraw,
  FlowAction.SkipSend,
  FlowAction.Back,
  FlowAction.Retract,
  FlowAction.AdminBack,
  FlowAction.AdminAddNode,
  FlowAction.MarkedRead,
  FlowAction.Submit,
  FlowAction.Nullify,
  FlowAction.Alter,
  FlowAction.AdminSkipNode,
  FlowAction.Delete,
]

/**
 * 判断是否是转移状态的action
 * @param {string} action 动作名称
 * @param {ArgsType<typeof Array.prototype.filter>[0]} filterPredicate 过滤函数
 * @returns {boolean} 是否是转移状态的action
 */
export const isActionOfTransferingState = (action: string, filterPredicate?: ArgsType<typeof Array.prototype.filter>[0]) => {
  const _actions = filterPredicate ? actionsOfTransferingState.filter(filterPredicate) : actionsOfTransferingState
  return _actions.includes(action)
}

export const isActionOfNotNeedRefresh = (actionName: string) => {
  const ACTIONS_OF_NEED_NOT_REFRESH: string[] = [
    FlowAction.Copy,
    FlowAction.Print,
    FlowAction.PrintInvoice,
    FlowAction.Suspend,
    FlowAction.Urge,
    FlowAction.ExportAttachment,
    FlowAction.Share,
    FlowAction.RemindAddingInvoice,
    FlowAction.Nullify,
    FlowAction.Delete,
  ]
  return ACTIONS_OF_NEED_NOT_REFRESH.includes(actionName)
}

export const isMofiyAction = (action: string) => {
  return action === FlowAction.Modify
}