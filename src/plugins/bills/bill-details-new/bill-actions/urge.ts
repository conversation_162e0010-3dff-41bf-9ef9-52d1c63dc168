import { BillActionBase, BillActionBaseProps } from './base-action'
import { app } from '@ekuaibao/whispered'
import { action } from './constant'
import { get } from 'lodash'
import { message as EUIMessage, message, Modal } from "@hose/eui";
import { getStaffName } from '../../../../elements/utilFn';

export class UrgeAction extends BillActionBase {
  label = i18n.get('催办')
  lastTime = 0

  onAction = () => {
    this.makeSureToUrge()
  }

  makeSureToUrge = () => {
    const { backlog } = this.props
    const title = get(backlog, 'form.title') || ''
    Modal.confirm({
      className: 'confirmCopyModal-wrapper',
      title: i18n.get('发送催办消息'),
      content: i18n.get(
        `系统将发送一条消息提醒 {__k0} 审批。不建议频繁使用此功能催促审批人。若长时间没有审批，建议通过电话等其他联系方式联系审批人。`,
        { __k0: this.getApproveMember() }
      ),
      okText: i18n.get('确认'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        this._throttleUrge()
      }
    })
  }

  getApproveMember = () => {
    const { taskId, nodes } = this.props.backlog.flowId.plan
    const currentNode = nodes.find(node => node.id === taskId)
    if (currentNode.type === 'countersign') {
      const approvingSigners = currentNode.counterSigners
        .filter(item => item.state === 'APPROVING' || item.state === null)
        .map(item => getStaffName(item.approverId))
      return i18n.get(`{__k0}等{__k1}人`, {
        __k0: approvingSigners.slice(0, 10).join(),
        __k1: approvingSigners.length
      })
    } else if (currentNode.type === 'ebot') {
      return 'Ebot'
    } else if (currentNode.type === 'invoicingApplication') {
      return i18n.get('开票申请')
    } else {
      return currentNode.approverId ? getStaffName(currentNode.approverId) : i18n.get('未选择')
    }
  }

  _throttleUrge = () => {
    let newTime = new Date().valueOf()
    if (newTime - this.lastTime > 60000) {
      //60秒内只能执行一次催办功能
      this.handleUrgeService()
      this.lastTime = newTime
    } else {
      message.warning(i18n.get('操作频繁'))
    }
  }

  handleUrgeService = () => {
    const { backlog } = this.props
    const { flowId: flow } = backlog
    const flowId = get(flow, 'id')
    const taskId = get(flow, 'plan.taskId')
    return app.invokeService('@bills:bill:reminde', flowId, taskId)
  }
}