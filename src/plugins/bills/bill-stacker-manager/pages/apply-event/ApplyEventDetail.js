/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/13.
 */
import styles from './ApplyEventDetail.module.less'
import React, { PureComponent } from 'react'
import ApplyCard from './element/ApplyCard'
import ApplyList from './element/ApplyList'
import {
  changeStateApplyEvent,
  getFlowInfoByIdNew,
  getApplyEvent,
  transferApply,
  sharedApply,
  getFlowInfoById,
  getApplyEventById,
  getDetailList,
  checkTripPlanningHasTravel,
  syncTravel,
  getSurplusConfirmList,
  getTravelConfirmList,
  getSubsidyConfig
} from '../../../bills.action'
import { getRequitistionEvent } from '../../../util/billUtils'
import { EnhanceConnect } from '@ekuaibao/store'
import { handleBillDetailClickByIdNew, handleBillDetailClickById } from '../../util'
import { app as api } from '@ekuaibao/whispered'
import { toJS } from 'mobx'
import { getDateTime, getDefaultSubsidyItem, parseSubsidyData } from '../../../layers/subsidy-drawer/utils'
import { showMessage } from '@ekuaibao/show-util'
import { uuid, visit } from '@ekuaibao/helpers'
import { fnIsRiskError } from '../../../riskWarning/formatRiskWarningData'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { getRequisitionShouldShowConsumeBalance } from './ApplyEventFetchUtils'
import {
  getPlatFormId,
  getOrderByRequisitionId,
  getEntityDetailsList,
} from './ApplyEventFetchUtils'
import { uniq, cloneDeep } from 'lodash'

const expressTrack = (key, options) => {
  const staff = api.getState()['@common'].userinfo?.staff || {}
  window.TRACK &&
    window.TRACK(key, {
      source: window.__PLANTFORM__,
      userId: staff.userId,
      corpId: staff.corporation?.id || staff.corporation,
      corpName: staff.corporation?.name,
      platForm: 'Web',
      ...options
    })
}
@EnhanceConnect(
  state => {
    return {
      SubsidyManage: state['@common'].powers.SubsidyManage,
      isLinkRequisition: state['@bills'].isLinkRequisition,
      powerCodeMap: state['@common'].powers.powerCodeMap,
      applyList: state['@bills'].applyList || [],
      applyListClosed: state['@bills'].applyListClosed || [],
      baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
    }
  },
  {
    changeStateApplyEvent,
    getFlowInfoByIdNew,
    getFlowInfoById,
    getApplyEvent,
    transferApply,
    sharedApply,
    getApplyEventById,
    getDetailList,
    getSubsidyConfig
  }
)
export default class ApplyEventDetail extends PureComponent {
  constructor(props) {
    super(props)
    let { detail = {}, SubsidyManage, keel } = props
    if (keel) {
      const applyList = props.applyList || []
      const applyListClosed = props.applyListClosed || []
      const applyItem = applyList.concat(applyListClosed).find(el => el.id === detail?.id)
      if (applyItem) {
        detail = applyItem
      } else if (detail) {//数据是mbox 在流转时configs 数组丢失了，此处用tojs 做了处理
        detail = toJS(detail)
      }
    }
    this.state = {
      detail: detail,
      requisitionId: detail.id || '',
      listData: {},
      budgetInfoList: [],
      detailList: [],
      loading: false,
      applyCardInitLoading: true,
      subsidyState: SubsidyManage,
      travelTabHasShow: false,
      btnLoading: false,
      showConsumeBalance: false,
      subsidyConfig: []
    }
  }

  componentDidMount() {
    const { getDetailList } = this.props
    this.fnGetOrder()
    this.getRequisitionVersion()
    getRequitistionEvent(this.state.requisitionId).then(result => {
      let upStateData = {}
      if (result.items) {
        upStateData = { budgetInfoList: result.items, inBudget: result.inBudget }
      }
      upStateData = Object.assign(upStateData, { loading: true })
      this.setState(upStateData)
    })
    getDetailList(this.state.requisitionId).then(result => {
      let upStateData = {}
      if (result.payload.items) {
        upStateData = { detailList: result.payload.items }
      }
      upStateData = Object.assign(upStateData, { loading: true })
      this.setState(upStateData)
    })
    checkTripPlanningHasTravel({ id: this.state.requisitionId }).then(res => {
      const { value } = res
      this.setState({ travelTabHasShow: value })
    })
    // 同步行程到第三方平台
    syncTravel({ id: this.state.requisitionId })
    this.fetchSubsidyConfig()
    this.getIsLinkRequisition()
  }

  fnSetTravelPersonName = async (orderListDataResult) => {
    const { orderPrefix, data } = orderListDataResult?.value || {}
    if (data?.length && orderPrefix) {
      let staffIdArr = []
      const staffsNameMap = {}
      orderListDataResult.value.data.forEach(el => {
        if (el?.form) {
          const travelPerson = el.form[`${orderPrefix}出行人`]
          if (travelPerson?.length) {
            staffIdArr = staffIdArr.concat(travelPerson)
          }
        }
      })
      const staffIds = uniq(staffIdArr)
      const staffsArr = await api.invokeService('@bills:get:staffs:by:ids', { ids: staffIds })
        .catch(err => console.log('====== err', err))
      if (staffsArr?.items?.length) {
        staffsArr.items.forEach(el => {
          staffsNameMap[el?.id] = el?.name
        })
      }
      orderListDataResult.value.data.forEach(el => {
        if (el?.form) {
          const staffNameArr = []
          const travelPerson = el.form[`${orderPrefix}出行人`]
          if (travelPerson?.length) {
            travelPerson.forEach(staffId => {
              if (staffsNameMap[staffId]) {
                staffNameArr.push(staffsNameMap[staffId])
              }
            })
            el.travelPersonName = staffNameArr.join('，')
          }
        }
      })
    }
  }

  fnGetOrder = async () => {
    const { detail } = this.props
    const platformInfo = await getPlatFormId()
      .catch(err => console.log('====== err', err))
    // 未开通行程管理时，不需要拉取订单
    if (!platformInfo?.id) return

    // 获取订单
    const allEntityInfo = await getEntityDetailsList({ id: platformInfo.id })
      .catch(err => console.log('====== err', err))
    const orderEntity = allEntityInfo?.items?.find(el => el.type === 'ORDER')
    if (!orderEntity) return
    const orderEntityMap = {}
    orderEntity?.children?.forEach(el => {
      orderEntityMap[el.id] = el.type
    })
    const orderListDataResult = await getOrderByRequisitionId(detail?.id)
      .catch(err => console.log('====== err', err))

    // 处理订单中的出行人字段
    await this.fnSetTravelPersonName(orderListDataResult)

    this.setState({
      orderListData: orderListDataResult?.value,
      orderEntityMap,
    })
  }

  getRequisitionVersion = () => {
    getRequisitionShouldShowConsumeBalance(this.state.requisitionId)
      .then(res => {
        this.setState({
          showConsumeBalance: res?.value,
          applyCardInitLoading: false
        })
      })
      .catch(e => {
        this.setState({ applyCardInitLoading: false })
        console.log('====== error:', e)
      })
  }

  getIsLinkRequisition = async () => {
    const { isLinkRequisition } = this.props
    isLinkRequisition === null && (await api.invokeService('@bills:get:link:requisition:coprs'))
  }

  fetchSubsidyConfig = () => {
    const { SubsidyManage } = this.props
    if (SubsidyManage) {
      getSubsidyConfig().then(res => {
        const subsidyConfig = res.items || []
        this.setState({ subsidyConfig })
      })
    }
  }

  /**
   * @description 手动创建补助单
   */
  handleCreateBill = () => {
    let { detail, detailList } = this.state
    api.emit('ApplyClick', { requisition: detail, billType: 'expense', detailList })
  }

  handleCreateApply = () => {
    let { detail, detailList } = this.state
    const { isLinkRequisition } = this.props
    const linkRequisitionInfo = detail?.specificationId?.configs?.find(item => item?.linkRequisitionInfo)
    if (isLinkRequisition && !linkRequisitionInfo) {
      showMessage.warning('根据该申请事项对应的单据模板配置，不允许进行补充申请！')
      return
    }
    api.emit('ApplyClick', { requisition: detail, billType: 'requisition', detailList })
  }
  handleChangeApply = async (status, type) => {
    let { changeStateApplyEvent, privilegeId = '' } = this.props
    let { detail } = this.state
    let action = detail.state === 'PROCESS' ? 'close' : 'open'
    let checkClose = false
    if (action === 'close') {
      const checkData = await api.invokeService('@bills:checkApplyClosed', detail.id)
      checkClose = checkData.value
    }
    return Promise.resolve()
      .then(async _ => {
        if (action === 'close') {
          if (checkClose) {
            await api.open('@bills:RequisitionEventCloseModal', {
              requisitionMoney: detail.requisitionMoney,
              id: detail.id,
              api
            })
          } else {
            showMessage.warning(i18n.get('申请事项正在报销中，不可关闭'))
            return Promise.reject()
          }
        }
      })
      .then(_ => changeStateApplyEvent({ action, id: detail.id, type, privilegeId }))
      .then(action => {
        if (action.error) return
        this.fnUpdateList(status)
      })
  }

  getSubsidyTravelConfig = () => {
    const { subsidyConfig } = this.state
    return subsidyConfig.find(item => item.subsidyType === 'TRAVEL') || {}
  }

  handleCreateSubsidy = async (travelSubsidyActive, travelSubsidyCheck, surplusSubsidyActive, surplusSubsidyCheck) => {
    expressTrack('apply_subsidy_click', { actionName: '点击【申请补助】' })
    if (travelSubsidyActive && surplusSubsidyActive) {
      await api.open('@bills:SelectSubsidyModal', {
        fnCreateTravelSubsidy: this.fnCreateTravelSubsidy,
        fnCreateSurplusSubsidy: this.fnCreateSurplusSubsidy,
        expressTrack,
        travelSubsidyCheck,
        surplusSubsidyCheck
      })
    }
    if (travelSubsidyCheck) {
      this.fnCreateTravelSubsidy()
    }
    if (surplusSubsidyCheck) {
      this.fnCreateSurplusSubsidy()
    }
  }

  fnGetDefaultTravelConfirmData = (response, confirmCity) => {
    if (response.confirmFields?.length) {
      return getDefaultSubsidyItem(response.confirmFields, confirmCity)
    }
    return getDefaultSubsidyItem([], confirmCity)
  }

  fnCreateTravelSubsidy = async () => {
    this.setState({ btnLoading: true })
    const requisitionId = this.state.detail?.id
    const { canModifyDate, confirmCity } = this.getSubsidyTravelConfig()
    const result = await getTravelConfirmList([requisitionId]).catch(e => {
      this.setState({ btnLoading: false })
      showMessage.warning(e.message || e.msg)
    })
    this.setState({ btnLoading: false })
    if (result?.value?.requisitionIds) {
      const travelConfirmList = result.value
      const { startTime, endTime, data, confirmFields = [] } = travelConfirmList
      let checkDate = true
      const now = getDateTime()
      if (!startTime) {
        travelConfirmList.startTime = now
      }
      if (!endTime) {
        travelConfirmList.endTime = now
      }
      if (!data.length) {
        const items = this.fnGetDefaultTravelConfirmData(result.value, confirmCity)
        travelConfirmList.data = [{ date: now, items }]
        checkDate = false
      }
      if (data.length) {
        travelConfirmList.data = data.map(dataItem => {
          dataItem.items = parseSubsidyData(dataItem.items)
          return dataItem
        })
      }
      api
        .open('@bills:SubsidyTravelDrawer', {
          travelConfirmList,
          requisitionIds: [requisitionId],
          canModifyDate,
          confirmCity,
          checkDate,
          title: i18n.get('行程确认'),
          expressTrack
        })
        .then(res => {
          if (res.value) {
            api.store.dispatch('@layout5/activeMenu')('myBill')
          }
        })
    }
  }

  getDefaultDimensionItems = async (dimensionName, tripTypeName) => {
    const ids = []
    const { detail } = this.state
    let dimensionIds = detail?.flowId?.form[tripTypeName]
    if (!dimensionIds) {
      return []
    }
    dimensionIds = !Array.isArray(dimensionIds) ? [dimensionIds] : dimensionIds
    const value = await api.invokeService('@common:get:staff:dimension', { name: dimensionName })
    const dimensionList = value?.items || []
    const dmMap = {}
    visit(dimensionList, node => {
      dmMap[node.id] = node
    })
    dimensionIds.forEach(item => {
      if (!dmMap[item]?.children?.length) {
        ids.push(item)
      }
    })
    return ids
  }

  fnCreateSurplusSubsidy = async () => {
    this.setState({ btnLoading: true })
    const requisitionId = this.state.detail?.id
    const result = await getSurplusConfirmList(requisitionId).catch(e => {
      this.setState({ btnLoading: false })
      showMessage.warning(e.message || e.msg)
    })
    this.setState({ btnLoading: false })
    if (result?.value?.requisitionId) {
      const surplusConfirmList = result.value
      const { startTime, endTime, data, tripTypeName } = surplusConfirmList
      const dimensionName = this.props.baseDataPropertiesMap[tripTypeName]?.dataType?.entity
      const dimensionItems = await this.getDefaultDimensionItems(dimensionName, tripTypeName)
      let checkDate = true
      const now = getDateTime()
      if (!startTime) {
        surplusConfirmList.startTime = now
      }
      if (!endTime) {
        surplusConfirmList.endTime = now
      }
      if (!data.length) {
        surplusConfirmList.data = []
        checkDate = false
      }
      api
        .open('@bills:SubsidyDrawer', {
          surplusConfirmList,
          requisitionId,
          title: i18n.get('行程确认'),
          checkDate,
          expressTrack,
          dimensionName,
          dimensionItems
        })
        .then(res => {
          if (res.value) {
            api.store.dispatch('@layout5/activeMenu')('myBill')
          }
        })
    }
  }

  fnUpdateList(status) {
    let { detail } = this.state
    let { getApplyEventById } = this.props
    api.invokeService('@layout5:refresh:menu:data')
    getApplyEventById(detail.id).then(action => {
      this.setState({ detail: action.payload.value })
    })
    let { getApplyEvent, fromApplyManage, bus } = this.props
    if (!fromApplyManage) {
      getApplyEvent(status)
    } else {
      bus.clearSelectedRowKeys()
      bus && bus.reload()
    }
  }

  handleShare = () => {
    let { detail } = this.state
    let { sharedApply, privilegeId = '', fromApplyManage } = this.props
    api
      .open('@bills:ApplyShareModal', { detail, isVisibilityStaffs: !fromApplyManage })
      .then(({ sharedOwnerIds: data = [], userRemark = '' }) => {
        if (data && data.length > 150) {
          showMessage.error(i18n.get('同时共享人数不能超过150人'))
          return
        }

        sharedApply(detail.id, data, privilegeId, userRemark).then(action => {
          if (action.error) return
          action?.payload?.value?.forbinddenNum && this.batchResultOptions(action?.payload?.value)
          this.fnUpdateList(detail.state)
        })
      })
  }

  handleTransfer = () => {
    let { detail } = this.state
    let { transferApply, privilegeId = '', fromApplyManage } = this.props
    api
      .open('@bills:ApplyTransferModal', { detail, isVisibilityStaffs: !fromApplyManage })
      .then(({ staffId: data, userRemark }) => {
        transferApply(detail.id, data, userRemark, privilegeId).then(action => {
          if (action.error) return
          this.fnUpdateList('PROCESS')
        })
      })
  }

  handleOpenBillInfoEdit = async (id, formType, state, code) => {

    if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: id,
        flows: [],
        scene: 'OWNER',
        showUpDown: false,
        params: { autoClose: true, isNewSearchInfo: true },
        from: 'from_myBill',
        callBack: () => this.refreshRelatedBillInfo(id),
      })
      return
    }

    // 获取单据的可展示的actions, 此处与我的单据列表模式存在区别，为审批中的单据提供了同意按钮
    const res = await api.invokeService('@bills:get:flow-action', { code })
    const { value: { buttons } } = res

    //  获取单据的风险提示
    let isForbid = state === 'rejected' || state === 'draft'
    const level = isForbid ? 'OutOfLimitReject' : ''
    const riskTip = await api
      .invokeService('@bills:get:flow:risk:warning', level ? { id, level } : { id })
    isForbid = isForbid && fnIsRiskError(riskTip?.value?.riskWarning)
    riskTip.isForbid = isForbid
    //  获取单据信息；line中没有plan字段
    const resp = await api.invokeService('@bills:get:flow-info', { id })
    const { value: flowId } = resp

    //  获取标题
    const title = isForbid
      ? i18n.get('编辑单据')
      : `${i18n.get(billTypeMap()[formType])}${i18n.get('详情')}`

    //  打开弹窗
    // @ts-ignore
    api.open('@bills:BillInfoEditePopup', {
      title,
      buttons,
      riskTip,
      backlog: { id: -1, flowId },
      invokeService: '@bills:get:flow-info',
      params: { id: flowId.id, autoClose: true, isNewSearchInfo: true },
      callBack: () => this.refreshRelatedBillInfo(id),
      from: 'from_myBill'
    })
  }

  refreshRelatedBillInfo = async () => {
    let { getApplyEventById } = this.props
    const { detail } = this.state
    setTimeout(() => {
      api.invokeService('@layout5:refresh:menu:data')
      getApplyEventById(detail.id).then(action => {
        this.setState({ detail: action.payload.value })
        api.invokeService('@bills:put:apply-event-detail-list', action.payload.value)

      })
    }, 900)
  }

  handleShowDetailExpense = (id, type, state, code) => {
    let { flowId, privilegeId, showAllFeeType, canEditDraft } = this.props
    const params = { id, flowId, privilegeId, showAllFeeType, fromPageV2: 'myRequisition' }
    if (canEditDraft && (state === 'rejected' || state === 'draft')) {
      this.handleOpenBillInfoEdit(id, type, state, code)
      return
    }
    if (type === 'requisition') {
      handleBillDetailClickByIdNew.call(this, params)
    } else {
      handleBillDetailClickById.call(this, params)
    }
  }
  handleShowFeeDetailView = (bill, index) => {
    let { getFlowInfoByIdNew, stackerManager, keel, flowId } = this.props
    getFlowInfoByIdNew({ id: bill.id, flowId }).then(res => {
      let {
        form: { details }
      } = res.payload.value
      let { specificationId = {}, submitterId } = res.payload.value.form
      if (keel) {
        keel.open('FeeDetailView', {
          dataSource: { ...details[index], showAllFeeType: true },
          billSpecification: specificationId,
          details: details,
          isEdit: false,
          submitterId
        })
      } else if (stackerManager) {
        stackerManager.push('FeeDetailView', {
          dataSource: details[index],
          billSpecification: specificationId,
          details: details,
          isEdit: false,
          submitterId
        })
      }
    })
  }

  batchResultOptions(value) {
    const { errorMsg } = value
    api.open('@audit:ApprovalCompleteModal', {
      label: (
        <div className="batch-show-title" style={{ textAlign: 'center' }}>
          <div
            className="title"
            style={{
              height: '24px',
              fontSize: '16px',
              fontWeight: 600,
              lineHeight: '24px',
              color: '#1d2b3d'
            }}
          >
            {i18n.get(`共享完成`)}
          </div>
        </div>
      ),
      details: (
        <div className="batch-show-agree-modal">
          <div className="batch-item color-gray">
            <span className="mark" />
            &nbsp;&nbsp;
            <span>{errorMsg?.msg}</span>
            &nbsp;&nbsp;
            <span>{errorMsg?.title}</span>
          </div>
        </div>
      )
    })
  }

  render() {
    const {
      showButton,
      fromApplyManage,
      privilegeId,
      powerCodeMap,
    } = this.props
    const {
      detail,
      budgetInfoList,
      inBudget,
      loading,
      detailList,
      travelTabHasShow,
      subsidyState,
      showConsumeBalance,
      applyCardInitLoading,
      btnLoading,
      orderListData,
      orderEntityMap,
    } = this.state
    const { related = [], orderRelated = [] } = detail
    const associatedOrder = powerCodeMap?.includes('170003') ? true : false
    // 计算已订购金额
    let orderRelatedAmount = 0
    orderRelated?.map(item => {
      orderRelatedAmount += Number(item?.orderAmount?.standard)
    })

    const applyListNode = loading ? (
      <ApplyList
        travelTabHasShow={travelTabHasShow}
        dataSource={detail}
        related={related}
        associatedOrder={associatedOrder}
        orderRelated={orderRelated}
        budgetInfoList={budgetInfoList}
        inBudget={inBudget}
        detailList={detailList}
        handleFeeDetail={this.handleShowFeeDetailView}
        handleShowDetail={this.handleShowDetailExpense}
        standardSymbol={detail?.requisitionMoneyNode?.standardSymbol}
        standardStrCode={detail?.requisitionMoneyNode?.standardStrCode}
        orderListData={orderListData}
        orderEntityMap={orderEntityMap}
      />
    ) : null
    return (
      <div className={styles['event-detail']}>
        <div id="ApplyEventDetail">
          {applyCardInitLoading
            ? null
            : <ApplyCard
              privilegeId={privilegeId}
              fromApplyManage={fromApplyManage}
              dataSource={detail}
              associatedOrder={associatedOrder}
              orderRelatedAmount={orderRelatedAmount}
              showButton={showButton}
              onCreateApply={this.handleCreateApply}
              onCreateBill={this.handleCreateBill}
              onShare={this.handleShare}
              onTransfer={this.handleTransfer}
              onChangeApply={this.handleChangeApply}
              onCreateSubsidy={this.handleCreateSubsidy}
              subsidyState={subsidyState}
              btnLoading={btnLoading}
              showConsumeBalance={showConsumeBalance}
            />}
          {applyListNode}
        </div>
      </div>
    )
  }
}
