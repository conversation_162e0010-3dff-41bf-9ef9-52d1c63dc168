export enum FlowAction {
  Save = 'freeflow.edit', // 保存草稿
  Submit = 'freeflow.submit', // 提交送审
  Comment = 'freeflow.comment', // 评论
  /**
   * 审批人撤回审批
  */
  Withdraw = 'freeflow.withdraw', // 撤回审批
  // 审批
  Agree = 'freeflow.agree', // 审批同意
  Reject = 'freeflow.reject', // 驳回
  // 支付
  Pay = 'freeflow.pay', // 支付
  // 打印
  Print = 'freeflow.print', // 打印单据/附件
  PrintInvoice = 'freeflow.printInvoice', // 打印单据和发票
  PrintDoc = 'freeflow.printDoc', // 打印面单
  Printed = 'freeflow.printed', // 已打印
  PrintRemind = 'freeflow.remind', // 打印提醒
  // 签署
  StartSignature = 'freeflow.start.signature', // 发起签署
  ViewSignature = 'freeflow.view.signature', // 查看签署
  Activate = 'freeflow.activate', // 激活
  // 寄送
  Send = 'freeflow.send', // 添加寄送
  SkipSend = 'freeflow.skip.send', // 跳过寄送
  // 收单
  Receive = 'freeflow.receive', // 收单
  ReceiveException = 'freeflow.receiveExcep', // 收单异常
  CancelReceiveException = 'freeflow.cancelReceiveExcep', // 取消收单异常

  Nullify = 'freeflow.nullify', // 作废
  Modify = 'freeflow.editApproving', // 修改

  ShiftNode = 'freeflow.shiftnode', // 转交审批
  AddNode = 'freeflow.addnode', // 加签审批
  AddSignNode = 'freeflow.addSignNode', // 加签审批
  AdminShiftNode = 'freeflow.adminShiftnode',// 管理员转交
  Suspend = 'freeflow.suspend', // 暂挂审批
  /**
   * 管理员的撤销支付
  */
  Back = 'freeflow.back',
  AddExpress = 'freeflow.addExpress', // 添加寄送信息
  JumpExpress = 'freeflow.jumpExpress', // 跳转寄送信息
  ShiftApprove = 'freeflow.shiftApprove', // 转交审批
  Urge = 'freeflow.urge', // 催办
  MarkedRead = 'freeflow.read', // 标为已读
  AdminSkipNode = 'freeflow.admin.skipnode', // 管理员跳过
  Alter = 'freeflow.alter', // 变更
  Copy = 'freeflow.copy', // 复制
  /**
   * 提单人撤回
  */
  Retract = 'freeflow.retract',
  Cancel = 'freeflow.cancel', // 取消
  Delete = 'freeflow.delete', // 删除
  ExportAttachment = 'freeflow.export', // 导出附件
  Share = 'freeflow.share',
  /**
   * 回退 小权限管理员的回退
  */
  AdminBack = 'freeflow.admin.back',
  AdminAddNode = 'freeflow.admin.addnode',
  RemindAddingInvoice = 'INVOICE_REMIND', // 提醒补充发票
}
