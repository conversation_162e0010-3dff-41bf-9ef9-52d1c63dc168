/**
 *  Created by gym on 2020-07-22 15:35.
 */
import React, { Component } from 'react'
import { Switch } from '@hose/eui'
import SignaturePanel from './SignaturePanel'
import styles from './ApproveSignature.module.less'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
import { EnhanceConnect } from '@ekuaibao/store'

interface Props {
  mustBeUsedSignature: boolean
  bus: MessageCenter
}
interface State {
  isShowAutograph: boolean
  isSetSignature: boolean
}

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data
}))
export default class ApproveSignature extends Component<Props, State> {
  constructor(props) {
    super(props)
    this.state = {
      isSetSignature: false,
      isShowAutograph: props.mustBeUsedSignature || get(props.userInfo, 'staff.autograph.lastHabit')
    }
  }

  componentWillReceiveProps(nextProps: Readonly<Props>, nextContext: any): void {
    if (this.props.mustBeUsedSignature !== nextProps.mustBeUsedSignature) {
      this.setState({
        isShowAutograph: nextProps.mustBeUsedSignature
      })
    }
  }

  componentDidMount() {
    const { bus } = this.props
    bus.watch('get:approve:signature:result', this.handleResult)
    bus.watch('get:approve:show:signature', this.handleIsShowSignature)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('get:approve:signature:result', this.handleResult)
    bus.un('get:approve:show:signature', this.handleIsShowSignature)
  }

  handleResult = () => {
    const { isShowAutograph, isSetSignature } = this.state
    const userInfo = api.getState('@common').userinfo.data
    const autograph = get(userInfo, 'staff.autograph.use')
    const isAutograph = autograph ? autograph : isSetSignature
    if (isShowAutograph && !isAutograph) {
      showMessage.warning(i18n.get('您还没有签名或还没有保存签名，请签名或保存'))
      return false
    }
    return true
  }

  handleIsShowSignature = () => {
    const { isShowAutograph } = this.state
    return isShowAutograph
  }

  handleSwitchChange = async (showAutograph: boolean) => {
    this.setState({
      isShowAutograph: showAutograph
    })
    const meInfo = api.getState('@common').userinfo.data
    const autographId = get(meInfo, 'staff.autograph.id')
    const key = get(meInfo, 'staff.autograph.key')
    const use = get(meInfo, 'staff.autograph.use')
    if (autographId && key) {
      await api.invokeService('@audit:update:signature', {
        id: autographId,
        imageId: key,
        lastHabit: showAutograph,
        use
      })
      await api.dataLoader('@common.userinfo').reload()
    }
  }

  handleSignatureChange = isSetSignature => {
    this.setState({ isSetSignature })
  }

  render() {
    const { mustBeUsedSignature } = this.props
    const { isShowAutograph } = this.state
    return (
      <div className={styles['signature_box']}>
        <div className="signature_header">
          <div className="signature_title">{i18n.get('影像签名')}</div>
          <Switch checked={isShowAutograph} onChange={this.handleSwitchChange} disabled={mustBeUsedSignature} />
        </div>
        {isShowAutograph ? <SignaturePanel onSignatureChange={this.handleSignatureChange} /> : null}
      </div>
    )
  }
}
