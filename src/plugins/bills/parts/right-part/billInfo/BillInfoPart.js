import React from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import BillInfoEditableContainer from './BillInfoEditableContainer'
import BillInfoReadOnlyContainer from './BillInfoReadOnlyContainer'
import EmptyBody from '../../../elements/EmptyBody'
import key from '../../../key'
import { connect } from '@ekuaibao/mobx-store'
import LoadingError from '../../../elements/LoadingError'
import AIChatBox from '../../../../../components/AIChatBox'
import { getBoolVariation } from '../../../../../lib/featbit'

@EnhanceConnect(state => ({
  billList: state[key.ID].billList,
  userInfo: state['@common'].userinfo.data.staff
}))
@connect(store => ({ size: store.states['@layout'].size }))
export default class BillInfoPart extends React.Component {
  renderEmpty() {
    let label = i18n.get('点击左侧列表查看详情')
    let { billList } = this.props
    if (!billList.length) {
      label = i18n.get('欢迎使用易快报')
    }
    return <div style={{ padding: '0 10px', height: '100%' }}>
      <EmptyBody label={label} type="FeeWelcome" style={{ backgroundColor: 'var(--eui-bg-body)', borderRadius: '8px' }} />
    </div>
  }

  renderError() {
    const retryFn = this.props.retryWhenError
    return <LoadingError retryFn={retryFn} />
  }

  renderAIChat() {
    const { bus } = this.props
    return <AIChatBox chatId="BillInfoPart:" bus={bus} />
  }

  renderBody() {
    const { dataSource, bus, userInfo, riskData, creatStyle = '', showHeaderClose, showFullScreenDrawer, billFooter, mode, showUpDown, ...others } = this.props
    //----判断我自己是不是寄单节点,如果不是寄单节点不能增加和编辑寄送信息-----
    const plan = dataSource && dataSource.plan
    const nodes = (plan && plan.nodes) || []
    const expressNode = nodes.find(node => node.expressConfig && node.expressConfig.type === 'send')
    let isSend = expressNode && expressNode.approverId && expressNode.approverId.id === userInfo.id
    if (!isSend && expressNode && expressNode.counterSignersCandidate) {
      expressNode.counterSignersCandidate.forEach(signer => {
        if (!isSend) isSend = signer.signerId === userInfo.id
      })
    }

    if (!dataSource) {
      const { showEmptyPage, loadingError } = this.props
      if (loadingError) return this.renderError()
      if (!showEmptyPage) return null
      if (getBoolVariation('aprd-5394-ai-chat')) {
        return this.renderAIChat()
      }
      return this.renderEmpty()
    }
    const { state } = dataSource
    if (state === 'new' || state === 'draft' || state === 'rejected') {
      return <BillInfoEditableContainer
        mode={mode}
        creatStyle={creatStyle}
        bus={bus}
        dataSource={dataSource}
        showHeaderClose={showHeaderClose}
        showFullScreenDrawer={showFullScreenDrawer}
        layer={this.props.layer}
        riskData={riskData}
        billFooter={billFooter}
        showUpDown={showUpDown}
      />
    }
    const bills = document.getElementById('billInfoPart')
    const param = { ...others }
    if (bills) {
      param.offsetWidth = bills.offsetWidth
    }

    return (
      <BillInfoReadOnlyContainer
        {...param}
        mode={mode || 'list'}
        bus={bus}
        showHeaderClose={showHeaderClose}
        showFullScreenDrawer={showFullScreenDrawer}
        layer={this.props.layer}
        showExpressButton={isSend}
        dataSource={dataSource}
        suppleInvoiceBtn={true}
        riskData={riskData}
        billFooter={billFooter}
      />
    )
  }

  render() {
    return (
      <div id="billInfoPart" className="dis-f fd-c flex-1 ovr-y-h">
        {this.renderBody()}
      </div>
    )
  }
}
