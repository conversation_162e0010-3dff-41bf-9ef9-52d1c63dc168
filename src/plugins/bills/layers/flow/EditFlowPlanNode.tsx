import React, { forwardRef, useEffect, useImperativeHandle, useState, useMemo } from 'react'
import { Form } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import FlowPlanSensitive from './FlowPlanSensitive'
import { forEach, get } from 'lodash'
import { getFlowRequirdNode } from '../../util/billUtils'
import { skippedTypeMap } from '@ekuaibao/lib/lib/enums'
import { getStaffShowByConfig, isPayNode } from '../../../../elements/utilFn'
import { CountersignSelector } from '../../../../elements/countersign/countersign-selector'
import InputSelector from '../../../../elements/input-selector'
import { checkStaffHasExternalStaff, flowPlanNodeFormItemStyle } from './flowHelper'
import { logEvent } from '../../../../lib/logs'
import { StaffIF } from '@ekuaibao/ekuaibao_types'
import FlowPlanUrgent from './FlowPlanUrgent'
import { IFlowPlanData, INode } from './FlowPlanType'
import MessageCenter from '@ekuaibao/messagecenter'
import { AIAgentTag, fetchNodesAIAgentMap, getAIAgentObj } from '../../../../elements/ai-agent-utils'
import styles from './EditFlowPlanNode.module.less'

const FormItem = Form.Item

const formItemLayout = {
  labelCol: { span: 24 },
  wrapperCol: { span: 24 }
}

interface Props {
  messageCenter: MessageCenter
  data: IFlowPlanData
  onlySensitive: boolean
  visible: boolean
  onUpload: (isUploading: boolean) => void
  onUpdateCountersMap: (node: any, selectCounters: StaffIF[]) => void
}

const EditFlowPlanNode: React.FC<Props> = props => {
  const { messageCenter, visible, data = {} as IFlowPlanData, onlySensitive, onUpdateCountersMap } = props
  const flowPlanNodeRef = React.useRef<FlowNodeHandle>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    messageCenter.watch('get:flow:plan:nodes', handleGetFlowPlanNodes)
    return () => {
      messageCenter.un('get:flow:plan:nodes', handleGetFlowPlanNodes)
    }
  }, [])

  const handleGetFlowPlanNodes = async () => {
    const nodes = await flowPlanNodeRef.current?.getFlowPlanNodes()
    return form.validateFields().then(values => {
      const { reason, otherReason, sensitive, sensitiveAttachment, isUrgent } = values
      return { reason, otherReason, sensitive, sensitiveAttachment, isUrgent, nodes }
    })
  }
  return (
    <Form form={form} scrollToFirstError>
      <FlowPlanUrgent data={data} onlySensitive={onlySensitive} form={form} />
      <FlowPlanSensitive data={data} onUpload={props.onUpload} form={form} />
      <FlowPlanNodes
        ref={flowPlanNodeRef}
        flowNodes={data.flowNodes}
        visible={visible}
        onUpdateCountersMap={onUpdateCountersMap}
      />
    </Form>
  )
}

interface FlowPlanNodesProps {
  flowNodes: INode[]
  visible: boolean
  onUpdateCountersMap: (node: INode, selectCounters: StaffIF[]) => void
}

interface FlowNodeHandle {
  getFlowPlanNodes: () => Promise<INode[]>
}

const FlowPlanNodes = forwardRef<FlowNodeHandle, FlowPlanNodesProps>((props, ref) => {
  const { flowNodes: defaultFlowNodes = [], visible, onUpdateCountersMap } = props
  const [flowNodes, setFlowNodes] = useState(defaultFlowNodes)
  const [requiredNodes, setRequiredNodes] = useState([])
  const [nodesAIAgentMap, setNodesAIAgentMap] = useState({})
  const [form] = Form.useForm()

  useEffect(() => {
    api.dataLoader('@common.authStaffStaffMap').load()
  }, [])

  useImperativeHandle(ref, () => ({
    getFlowPlanNodes: () => {
      return form.validateFields()
    }
  }))

  useEffect(() => {
    const updateFlowNodes = defaultFlowNodes.slice().filter(line => {
      return get(line, 'ebotConfig.type') !== 'costControlCheck' && get(line, 'type') !== 'recalculate'
    })
    getNodesAIAgentMap(updateFlowNodes)
    const requiredNodes = getFlowRequirdNode(updateFlowNodes)
    setRequiredNodes(requiredNodes)
    fillFieldValue(defaultFlowNodes)
  }, [visible])

  const getSelectCounters = (node, selectCounters) => {
    onUpdateCountersMap(node, selectCounters)
  }

  const getNodesAIAgentMap = async (flowNodes) => {
    const map = await fetchNodesAIAgentMap(nodesAIAgentMap, flowNodes)
    setNodesAIAgentMap(map)
  }

  const fillFieldValue = (flowNodes) => {
    const formData = {}
    const logData = {}
    forEach(flowNodes, node => {
      if (
        node.skippedType !== 'NO_ABILITY' &&
        node.skippedType !== 'PAY_AMOUNT_IS_0' &&
        get(node, 'ebotConfig.type') !== 'costControlCheck'
      ) {
        let value = {}
        if (
          node.type === 'countersign' &&
          node.config &&
          !node.config.isAuto &&
          node.counterSigners &&
          node.counterSigners.length
        ) {
          value = node.counterSigners
        } else if (node?.type === 'carbonCopy') {
          const staffIds = get(node, 'carbonCopy[0].staffIds', [])
          if (staffIds.length === 1) {
            const staffMap = api.getState()['@common']?.authStaffStaffMap || {}
            const staff = staffMap[staffIds[0]]
            value = {
              key: staff.id,
              value: getStaffShowByConfig(staff),
              tag: staff
            }
          }
        } else {
          value = {
            key: node.approverId ? node.approverId.id : '',
            value: node.approverId ? getStaffShowByConfig(node.approverId) : '',
            tag: node.approverId
          }
        }
        formData[node.id] = value
        logData[node.id] = { value, node }
      }
    })
    form.setFieldsValue(formData)
    logEvent('fill_flow_config_modal', { title: '填充流程配置弹窗', data: logData })
  }

  const handleSelectStaff = async (node, required, done) => {
    //  协同审批弹另一个框
    if (node?.config?.crossCorpApprove) {
      api.open('@bills:CollaborationApproveStaff', { node, ids: [node.approverId] }).then(staff => {
        done({
          key: get(staff, 'id'),
          value: getStaffShowByConfig(staff),
          tag: staff
        })
      })
    } else {
      let approveId = form.getFieldValue(node.id).key
      let limitData = node.isAllStaffs || node.allowModifyApprover ? undefined : node.staffIds
      let checkIds = approveId ? [approveId] : []
      if (node?.type === 'carbonCopy') {
        const ccStaffIds = get(node, 'carbonCopy[0].staffIds', [])
        limitData = ccStaffIds
        checkIds = ccStaffIds
      }
      const data = [
        {
          type: 'department-member',
          checkIds: approveId ? [approveId] : []
        }
      ]
      const hasExternalStaff = await checkStaffHasExternalStaff(limitData)
      if (hasExternalStaff) {
        data.push({
          type: 'external',
          checkIds: approveId ? [approveId] : []
        })
      }
      api
        .open('@organizationManagement:SelectStaff', {
          title: i18n.get('选择人员'),
          multiple: false,
          required: true, // 单据提交弹框中审批节点必须有人才能点击确定
          staffLimitData: limitData,
          data,
          fetchDataSourceAction: {
            staff: api.invokeService('@organizationManagement:get:visibility:staffs'),
            department: api.invokeService('@organizationManagement:get:visibility:departments')
          },
          // 非会签节点选人遵循通讯录组织范围规则
          followContactRules: true,
          notFollowExternalChargeRules: hasExternalStaff
        })
        .then((checkedList: []) => {
          const staffData: any = checkedList.find((v: any) => v.type === 'department-member')
          const externalStaffData: any = checkedList.find((v: any) => v.type === 'external')
          let staffs = []
          if (staffData?.checkList?.length) {
            staffs = staffData.checkList
          }
          if (externalStaffData?.checkList?.length) {
            staffs = externalStaffData.checkList
          }
          const staff = staffs[0]
          done({
            key: staff?.id,
            value: getStaffShowByConfig(staff),
            tag: staff
          })
        })
    }
  }

  const renderNoticeDom = laterCalcReason => {
    return !!laterCalcReason ? (
      <span style={{ height: 25 }}>
        <span style={{ color: '#FA8C16' }}>{i18n.get('注意：')}</span>
        <span style={{ color: '#8c8c8c' }}>{laterCalcReason}</span>
      </span>
    ) : null
  }

  const nodes = useMemo(() => flowNodes.filter(line => {
    return get(line, 'ebotConfig.type') !== 'costControlCheck' && get(line, 'type') !== 'recalculate'
  }), [flowNodes])

  return (
    <Form name={'nodes'} form={form} scrollToFirstError>
      {nodes
        .map((el, idx) => {
          const { configNodeId, config, type, laterCalcReason } = el
          if (config && config.isNeedCashierNode === false) {
            // 出纳节点被停用，从前端隐藏
            return null
          }
          if ((type === 'ebot' || type === 'invoicingApplication' || type === 'aiApproval') && el.ebotConfig?.hiddenNode) {
            if ((el.ebotConfig?.hiddenModule || ['feeflow']).includes('feeflow')) {
              return null
            }
          }
          if (el?.skippedType !== 'NO_SKIPPED' && el?.config?.hiddenNode) {
            return null
          }

          // 是否启用自动跳过样式
          const isEbot = el.type === 'ebot' || el.type === 'invoicingApplication' || el.type === 'aiApproval'
          const hasApprover = el.isAllStaffs || (el.staffIds && el.staffIds.length > 0)
          let isPass =
            el.skippedType === 'NO_ABILITY' || el.skippedType === 'PAY_AMOUNT_IS_0' || !hasApprover || isEbot
          const skippedType = el.skippedType
          const isCountersign = el.type === 'countersign' // 是否是会签节点
          // @i18n-ignore
          let showCountersignComponent = isCountersign
            ? el?.name === '出纳支付'
              ? el.skippedType !== 'NO_ABILITY'
              : isCountersign
            : isCountersign


          const rules = []
          if (requiredNodes.includes(configNodeId)) {
            rules.push({
              required: true,
              validator(rule, value, callback) {
                if (!isCountersign && (!value || !value.key || !value.value)) {
                  return callback(i18n.get('please-choose-format', { format: el.name }))
                } else if (isCountersign && (!value || !value.length)) {
                  return callback(i18n.get('please-choose-format', { format: el.name }))
                }
                callback()
              }
            })
          }
          let skipMapStr = skippedTypeMap()[
            `${el.skippedType}_${el.skipWhenApproverNonMatched.toString().toLocaleUpperCase()}`
          ]
          if (el.type === 'ebot') {
            skipMapStr = 'EBot'
          }
          if (el.type === 'invoicingApplication') {
            skipMapStr = i18n.get('开票申请')
          }
          if (el.type === 'aiApproval') {
            const ApprovalCopilotPower = api.getState()['@common'].powers.ApprovalCopilot
            if(!ApprovalCopilotPower){
              skipMapStr = i18n.get('功能过期，请联系管理员处理')
            }else{
              const { agentTitle } = getAIAgentObj(el, nodesAIAgentMap)
              skipMapStr = agentTitle
            }
          }
          let placeholder = !isPass ? i18n.get('选择审批人') : skipMapStr ? i18n.get(skipMapStr) : skipMapStr
          // @i18n-ignore
          // const name = el.name === i18n.get('出纳支付节点') ? (el.label ? el.label : el.name) : el.name
          const name = isPayNode(el) ? (el.label ? el.label : el.name) : el.name

          let customStaffs
          let customDesc
          let tagShow = true
          const isCarbonCopy = el.type === 'carbonCopy'
          if (isCarbonCopy) {
            const ccStaffIds = get(el, 'carbonCopy[0].staffIds', [])
            const staffMap = api.getState()['@common']?.authStaffStaffMap || {}
            customStaffs = ccStaffIds.map(id => { return { signer: staffMap[id] } })
            customDesc = i18n.get('抄送至{__k0}名人员', { __k0: customStaffs.length })
            showCountersignComponent = customStaffs.length > 1 ? true : showCountersignComponent
            tagShow = customStaffs.length
            placeholder = customStaffs.length === 0 ? i18n.get('匹配不到抄送人，将自动跳过本节点') : placeholder
            isPass = !customStaffs.length
          }

          let label = i18n.currentLocale === 'en-US' && el?.enName ? el.enName : name

          if (el.type === 'aiApproval') {
            label = <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }} >
              <span>{label}</span>
              <AIAgentTag text='AI' />
            </span>
          }

          return (
            <FormItem
              key={idx}
              style={flowPlanNodeFormItemStyle}
              {...formItemLayout}
              name={el.id}
              label={label}
              rules={rules}
            >
              {showCountersignComponent ? (
                <CountersignSelector
                  {...props}
                  node={el}
                  customDesc={customDesc}
                  customStaffs={customStaffs}
                  isEditConfig={true}
                  getSelectCounterSigners={getSelectCounters}
                  noticeDom={renderNoticeDom(laterCalcReason)}
                />
              ) : (
                <InputSelector
                  onSelect={done => handleSelectStaff(el, requiredNodes.includes(configNodeId), done)}
                  placeholder={el.type === 'branch' ? i18n.get('系统计算中') : placeholder}
                  pass={{ isPass: isPass, skippedType: skippedType }}
                  disabled={el.type === 'branch'}
                  noticeDom={renderNoticeDom(laterCalcReason)}
                  tagShow={tagShow}
                  closable={false}
                  className={el.type === 'aiApproval' ? styles['ai-agent-input-selector-wrapper'] : ''}
                />
              )}
            </FormItem>
          )
        })
        .filter(v => v)}
    </Form>
  )
})

export default EditFlowPlanNode
