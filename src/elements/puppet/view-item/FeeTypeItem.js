import styles from './SelectItem.module.less'
import React from 'react'
import { Col, Row } from 'antd'
import FeeTypeSelect from '../../feeType-tree-select'
import FeeTypeSelectEUI from '../../feeType-tree-select-eui'
import classNames from 'classnames'

export default function FeeTypeItem(props) {
  const { label, useEUI } = props

  const FeeTypeSelect$ = useEUI ? FeeTypeSelectEUI : FeeTypeSelect
  return (
    <Row className={styles.select_specification_wrapper}>
      <Col span={24} className={styles.select_specification_label}>
        <div className={classNames(styles.feetype_label, styles['eui-feetype-label'])}>
          {`${label || 'Default'}`}
        </div>
      </Col>
      <Col span={24}>
        <FeeTypeSelect$ {...props} />
      </Col>
    </Row>
  )
}
