import MessageCenter from '@ekuaibao/messagecenter'
import { startOpenFlowPerformanceStatistics } from '../../../../lib/flowPerformanceStatistics'
import { Backlog } from '../types'
import { get } from 'lodash'
import { action } from './constant'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager'
import { app } from '@ekuaibao/whispered'
import { ButtonProps } from '@hose/eui'
import { FlowAction } from '../../layers/bill-info-popup/FlowAction'
import { isActionOfTransferingState, isActionOfNotNeedRefresh, isMofiyAction } from './actions-utils'

export interface BillActionBaseProps {
  backlog: Backlog
  riskData?: any
  from: 'from_drawer' | 'from_myBill' | 'other'
  bus?: (MessageCenter & { reload?: () => void } & { getValueWithValidate?: (validateType?: number) => Promise<any>})
  layer?: ILayerProps['layer']
  name: FlowAction
  /**
   * 小权限ID
  */
  privilegeId?: string
  onFooterButtonsClick?: (action: number, backlog: Backlog, riskData: any) => Promise<void>
  callBack?: () => void
  /**
   *  刷新当前单据信息
   */
  forceUpdateFlow?: () => void
  /**
   * 在onAction完成之后触发，一般用来操作之后的页面更新
   */
  postAction?: (action: FlowAction, metaAction?: () => void) => void
  /**
   * 是否在onAction完成之后在按钮点击后关闭抽屉，默认为true
   */
  closeAfterAction?: boolean
  noCheckPermissions?: boolean
  showAllFeeType?: boolean
}

export class BillActionBase<T extends BillActionBaseProps = BillActionBaseProps> {
  /** 前端定义的，在处理函数中有使用 */
  action: number = action.meanless

  buttonProps: ButtonProps

  props: T

  /** 后端定义的，在接口中对应 action */
  actionName: FlowAction

  forceUpdateFlow?: () => void

  getContext = () => {
    const commonState = app.getState('@common')
    const baseDataProperties = commonState.globalFields.data
    const budgetPower = commonState.powers.Budget
    const staffs = commonState.staffs
    return {
      baseDataProperties,
      budgetPower,
      staffs
    }
  }

  defaultHandleActionImplementation = async (type: number, backlog: Backlog, riskData: any) => {
    const { handleActionImplementation } = await app.require<any>('@audit/service').load()
    return new Promise((resolve, reject) => {
      try {
        handleActionImplementation({
          type,
          riskData,
          backlog,
          fn: () => {
            resolve(1)
          },
          // 想要优化这里，需要优化 handleActionImplementation 的实现。
          context: {
            props: this.getContext()
          }
        })
      } catch (e) {
        reject(e)
      }
    })
  }

  onAction: () => Promise<any> | void = async () => {
    const { onFooterButtonsClick = this.defaultHandleActionImplementation, backlog, riskData } = this.props
    startOpenFlowPerformanceStatistics()
    return onFooterButtonsClick(this.action, backlog, riskData)
  }

  onClick = async () => {
    try {
      await this.onAction()
      const actionName = this.props.name
      if (actionName === FlowAction.Cancel) {
        return
      }
      if (!isActionOfNotNeedRefresh(actionName)) {
        this.forceUpdateFlow?.()
      }
      // 审批中修改比较特殊，在点击修改之后，需要保留抽屉状态，所以需要传入 layerClose 参数
      const _isModifyAction = isMofiyAction(actionName)
      if (this.props.postAction) {
        this.props.postAction(actionName, this.reloadData.bind(this))
      } else if (
        isActionOfTransferingState(this.props.name) ||
        // 修改单据后，会导致页面内容变更，所以也需要刷新
        _isModifyAction
      ) {
        this.reloadData(!_isModifyAction)
      }
    } catch (e) {
      console.error(e)
    }
  }

  constructor(props: T, buttonConfig: ButtonProps) {
    this.props = props
    this.actionName = props.name
    this.buttonProps = buttonConfig || {}
    this.forceUpdateFlow = props.forceUpdateFlow.bind(this)
  }

  reloadData = (layerClose?: boolean) => {
    const { callBack, layer, bus } = this.props
    if (
      layer &&
      (get(this.props, 'params.autoClose') !== false && get(this.props, 'closeAfterAction') !== false) &&
      layerClose !== false
    ) {
      layer.emitCancel()
    }
    callBack && callBack()
    bus?.reload?.()
    app.invokeService('@layout5:refresh:menu:data')
  }
}
