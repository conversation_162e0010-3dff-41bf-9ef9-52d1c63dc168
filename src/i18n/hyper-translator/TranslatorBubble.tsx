import React, { useState, useRef } from 'react';
import { Tooltip } from '@hose/eui';
import { FilledOtherTranslate, FilledTipsClose, FilledTipsYes } from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
interface BubbleProps {
    translateConfig?: any,
    onBubbleClose?: () => void,
    target
}

export const TranslatorBubble: React.FC<BubbleProps> = ({
    translateConfig,
    onBubbleClose,
    target
}) => {
    const [visible, setVisible] = useState(true);
    const [enable, setEnable] = useState(false)
    const [isDragging, setIsDragging] = useState(false);
    const [position, setPosition] = useState({ x: 20, y: 120 });
    const [tooltipVisible, setTooltipVisible] = useState(false)
    const ref = useRef<HTMLDivElement>(null)
    const handleMouseDown = (e: React.MouseEvent) => {
        setIsDragging(true);
        let isFloatingDragging = true;
        e.preventDefault()
        e.stopPropagation()
        const rect = ref.current?.getBoundingClientRect();
        const offsetX = e.clientX - rect.left;
        const offsetY = e.clientY - rect.top;
        let currentPosition = {
            x: position.x,
            y: position.y
        };
        const handleMouseMove = (e: MouseEvent) => {
            if (!isFloatingDragging) return;
            e.preventDefault()
            e.stopPropagation()
            setTooltipVisible(false)
            currentPosition.x = window.innerWidth - e.clientX - offsetX
            currentPosition.y = window.innerHeight - e.clientY - offsetY
            setPosition({
                x: currentPosition.x,
                y: currentPosition.y
            });
        };

        const handleMouseUp = (e) => {
            isFloatingDragging = false
            setIsDragging(false);
            const closeButton = e.target as HTMLElement;
            if (closeButton.closest('.close-button-translator-ekb')) {
                onCloseClick()
            } else if (Math.abs(currentPosition.x - position.x) < 5 && Math.abs(currentPosition.y - position.y) < 5) {
                onBubbleClick();
            } else {
                setPosition({
                    x: 20,
                    y: currentPosition.y < 0 || currentPosition.y > window.innerHeight - 30 ? 120 : currentPosition.y
                });
            }
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    };
    const onCloseClick = () => {
        const flag = translateConfig.translate.language.translateLocal
        if (flag) {
            translateConfig.translate.language.translateLocal = false
            translateConfig.translate.restore()
        }
        localStorage.setItem(`${Fetch.ekbCorpId}_enableHyperTranslate`, 'close')
        setVisible(false);
        onBubbleClose?.()
    }

    const onBubbleClick = () => {
        setEnable(!enable)
        const flag = translateConfig.translate.language.translateLocal
        translateConfig.translate.language.translateLocal = !flag
        if (!flag) {
            translateConfig.translate.ignore.class = ['ignore-translate']
            translateConfig.translate.execute?.()
        } else {
            translateConfig.translate.ignore.class.push('translate-ignore-class')
            translateConfig.translate.restore()
        }
    }

    return <Tooltip title={i18n.get('点击翻译成中文')} placement='topRight' open={tooltipVisible} onOpenChange={(open) => setTooltipVisible(open)}>
        <div
            ref={ref}
            style={{
                position: 'fixed',
                right: `${position.x}px`,
                bottom: `${position.y}px`,
                width: '40px',
                height: '40px',
                borderRadius: '20px',
                transition: 'opacity 0.3s ease',
                opacity: visible ? 1 : 0,
                backgroundColor: 'var(--eui-bg-body)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: "center",
                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
                cursor: isDragging ? 'grabbing' : 'pointer',
                transform: isDragging ? 'scale(1.1)' : 'none',
                zIndex: 9999,
            }}
            onMouseDown={handleMouseDown}
        >
            <div style={{
                position: 'relative',
                width: '40px',
                height: '40px',
                borderRadius: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: "center",

            }}>
                <FilledTipsClose
                    className="close-button-translator-ekb"
                    style={{
                        color: "var(--eui-icon-n3)",
                        position: "absolute",
                        top: '-6px',
                        right: '-10px'
                    }}
                />
                <FilledOtherTranslate style={{
                    color: "var(--eui-primary-pri-500)",
                    fontSize: 32
                }} />
                {enable && <FilledTipsYes style={{
                    color: "var(--eui-function-success-500)",
                    position: "absolute",
                    bottom: '2px',
                    right: '2px'
                }} />}
            </div>
        </div >
    </Tooltip>
};
