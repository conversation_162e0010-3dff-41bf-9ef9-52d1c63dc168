import { ILayerProps } from "@ekuaibao/enhance-layer-manager";
import { FlowAction } from "../layers/bill-info-popup/FlowAction";
import { Action } from "./types";

export const buttonsOfCreatingNewBill: Action[] = [
  {
    action: FlowAction.Submit,
    name: i18n.get('提交送审'),
    type: 'system',
    category: 'primary',
    id: 'ACTION_SUBMIT',
    describe: "提交单据，开始审批流程"
  },
  {
    action: FlowAction.Save,
    name: i18n.get('存为草稿'),
    describe: "在编辑单据的过程中随时保存修改内容",
    type: 'system',
    category: 'secondary',
    id: 'ACTION_EDIT',
  },
]

const deleteButton: Action = {
  action: FlowAction.Delete,
  name: i18n.get('删除单据'),
  describe: "删除无用单据，被删除的单据将出现在回收站",
  type: 'system',
  category: 'secondary',
  theme: 'danger',
  id: 'ACTION_DELETE',
}


const cancelButton: Action = {
  action: FlowAction.Cancel,
  name: i18n.get('取消'),
  describe: "取消编辑单据",
  type: 'system',
  category: 'secondary',
  id: 'ACTION_CANCEL',
}

export const getButtonsOfCreatingNewBill = (layer?: ILayerProps['layer']): Action[] => {
  if (layer) {
    // @ts-ignore
    return [...buttonsOfCreatingNewBill, cancelButton]
  }
  return [
    ...buttonsOfCreatingNewBill,
    deleteButton,
  ]
}