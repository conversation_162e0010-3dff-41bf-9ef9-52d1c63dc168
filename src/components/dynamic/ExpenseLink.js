import React, { Fragment, PureComponent } from 'react'
import styles from './ExpenseLink.module.less'
import classNames from 'classnames'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { Icon } from 'antd'
import { get, flatten, compact } from 'lodash'
import { Button } from '@hose/eui'
import { OutlinedTipsAdd } from '@hose/eui-icons'
import { isDisable } from '../utils/fnDisableComponent'
import RequisitionInfo from '../../elements/puppet/RequisitionInfo'
import AssociatedTrip from '../../elements/puppet/RelevantApply'
import { related } from '../../elements/feeDetailViewList/Related'
import { showModal } from '@ekuaibao/show-util'
import { copyExpenseLinkFeeDetail } from '../utils/fnExpenseLinkUtil'
import { parseFormValueAsParam } from '../../plugins/bills/util/parse'
import { fnCheckLimitTripOrder, limitTripOrderFilter } from '../utils/fnExpenseLinkHelper'
import { importFeeByExpenseLink } from '../../plugins/bills/util/trackLogs'
import { useDepartmentVisible } from '../../lib/featbit'

@EnhanceField({
  descriptor: {
    type: 'expenseLink'
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => {
  return {
    requisitionInfo: state['@bills'].requisitionInfo,
    loanPackage: state['@common'].loanPackage,
    baseDataProperties: state['@common'].globalFields.data,
    entityInfo: undefined
  }
})
export default class ExpenseLink extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      isShowImportTips: false,
      hasWrittenOff: false,
      hasDetail: false,
      showImportTripOrderButton: false,
      relatedRequisitionData: [],
      limitTripOrders: [],
      hasOrder: false
    }
    this.clearDetailsMap = {}
    this.expenseLink = []
  }

  componentWillMount() {
    const { value, bus, flowId, submitterId, isModify, field } = this.props
    const cValue = value ? [value] : []
    related.setExpenseLink(cValue)
    related.setExpenseSpecification(field)
    const param = isModify ? { id: submitterId.id, status: 'REPAID', flowId } : { id: submitterId.id, status: 'REPAID' }
    api.invokeService('@common:get:submitter:loan:list', param).then(_ => {
      this.setState({
        hasWrittenOff: this.checkWrittenOff(value)
      })
    })
    bus.on('set:delegator', this.fnSetDelegator)
    bus.on('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
  }

  componentDidMount() {
    const { value, billSpecification, billState } = this.props
    const limitTripOrders = fnCheckLimitTripOrder(billSpecification)
    this.setState({ limitTripOrders })
    // 防止无缘无故刷新单据值
    // fnHandleExpenseLinkValueChanged中已经执行了getTripOrderListByExpenseCode
    if (value && ['new', 'draft', 'rejected', 'modify'].includes(billState)) {
      let id = typeof value === 'object' ? value.id : value
      this.fnHandleExpenseLinkValueChanged(id)
    } else {
      this.getTripOrderListByExpenseCode(value, true)
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('set:delegator', this.fnSetDelegator)
    bus.un('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
  }

  fnSetDelegator = () => {
    let { template = [], form } = this.props
    let temp = template.filter(
      el => el.defaultValue && el.defaultValue.type === 'predefine' && el.defaultValue.value === 'submit.requisition'
    )
    if (temp) {
      let valueMap = {}
      temp.forEach(el => {
        valueMap[el.field] = undefined
      })

      form.setFieldsValue(valueMap)
    }
    this.handleRemove()
  }

  handleDimentionMultiCurrencyChange = dimention => {
    const { billSpecification } = this.props
    const applyConfig = billSpecification?.configs?.find(v => v?.ability === 'apply')
    if ((applyConfig?.sameCurrency || applyConfig?.sameLegalEntity) && (dimention?.id !== this.legalEntityMultiCurrency || !dimention?.id)) {
      this._handleRemove()
    }
  }

  onChange = async (id, selectedData) => {
    const { bus, onChange, requisitionInfo = [], submitterId, field } = this.props
    const { limitTripOrders = [] } = this.state
    const res = selectedData ?? requisitionInfo.find(v => v.id === id)
    let isTips = true //  默认展示tips
    const expenseLink = await this.fnHandleExpenseLinkValueChanged(id)
    const data = compact(flatten(expenseLink?.map(v => v.form.details)))
    related.setExpenseLink([res])
    onChange?.(res)
    // 自动导入费用明细
    if (field?.automaticImport) {
      const feeDetails = await copyExpenseLinkFeeDetail(data, submitterId, false)
      if (feeDetails?.autoFeeDetails) {
        isTips = feeDetails.isApportionAbortCopy
        feeDetails.autoFeeDetails.isImportConsume = true
        bus.emit('import:expenseLink', feeDetails.autoFeeDetails)
      }
    }
    if (isTips && limitTripOrders.length) {
      const [first] = limitTripOrders
      if (first === 'ALL') {
        // 三种限制方式都有了，就不可以导入订单了
        isTips = false
      }
    }
    bus.emit('expenseLink:change', { expenseLink: res, clearData: true })
    this.setState({ isShowImportTips: isTips, hasWrittenOff: this.checkWrittenOff(res) })
  }

  fnHandleExpenseLinkValueChanged = async id => {
    const { template, form, bus } = this.props
    if (!id) return
    const resp = await api.invokeService('@bills:get:apply-event-detail-list', id)
    if (!resp?.items) return
    const flow = resp.items
    this.setState({ relatedRequisitionData: flow || [] }, () => {
      this.getTripOrderListByExpenseCode({ id })
    })
    this.expenseLink = flow
    const hasDetail = this.expenseLink.filter(item => get(item, 'form.details')).length > 0
    if (hasDetail) {
      // 告知单据详情关联申请的明细
      bus.emit('billInfoEditable:expenseLinkDetails:change', { expenseLinkDetails: get(flow, '[0].form.details') })
    }
    this.setState({ hasDetail })
    if (template) {
      let temp = template.filter(
        el => el.defaultValue && el.defaultValue.type === 'predefine' && el.defaultValue.value === 'submit.requisition'
      )
      if (temp) {
        const depFields = []
        let valueMap = {}
        const flowForm = flow && flow.find(v => v.form && !v.form.linkRequisitionInfo)
        if (flowForm && flowForm.form) {
          temp.forEach(el => {
            valueMap[el.field] = flowForm.form[el.field]
            if (el.type === 'ref:organization.Department') {
              depFields.push(el)
            }
          })
          this.legalEntityMultiCurrency = flowForm?.form?.legalEntityMultiCurrency
          if (useDepartmentVisible()) {
            for (const v of depFields) {
              const isCanSelect = await bus.invoke(`department:check:${v.name}:value`, valueMap[v.name])
              if (!isCanSelect) {
                delete valueMap[v.name]
              }
            }
          }
          form.setFieldsValue(valueMap)
        }
      }
    }
    return hasDetail ? flow : []
  }

  handleCopyConsume = () => {
    let { bus, submitterId } = this.props
    bus.invoke('element:details:importRequistition:click', this.expenseLink, submitterId).then(result => {
      result.isImportConsume = true
      bus.emit('import:expenseLink', result)
    })
    this.setState({ isShowImportTips: false })
  }

  handleImportWrittenOff = _ => {
    let { bus } = this.props

    if (this.writtenoff) {
      if (this.writtenoff[0].writtenOffRemainTimes === 0) {
        return showModal.info({
          title: i18n.get('可核销次数为0')
        })
      }
      bus.emit('import:writtenoff:fromApply', this.writtenoff)
      this.setState({ hasWrittenOff: true, isShowImportTips: false })
    }
  }

  handleClose = () => {
    this.setState({ isShowImportTips: false })
  }

  isImportDetails = () => {
    const { value, billSpecification } = this.props
    const { hasDetail } = this.state
    return hasDetail && value && billSpecification.type !== 'loan'
  }

  checkWrittenOff = value => {
    let {
      loanPackage: { items = [] }
    } = this.props
    if (value) {
      let relatedIds = value.related.map(v => v.id)
      let writtenOff = items.filter(el => relatedIds.includes(el.flowId))
      this.writtenoff = writtenOff
      return writtenOff.length > 0
    }
    return false
  }

  handleAddExpenseLink = () => {
    this.fnChangeExpenseLink(this._handleAddExpenseLink, true)
  }

  updateApplyFromExpense = params => {
    const { bus } = this.props
    bus && bus.emit('update:apply:from:Expense', params)
  }

  _handleAddExpenseLink = async () => {
    const { value } = this.props
    const { id, selectedData } = await api.open('@bills:BillStackerModal', {
      viewKey: 'ExpenseLinkListView',
      updateApplyFromExpense: this.updateApplyFromExpense,
      value: value?.id ?? value,
      refValue: value?.id ? { [value.id]: value } : {}
    })
    this.handleClearRelated()
    this.setState({ showImportTripOrderButton: false }, () => {
      this.onChange(id, selectedData)
    })
  }

  getTripOrderListByExpenseCode = async (data, isDidMount) => {
    const { billSpecification, bus, billState, baseDataProperties } = this.props
    //自动导入订单
    const autoAssociate = billSpecification?.configs.find(v => v.ability === 'apply')?.autoAssociateOrderConfig
      ?.autoAssociate
    const allowSubmitDetail = billSpecification?.configs.find(v => v.ability === 'apply')?.autoAssociateOrderConfig
      ?.allowSubmitDetail
    const billData = await this.props.bus.getValue()
    if ((isDidMount && billState === 'new' && autoAssociate) || (!isDidMount && autoAssociate)) {
      importFeeByExpenseLink({ title: '开始导入订单明细' })
      if (!data) return
      const param = parseFormValueAsParam(billData, billSpecification, undefined, baseDataProperties)
      bus.emit('import:expenseLink:loading', true)
      api
        .invokeService('@bills:postAutoAssociateDetailcr', {
          requisitionId: data?.id,
          specificationId: billSpecification?.id,
          form: {
            ...param.form,
            specificationId: billSpecification?.id,
            detail: [],
            source: 'detail_'
          }
        })
        .then(resp => {
          bus.emit('import:expenseLink:loading', false)
          const details = this.props.form.getFieldValue('details') || []
          let newDetails = resp.items
          importFeeByExpenseLink({ title: '导入明细接口成功', result: newDetails })
          // 是否只展示出行人中包含提交人
          if (allowSubmitDetail) {
            newDetails = newDetails.filter(oo => {
              return oo.feeTypeForm.travelers.some(item => {
                if (typeof item === 'string') {
                  return item === billData.submitterId.id
                } else {
                  return item.id === billData.submitterId.id
                }
              })
            })
          }
          const clearDatas = this.clearDetailsMap[data?.id]
          let detailsValue = details.concat(newDetails).map((v, idx) => {
            v.idx = idx
            return v
          })
          if (!newDetails.length && clearDatas?.length) {
            importFeeByExpenseLink({ title: '清空重新关联' })
            detailsValue = details.concat(clearDatas).map((v, idx) => {
              v.idx = idx
              return v
            })
          }
          if (newDetails?.length) {
            bus.setFieldsValue({ details: detailsValue }).then(_ => {
              importFeeByExpenseLink({ title: '单据赋值明细成功', result: detailsValue })
              bus.emit('details:change', detailsValue)
            })
          }
        }).catch(err => {
          bus.emit('import:expenseLink:loading', false)
          importFeeByExpenseLink({ title: '请求关联明细错误', result: err })
        })
      setTimeout(() => { bus.emit('import:expenseLink:loading', false) }, 9000)
    } else {
      //@i18n-ignore
      api.invokeService('@third-party-manage:get:entity:by:platform:name', '行程管理').then(result => {
        const entityInfo = result?.items?.[0]
        if (entityInfo) {
          const entityId = entityInfo.id
          const relatedRequisitionData = this.state?.relatedRequisitionData || []
          const filterCode = relatedRequisitionData
            ?.map(item => `form.E_${entityId}_申请单编号.containsIgnoreCase("${item?.form?.code}")`)
            .join(' || ')
          const filter = filterCode ? `(active==true) && (${filterCode})` : '(active==true)'
          const params = {
            entityId,
            type: 'LIST',
            query: { limit: { start: 0, count: 10 }, filterBy: filter },
            params: { type: 'order' }
          }
          api.invokeService('@third-party-manage:search:datalink:by:entityId', params).then(res => {
            const itemLength = res?.items?.data?.length
            if (itemLength) {
              const { limitTripOrders = [] } = this.state
              const [first] = limitTripOrders
              let showImportTripOrderButton = true
              if (limitTripOrders.length) {
                if (first === 'ALL') {
                  // 三种限制方式都有了，就不可以导入订单了
                  showImportTripOrderButton = false
                }
              }
              this.setState({ showImportTripOrderButton, entityInfo })
            } else {
              this.setState({ entityInfo })
            }
          })
        }
      })
    }
  }

  handleRemove = () => {
    this.setState({ relatedRequisitionData: [] })
    this.fnChangeExpenseLink(this._handleRemove)
  }

  _handleRemove = () => {
    const { onChange, bus } = this.props
    onChange()
    related.setExpenseLink([])
    bus.emit('billInfoEditable:expenseLinkDetails:change', { expenseLinkDetails: [] })
    bus.emit('expenseLink:change', { clearData: true })
    this.setState({ hasWrittenOff: this.checkWrittenOff(), entityInfo: undefined, showImportTripOrderButton: false })
  }

  fnChangeExpenseLink(fn, isAdd) {
    const details = this.props.form.getFieldValue('details')
    const relatedDetailArr = details?.filter(line => line.feeTypeForm.linkDetailEntities)
    const filterDetails = details?.filter(v => v?.feeTypeForm?.systemGenerationDetail)
    if (relatedDetailArr?.length || filterDetails?.length) {
      showModal.confirm({
        title: i18n.get('提示'),
        content: i18n.get('申请明细已被关联，若删除关联申请，将同步删除明细中的关联明细'),
        cancelText: i18n.get('取消'),
        okText: i18n.get('确定'),
        onOk: () => this.handleClearRelated(fn, isAdd)
      })
    } else {
      fn()
    }
  }

  handleClearRelated = (fn, isAdd) => {
    if (isAdd && fn) {
      fn()
      return
    }
    const { form, bus, value } = this.props
    const details = form.getFieldValue('details') || []
    const relatedDetailArr = details?.filter(line => line.feeTypeForm.linkDetailEntities)
    const filterDetails = details?.filter(v => v?.feeTypeForm?.systemGenerationDetail)
    if (relatedDetailArr?.length || filterDetails?.length) {
      const cDetails = details
        .map(line => {
          if (line.feeTypeForm.linkDetailEntities) {
            delete line.feeTypeForm.linkDetailEntities
          }
          return line
        })
        .filter(v => !v?.feeTypeForm?.systemGenerationDetail)
      this.clearDetailsMap[value?.id] = details.filter(v => v?.feeTypeForm?.systemGenerationDetail)
      form.setFieldsValue({ details: cDetails })
      if (relatedDetailArr?.length > 0) {
        related.clearRelatedData()
        bus.invoke('clear:realted:application:details')
      }
      fn?.()
    }
  }

  handleDetailClick = () => {
    let { value = {}, bus, flowId } = this.props
    bus && bus.emit('check:requisition:detail', { detail: value, flowId })
  }
  handleImportTripOrder = () => {
    const { value, bus } = this.props
    const { entityInfo, relatedRequisitionData = [], limitTripOrders } = this.state
    let codeList = [value?.code]
    if (relatedRequisitionData.length > 0) {
      codeList = relatedRequisitionData?.map(item => item?.form?.code)
    }
    let otherParams = undefined
    if (limitTripOrders.length) {
      const filters = limitTripOrderFilter(limitTripOrders, entityInfo)
      otherParams = { filters }
    }
    bus && bus.emit('open:trip:order:datalink:modal', entityInfo, codeList, undefined, otherParams)
  }
  setHasOrder = (hasOrder) => {
    this.setState({
      hasOrder
    })
  }
  render() {
    const { isShowImportTips, hasWrittenOff, showImportTripOrderButton, hasOrder } = this.state
    const { value, billSpecification, submitterId } = this.props
    const canWrittenOff = !!billSpecification?.configs?.find(v => v.ability === 'writtenOff' && !v.writeOffTurnOff)
    const tripOrderInfo = billSpecification?.configs?.find(line => line.ability === 'apply')?.tripOrderInfo
    const disabled = isDisable(this.props)
    const { id, name } = submitterId

    return (
      <div
        id="expense-link-select"
        className={classNames(styles['expenselink-wrapper'], { 'expenselink-wrapper-disabled': disabled })}
      >
        <Button
          category="secondary"
          theme="default"
          size="small"
          onClick={disabled ? undefined : this.handleAddExpenseLink}
          disabled={disabled}
          icon={<OutlinedTipsAdd />}
          data-testid="field-expenseLink-select"
        >
          {i18n.get('选择')}
        </Button>
        {value && (
          <div className='new-requisition-wrapper'>
            <RequisitionInfo
              editable={!disabled}
              value={value}
              onRemove={this.fnSetDelegator}
              onDetailClick={this.handleDetailClick}
              hasOrder={hasOrder}
            />
            {tripOrderInfo && <AssociatedTrip fromExpenseLink submitterId={id} requisitionId={value.id} submitterName={name} setHasOrder={this.setHasOrder} />}
          </div>
        )}
        <ExpenseLinkActions
          isShowImportTips={isShowImportTips}
          handleClose={this.handleClose}
          list={[
            {
              show: this.isImportDetails(),
              handler: this.handleCopyConsume,
              text: i18n.get('复制明细'),
              tip: i18n.get('费用明细')
            },
            {
              show: hasWrittenOff && canWrittenOff && value,
              handler: this.handleImportWrittenOff,
              text: i18n.get('复制核销'),
              tip: i18n.get('借款包')
            },
            {
              show: showImportTripOrderButton && !!value,
              handler: this.handleImportTripOrder,
              text: i18n.get('导入差旅订单'),
              tip: i18n.get('差旅订单')
            }
          ]}
        />
      </div>
    )
  }
}

const ExpenseLinkActions = props => {
  const { list, isShowImportTips, handleClose } = props
  const showList = list.filter(v => v.show)
  if (!showList.length) return null
  return isShowImportTips ? (
    <div className="import-details">
      <div className="title">
        <Icon className="tip-icon" type="exclamation-circle-o" />
        <span className="span-info">
          是否将此申请事项关联的{showList.map(item => item.tip).join('或')}复制至当前报销单中?
        </span>
      </div>
      <div className="import-actions">
        {showList.map(item => (
          <span className="import-font mr-10" key={item.text} onClick={item.handler}>
            {i18n.get(item.text)}
          </span>
        ))}
        <span className="import-font mr-10" onClick={handleClose}>
          {i18n.get('稍后')}
        </span>
      </div>
    </div>
  ) : (
    <>
      {showList.map(item => (
        <span className="import-font mr-10" key={item.text} onClick={item.handler}>
          {i18n.get(item.text)}
        </span>
      ))}
    </>
  )
}
