/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/7/13.
 */
@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/web-theme-variables/styles/colors';

.popover_related_card {
  :global {
    .ant-popover-inner-content {
      padding: 16px;
      width: 400px;
      max-height: 160px;
      overflow-y: auto;
    }
  }
}

.popover_invoice_card {
  :global {
    .ant-popover-inner-content {
      padding: 16px;
      height: 320px;
      overflow-y: auto;
    }
  }
}

.popover_account_card {
  :global {
    .ant-popover-inner-content {
      padding: 16px;
      max-height: 160px;
      overflow-y: auto;
    }
  }
}

.account-info {
  >span {
    white-space: normal;
    text-align: left;
  }
  :global {
    .account-type {
      color: var(--eui-text-placeholder);
    }
  }
}

.popover_third_card {
  :global {
    .ant-popover-inner {
      max-height: 400px;
      overflow-y: auto;
      overflow-x: hidden;
    }
    .ant-popover-inner-content {
      padding: 16px;
    }
  }
}

.details-item {
  display: flex;
  :global {
    .checked {
      margin-top: 1px;
      flex-shrink: 0;
      margin-right: 12px;
    }
    .detail-content {
      width: 100%;
    }
  }
}

.item-wrap {
  display: flex;
  font-size: 12px;
  :global {
    .item-icon {
      align-self: flex-start;
      margin-right: 12px;
      width: 24px;
      height: 24px;
      border-radius: 24px;
      overflow: hidden;
      flex-shrink: 0;
    }
    .item-main {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      overflow-x: hidden;
      // padding-right: 16px;
      position: relative;
      .is-system{
        position: absolute;
        right: 0;
        bottom: 0;
        color: var(--eui-decorative-neu-600);
        font: var(--eui-font-note-r2);
        display: flex;
        align-items: center;
      }
      .content {
        display: flex;
        justify-content: space-between;
        flex: 1;
        overflow: hidden;
        .cost {
          flex-shrink: 0;
          padding-left: 8px;
        }
        .name {
          .feeType-name {
            color: var(--eui-text-title);
            font: var(--eui-font-body-b1);
            .amortize-type {
              background-color: var(--eui-function-info-50);
              border-radius: 4px;
              padding: 1px 4px;
              color: var(--eui-decorative-neu-600);
            }
            .detailNo, .code {
              color: var(--eui-text-placeholder);
              font: var(--eui-font-body-r1);
              word-break: break-all;
            }
          }
        }

        .name,
        .date {
          display: flex;
          position: relative;
          color: var(--eui-text-caption);
          font: var(--eui-font-note-r2);
        }
        .reasons {
          margin-bottom: 6px;
        }

        .tip-right {
          margin-top: 8px;
          border-radius: 4px;
        }

        .tip {
          display: flex;
          align-items: center;
        }
        .text-error {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          color: @red-6;
        }
      }
      .risk-warning {
        //padding-top: 10px;
        // margin-right: @space-4;
        // flex-shrink: 0;
      }
      .border {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 1px;
        background: rgba(0, 0, 0, 0.08);
      }
    }
    .new-money {
      .new-money-first {
        font: var(--eui-num-head-b1);
        color: var(--eui-text-title);
        display: flex;
        justify-content: flex-end;
      }
      .new-money-other {
        color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
        font-weight: 700;
        line-height: 18px;
        font: var(--eui-num-note-b2);
        text-align: left;
        display: flex;
        justify-content: right;
      }
      .prefix {
        font-weight: normal;
      }
    }
  }
}

.isError {
  border-radius: 2px;
  border: 1px solid red;
}


.base-currency-of-quote {
  display: flex;
  color: var(--eui-text-placeholder);
  font: var(--eui-font-note-r2);

  .label, .currency-code {
    margin-right: 4px;
  }
}

.tip-group {
  display: flex;
  flex-wrap: wrap;
  .invoice-tip {
    display: flex;
  }
  .tip-group-space {
    flex-wrap: wrap;
  }
}

.risk-warning {
  margin-top: 2px
}

.currency-wrapper {
  display: flex;
  flex-direction: column;
  :global {
    .money {
      flex: 1;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      flex-shrink: 1;
      color: var(--eui-text-title);
      font: var(--eui-num-head-b1);
    }
    .budget-money {
      color: var(--eui-text-placeholder);
      font: var(--eui-font-note-r2);
      position: absolute;
      right: 0;
      display: flex;
      top: 4px;
    }
    .shared {
      flex: 1;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
      flex-shrink: 1;
      line-height: 24px;
      .text {
        margin-right: 5px;
      }
      color: var(--brand-base);
      cursor: pointer;
      position: relative;
      span{
        position: absolute;
        z-index: 99;
        cursor: pointer;
      }
    }
  }
}

.attachment-item {
  min-width: 343px;
}