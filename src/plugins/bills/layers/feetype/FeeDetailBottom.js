import React, { PureComponent, Fragment } from 'react'
import { Button, Space, message } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import styles from './FeeDetailBottom.module.less'
import { getBoolVariation } from '../../../../lib/featbit'

export default class FeeDetailBottom extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = props.bus
    this.loadingCount = 0
    this.state = {
      disabled: false
    }
  }

  componentDidMount() {
    this.bus.on('savebtn:state:change', this.loadingChange)
  }

  componentWillUnmount() {
    this.bus.un('savebtn:state:change')
  }

  loadingChange = ({ disabled }) => {
    if(getBoolVariation('cyxq-75301')){
      if (disabled) {
        this.loadingCount += 1
        if (!this.state.disabled) {
          this.setState({ disabled: true })
        }
      } else {
        this.loadingCount = Math.max(0, this.loadingCount - 1)
        if (this.loadingCount === 0 && this.state.disabled) {
          this.setState({ disabled: false })
        }
      }
      return
    }
    const d = this.state.disabled
    if (d === disabled) return
    this.setState({ disabled })
  }

  handleCancel = () => {
    this.bus.emit('cancel:action')
  }

  handleSave = () => {
    const { disabled } = this.state
    if (disabled) return
    this.bus.setValidateLevel(0)
    this.bus.emit('save:action')
  }

  handleSaveContinue = () => {
    const { disabled } = this.state
    if (disabled) return
    this.bus.setValidateLevel(0)
    this.bus.emit('saveContinue:action')
  }

  handleSaveDraft = () => {
    const { disabled } = this.state
    if (disabled) return
    this.bus.setValidateLevel(1)
    this.bus.emit('saveDraft:action')
  }

  handleAddTag = () => {
    const { dataSource = [], billSpecification, fullDataSource, updateDetailsCallBack, bus, detailId } = this.props
    const feeTypeData = dataSource.find(el => el.feeTypeForm.detailId === detailId)
    if (!feeTypeData) return
    const data = [
      {
        form: {
          ...feeTypeData.feeTypeForm,
          feeTypeId: feeTypeData.feeTypeId,
          flowSpecificationId: billSpecification,
          flow: fullDataSource.flowId,
          specificationId: feeTypeData.specificationId
        },
        nodeState: { backlogId: fullDataSource.id }
      }
    ]
    app.open('@audit:AddTagModal', { data }).then(res => {
      //调用添加接口
      app
        .invokeService('@audit:detail:add:tag', res.newTag)
        .then(res => {
          if (updateDetailsCallBack) {
            updateDetailsCallBack()
          }
          message.success(i18n.get('添加成功'))
          if (res.value) {
            // 如果返回true说明审批单据可能发生变化，需要关闭弹窗
            app.close()
            return
          }
          bus.emit('details:tag:change')
        })
        .catch(err => {
          message.error(err?.errorMessage)
        })
    })
  }

  handlePrev = () => {
    this.bus.emit('previous:action')
  }

  handleNext = () => {
    this.bus.emit('next:action')
  }

  renderEditable = () => {
    const { addable, isRecordExpends } = this.props
    const { disabled } = this.state
    const style = disabled ? { cursor: 'not-allowed' } : undefined
    return (
      <div className={styles['modal-footer']}>
        <Space wrap>
          <Button category="primary" onClick={this.handleSave} data-testid="feetype-footer-save" style={style}>
            {i18n.get('保存')}
          </Button>
          <Button category="secondary" onClick={this.handleCancel} data-testid="feetype-footer-cancel">
            {i18n.get('取消')}
          </Button>
          {addable && (
            <Button category="secondary" onClick={this.handleSaveContinue} data-testid="feetype-footer-continue" style={style}>
              {i18n.get('再记一笔')}
            </Button>
          )}
          {!isRecordExpends && <Button category="secondary" style={style} data-testid="feetype-footer-draft" onClick={this.handleSaveDraft}>
            {i18n.get("存为草稿")}
          </Button>}
        </Space>
      </div>
    )
  }

  renderReadonly = () => {
    const { currentIdx, count, allowAddTag, showPreAndNext = true } = this.props
    return (
      <div className={styles['modal-footer']} style={{ flexShrink: 0 }}>
        {showPreAndNext ? <div style={{ marginRight: 20 }}>{`${currentIdx + 1}/${count}`}</div> : null}
        <Space wrap>
          {allowAddTag ? (
            <Button category={'primary'} onClick={this.handleAddTag}>
              {i18n.get('添加审批意见')}
            </Button>
          ) : null}
          {showPreAndNext ? (
            <Button category="secondary" disabled={currentIdx === 0} onClick={this.handlePrev}>
              {i18n.get('上一条')}
            </Button>
          ) : null}
          {showPreAndNext ? (
            <Button category="secondary" disabled={currentIdx >= count - 1} onClick={this.handleNext}>
              {i18n.get('下一条')}
            </Button>
          ) : null}
        </Space>
      </div>
    )
  }

  render() {
    const { editabel } = this.props
    return editabel ? this.renderEditable() : this.renderReadonly()
  }
}
