/**
 *  Created by gym on 2020-07-20 11:35.
 */
import React, { Component } from 'react'
import Signature, { SignaturePad, dataURLToBlob } from '@ekuaibao/signature'
import UploadButton from '../../elements/ekbc-business/upload-button'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import './SignaturePanel.less'
import { Button, Checkbox } from '@hose/eui'
import uploadFile, { uploaderMinio } from './uploadFile'
import { app as api } from '@ekuaibao/whispered'
import { MeIF } from '@ekuaibao/ekuaibao_types'
import { get } from 'lodash'
import { OutlinedEditEdit } from '@hose/eui-icons'

interface Props {
  onSignatureChange: (agr: boolean) => void
}
interface State {
  checked: boolean
  meInfo: MeIF
  isEdit: boolean
  autographUrl: string
  isUpload: boolean
  fileList: any[]
}

export default class SignaturePanel extends Component<Props, State> {
  signaturePad: SignaturePad
  state = {
    checked: true,
    meInfo: {} as MeIF,
    isEdit: false,
    isUpload: false,
    fileList: []
  }

  public getResult = () => {
    if (this.signaturePad.isEmpty()) {
      return Promise.reject()
    }
    const data = this.signaturePad.toDataURL()

    return data
  }

  componentDidMount() {
    const meInfo = api.getState('@common').userinfo.data
    const autograph = get(meInfo, 'staff.autograph', {})
    this.setState({
      meInfo,
      isEdit: !autograph.use,
      checked: get(meInfo, 'staff.autograph') ? autograph.use : true,
      autographUrl: get(meInfo, 'staff.autograph.url', '')
    })
  }

  handleSignature = (signature: SignaturePad) => {
    this.signaturePad = signature
  }

  handleCheckChange = (e: { target: { checked: boolean } }) => {
    this.setState({ checked: e.target.checked })
  }

  handleEdit = () => {
    const { isEdit } = this.state
    this.setState({ isEdit: !isEdit, fileList: [] })
  }

  handleSave = async () => {
    const { fileList } = this.state
    if (this.signaturePad.isEmpty() && !fileList.length) {
      return Promise.reject()
    }
    if (fileList.length) {
      return this.handleDoneSave(fileList[0])
    }
    const dataUrl = this.signaturePad.toDataURL()
    const blob = dataURLToBlob(dataUrl)
    const result = window.IS_STANDALONE ? await uploaderMinio(blob) : await uploadFile(blob)
    const [attachment] = result.items
    return this.handleDoneSave(attachment)
  }

  handleDoneSave = async file => {
    const { onSignatureChange } = this.props
    const { meInfo, checked } = this.state
    const autographId = get(meInfo, 'staff.autograph.id')
    await api.invokeService('@audit:update:signature', {
      id: autographId,
      imageId: file.key,
      use: checked,
      lastHabit: true
    })
    await api.dataLoader('@common.userinfo').reload()
    const updateMeInfo = api.getState('@common').userinfo.data
    this.setState({ isEdit: false, meInfo: updateMeInfo, fileList: [] })
    onSignatureChange && onSignatureChange(true)
    return Promise.resolve()
  }

  handleClear = () => {
    this.setState({
      fileList: []
    })
    this.signaturePad.clear()
  }

  handleOnDone = fileList => {
    this.setState({ isUpload: false, isEdit: true, fileList: fileList })
  }

  handleStart = () => {
    this.setState({ isUpload: true })
  }

  fnGetChildren = () => {
    return (
      <Button category="secondary" size="small" className="mr-8">
        {i18n.get('上传签名')}
      </Button>
    )
  }
  fnGetImgUrl = () => {
    const { fileList, meInfo } = this.state
    if (fileList.length > 0) {
      return fileList[0]?.url
    }
    return meInfo?.staff?.autograph?.url ?? ''
  }

  renderSignature = () => {
    const { fileList } = this.state
    return (
      <>
        {!fileList.length ? (
          <Signature
            className="signature"
            backgroundColor="rgba(247, 248, 250, 1)"
            onSignaturePad={this.handleSignature}
            placeholderClass="signature_placeholderStyle"
            placeholder={i18n.get('请在此处按住鼠标拖动签名')}
          />
        ) : (
          <div className={'img_wrap'}>
            <img src={this.fnGetImgUrl()} />
          </div>
        )}
      </>
    )
  }
  render() {
    const { checked, isEdit, meInfo, isUpload, fileList } = this.state
    const autograph = get(meInfo, 'staff.autograph', {})
    const file = fnFormatAttachment(fileList)
    const url = this.fnGetImgUrl()
    return (
      <div className="signature_panel">
        {isEdit ? this.renderSignature() : <img className="signature_image" src={url} />}
        <div className="signature_footer">
          <div className="signature_buttonList">
            {isEdit ? (
              <div className="signature_buttonGroup">
                <Checkbox checked={checked} onChange={this.handleCheckChange}>
                  {i18n.get('下次审批时，继续使用此签名')}
                </Checkbox>
                <div className="signature_buttonLeft">
                  {autograph.use ? (
                    <Button category="secondary" size="small" className="mr-8" onClick={this.handleEdit}>
                      {i18n.get('取消')}
                    </Button>
                  ) : null}
                  <Button category="secondary" size="small" className="mr-8" onClick={this.handleClear}>
                    {i18n.get('重签')}
                  </Button>
                  <div>
                    <UploadButton
                      accept={'image/png, image/jpeg, image/jpg'}
                      fileList={file}
                      multiple={false}
                      isLoading={isUpload}
                      onStart={this.handleStart}
                      children={this.fnGetChildren()}
                      onFinish={this.handleOnDone}
                      fileName="sign-image.png"
                    />
                  </div>
                  <Button size="small" category="secondary" theme="highlight" onClick={this.handleSave}>
                    {i18n.get('保存')}
                  </Button>
                </div>
              </div>
            ) : (
              <Button category="text" theme="highlight" onClick={this.handleEdit}>
                <OutlinedEditEdit fontSize={16} className="mr-4" />
                {i18n.get('编辑签名')}
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }
}
