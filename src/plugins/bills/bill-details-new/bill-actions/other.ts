import { BillActionBase } from './base-action'
import { action } from './constant'
import { shareBillAction } from '../../util/billUtils'

export class NullifyAction extends BillActionBase {
  action = action.nullify
  label = i18n.get('作废')
}

export class ShareAction extends BillActionBase {
  label = i18n.get('转发')

  onAction = () => {
    this.handleShare()
  }

  handleShare = () => {
    const { backlog } = this.props
    shareBillAction(backlog.flowId.id)
  }
}