import { app } from "@ekuaibao/whispered"
import { BillActionBase, BillActionBaseProps } from "./base-action"
import { action } from "./constant"
import { message as EUIMessage } from "@hose/eui"

export class MarkedReadAction extends BillActionBase<BillActionBaseProps> {
  action = action.commnet
  label = i18n.get('标为已读')

  onAction = () => {
    return this.handleMarkedRead()
  }

  handleMarkedRead = () => {
    const { backlog } = this.props
    const { id } = backlog.flowId
    return app.invokeService('@audit:marked:read', { ids: [id] }).then(_ => {
      EUIMessage.success(i18n.get('操作成功'))
    })
  }
}