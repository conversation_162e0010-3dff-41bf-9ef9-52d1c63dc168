@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';

.travel-item {
  width: 100%;
  display: flex;
  cursor: pointer;

  :global {
    .left-line {
      display: flex;
      flex-direction: column;
      color: rgba(46, 49, 50, 0.8);
      margin-right: @space-5;
      width: 22px;
      text-align: center;

      .date {
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        word-break: keep-all;
        font-style: normal;
      }

      .month {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        font-style: normal;
        text-align: center;
      }

      .line-icon {
        margin-left: 10px;
        flex: 1;
        border-left: 1px solid #f5f5f5;
      }
    }

    .left-part-new {
      display: flex;
      flex-direction: column;
      color: rgba(46, 49, 50, 0.8);
      margin-right: @space-5;
      width: 11px;
      text-align: center;

      .date {
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        word-break: keep-all;
        font-style: normal;
      }

      .month {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        font-style: normal;
        text-align: center;
      }

      .line-icon {
        margin-left: 6px;
        flex: 1;
        border-left: 1px solid #f5f5f5;
      }

      .line-icon-front {
        margin-left: 6px;
        height: 16px;
        border-left: 1px solid #f5f5f5;
      }
    }

    .right-part {
      flex: 1;

      .info-top {
        width: 100%;
        position: relative;
        background: rgba(46, 49, 50, 0.04);
        padding: @space-5;
        border-radius: 6px;

        .info-top-first-line {
          display: flex;

          .info-top-icon {
            margin-right: @space-5;
            width: 24px;
          }

          .info-top-city {
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: #000000;
            display: flex;

            .start {
              margin-right: @space-5;
            }

            .end {
              margin-left: @space-5;
            }

            small {
              font-size: 12px;
              color: rgba(39, 46, 59, 0.48);
            }
          }
        }

        .scene-line {
          small {
            font-size: 12px;
            color: rgba(39, 46, 59, 0.48);
          }
        }

        .info-top-second-line {
          display: flex;
          line-height: 1.5;
          margin-top: 8px;

          .info-top-name {
            margin-right: @space-5;
            font-size: 12px;
            color: rgba(39, 46, 59, 0.48);
          }

          .info-top-date {
            font-size: 12px;
            color: rgba(39, 46, 59, 0.48);

            .date-line {
              margin: 0 8px;
              color: rgba(39, 46, 59, 0.08);
            }
          }
        }

        .info-top-delete-icon {
          position: absolute;
          font-size: 16px;
          color: var(--eui-icon-n2);
          right: 12px;
          top: 12px;
        }


        .disabled {
          cursor: not-allowed;
          color: var(--eui-icon-disabled);
        }

        .travel-item-button {
          width: 51px;
          height: 32px;
          background: #22b2cc;
          border-radius: @radius-2;
          padding: 0;
          line-height: 20px;
          position: absolute;
          top: 18px;
          right: 12px;
        }
      }

      .price-wrapper {
        display: flex;

        .price-wrapper-action {
          font-size: 12px;
          cursor: pointer;
          color: var(--brand-base);
        }

        .price-wrapper-money {
          font-size: 12px;
          color: rgba(46, 49, 50, 0.48);
          margin-right: 16px;
        }
      }

      .margin-part {
        height: 16px;
      }
    }

    .right-part-new {
      flex: 1;

      .info-top {
        width: 100%;
        position: relative;
        background: rgba(46, 49, 50, 0.04);
        padding: @space-5;
        border-radius: 6px;

        .info-top-first-line {
          display: flex;

          .info-top-icon {
            margin-right: @space-5;
            width: 24px;
          }

          .info-top-city {
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            color: #000000;
            display: flex;

            .start {
              margin-right: @space-5;
            }

            .end {
              margin-left: @space-5;
            }

            small {
              font-size: 12px;
              color: rgba(39, 46, 59, 0.48);
            }
          }
        }

        .scene-line {
          small {
            font-size: 12px;
            color: rgba(39, 46, 59, 0.48);
          }
        }

        .info-top-second-line {
          display: flex;
          line-height: 1.5;
          margin-top: 8px;

          .info-top-name {
            margin-right: @space-5;
            font-size: 12px;
            color: rgba(39, 46, 59, 0.48);
          }

          .info-top-date {
            font-size: 12px;
            color: rgba(39, 46, 59, 0.48);

            .date-line {
              color: rgba(39, 46, 59, 0.08);
            }
          }
        }

        .info-top-delete-icon {
          position: absolute;
          font-size: 16px;
          color: var(--eui-icon-n2);
          right: 12px;
          top: 12px;
        }

        .disabled {
          cursor: not-allowed;
          color: var(--eui-icon-disabled);
        }

        .travel-item-button {
          width: 51px;
          height: 32px;
          background: #22b2cc;
          border-radius: @radius-2;
          padding: 0;
          line-height: 20px;
          position: absolute;
          top: 18px;
          right: 12px;
        }
      }

      .price-wrapper {
        display: flex;

        .price-wrapper-action {
          font-size: 12px;
          cursor: pointer;
          color: var(--brand-base);
        }

        .price-wrapper-money {
          font-size: 12px;
          color: rgba(46, 49, 50, 0.48);
          margin-right: 16px;
        }
      }

      .margin-part {
        height: 16px;
      }

      .date-line {
        height: 20px;
        line-height: 20px;
        font-size: 14px;
        font-weight: 500;
      }

      .trip-card {
        width: 100%;
        font-size: 14px;
        margin-top: 6px;
        display: flex;
        flex-direction: column;
        padding: 6px 12px 8px 12px;
        border-radius: 8px;
        background: var(--eui-bg-body-overlay, #f7f8fa);
      }

      .title-line {
        height: 30px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
      }

      .info-top-delete-icon {
        font-size: 16px;
        color: var(--eui-icon-n2);
      }

      .disabled {
        cursor: not-allowed;
        color: var(--eui-icon-disabled);
      }

      .title {
        display: flex;
        align-items: center;
        width: 100px;
        color: rgba(29, 33, 41, 0.9);
      }

      .info-box {
        display: flex;
        padding: 6px 12px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 6px;
        background: #fff;
      }

      .city-line {
        color: rgba(29, 33, 41, 0.9);
        font-size: 14px;
        font-weight: 500;
        line-height: 20px; /* 142.857% */
      }

      .detail-line {
        line-height: 20px;
        margin-top: 4px;
        display: flex;
        flex-wrap: wrap;
      }

      .scene-line {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        align-items: center;
      }

      .divider {
        height: 14px;
        width: 1px;
        background: rgba(29, 33, 41, 0.20);
        margin: 3px 8px;
      }

      .price-line {
        height: 20px;
        margin-top: 8px;
        display: flex;
      }

      .price {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 142.857% */
      }

      .btn-get-price {
        color: var(--eui-primary-pri-500, #2555ff);

        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 142.857% */
      }
    }
  }
}
