/*
 * @Author: joyBoy
 * @Date: 2023-12-06 18:13:06
 * @LastEditors: wangweidong <EMAIL>
 * @LastEditTime: 2024-03-28 16:16:45
 * @FilePath: /web/src/plugins/bills/layers/dataLink-detail-modal/ledgerUtil.ts
 */
import moment from 'moment'
import { LedgerListItemInterface, StatisticsSourceEnum, LedgerEntityListItem } from './LedgerRelationInterface'

import { buildFeeDateString } from '../../../../lib/fee-util'
import { get } from 'lodash'
import { EntityListDataItem } from '@ekuaibao/ekuaibao_types'

export function formatResult(items: any[] = [], statisticsSource: StatisticsSourceEnum, baseDataPropertiesMap: any) {
  if (statisticsSource === StatisticsSourceEnum.MASTER) {
    return formatBill(items, baseDataPropertiesMap)
  } else if (statisticsSource === StatisticsSourceEnum.DETAILS) {
    return formatDetail(items, baseDataPropertiesMap)
  } else if (statisticsSource === StatisticsSourceEnum.DATA_LINK) {
    return formatDataLink(items[0])
  } else if (statisticsSource === 'INVOICE') {
    return formatInvoiceData(items, baseDataPropertiesMap)
  }
  return []
}
function formatInvoiceData(items: any[] = [], baseDataPropertiesMap: any): LedgerListItemInterface[] {
  return items
}
function formatBill(items: any[] = [], baseDataPropertiesMap: any): LedgerListItemInterface[] {
  return items.map(
    (item: any): LedgerListItemInterface => {
      const {
        amount = {},
        sumFieldName,
        flowId = {},
        fieldType,
        totalNum,
        unit,
        originalApportionAmount = {},
        originalApportionTotalNum,
        fromApporation,
        manualApporationLabel
      } = item
      const submitDate = moment(flowId.form.submitDate).format('YYYY/MM/DD')
      const desc = `${submitDate} ${flowId.form.submitterId.name}`
      const sumFieldNameStr = baseDataPropertiesMap[sumFieldName].label
      const active = item.active
      const title = flowId.form.title || i18n.get('[无标题]')
      return {
        title,
        desc,
        amount,
        sumFieldName: sumFieldNameStr,
        active,
        flowId: flowId.id,
        fieldType,
        totalNum,
        unit,
        originalApportionAmount,
        originalApportionTotalNum,
        fromApporation,
        manualApporationLabel
      }
    }
  )
}

function formatDetail(items: any[] = [], baseDataPropertiesMap: any): LedgerListItemInterface[] {
  return items.map(
    (item: any): LedgerListItemInterface => {
      const {
        feeTypeId,
        sumFieldName,
        totalNum,
        unit,
        fieldType,
        originalApportionAmount = {},
        originalApportionTotalNum,
        fromApporation
      } = item
      const feeDateString = buildFeeDateString(item.feeDate)
      const active = feeTypeId.active
      const sumFieldNameStr = baseDataPropertiesMap[sumFieldName].label
      const amount = item.amount
      return {
        title: feeTypeId.fullname,
        desc: feeDateString,
        amount,
        sumFieldName: sumFieldNameStr,
        active,
        background: feeTypeId.color,
        icon: feeTypeId.icon,
        flowId: item.flowId,
        detailId: item.detailId,
        fieldType,
        totalNum,
        unit,
        originalApportionAmount,
        originalApportionTotalNum,
        fromApporation
      }
    }
  )
}

function formatDataLink(data): LedgerEntityListItem[] {
  const items = get(data, 'items', [])
  const template = get(data, 'template.content', {})
  return items.map(
    (item: EntityListDataItem): LedgerEntityListItem => {
      return {
        item,
        template
      }
    }
  )
}
