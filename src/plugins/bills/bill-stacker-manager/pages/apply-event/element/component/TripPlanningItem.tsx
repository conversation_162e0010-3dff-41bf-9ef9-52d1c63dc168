import React from 'react'
import { Button } from 'antd'
import styles from './TripPlanningItem.module.less'
import moment from 'moment'
import { getWeek } from '../../../../../../../lib/lib-util'
import { useEffect, useState } from 'react'
import { Resource } from '@ekuaibao/fetch'
import { Tooltip } from '@hose/eui'
import { getBoolVariation } from '../../../../../../../lib/featbit'
const cityFetch = new Resource('/api/v2/basedata/city/')
const TripPlanningItem = ({
  item,
  index,
  loading,
  handleClickOrder,
  handleCtripToOrder,
  formatTripButtonText,
  checkInDateTravelList
}) => {
  const [startCityEnhance, setStartCityEnhance] = useState([])
  const [endCityEnhance, setEndCityEnhance] = useState([])

  const [startCityStr, setStartCityStr] = useState([])
  const [endCityStr, setEndCityStr] = useState([])

  const formatCity = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray
    return cityArray.map(city => city.label || city.name || '').join(', ')
  }

  const formatCityOrigin = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray

    if (cityArray.length === 1) {
      return cityArray[0].label || cityArray[0].name || ''
    }

    if (cityArray.length > 1) {
      const firstCity = cityArray[0].label || cityArray[0].name || ''
      return `${firstCity}(+${cityArray.length - 1})`
    }

    return ''
  }

  useEffect(() => {
    if(!getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')) return
    const startCity = reconsitutionValue(item?.dataLinkForm, '出发地')
    const endCity = reconsitutionValue(item?.dataLinkForm, '目的地')
    const stayCity = reconsitutionValue(item?.dataLinkForm, '住宿地')
    
    if (startCity) {
      const fetchData = async () => {
        const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityDetails(startCity, endCity)
        setStartCityStr(formatCityOrigin(enhanceFromCityData))
        setEndCityStr(formatCityOrigin(enhanceToCityData))

        setStartCityEnhance(formatCity(enhanceFromCityData))
        setEndCityEnhance(formatCity(enhanceToCityData))
      }
      fetchData()
    } else if (stayCity) {
      const fetchData = async () => {
        const { enhanceFromCityData } = await fetchAndProcessCityDetails(stayCity, [])
        setStartCityStr(formatCityOrigin(enhanceFromCityData))
        setStartCityEnhance(formatCity(enhanceFromCityData))
      }
      fetchData()
      setStartCityStr(formatCityOrigin(JSON.parse(stayCity)))
    }
  }, [item])

  const fetchAndProcessCityDetails = async (travelFromCity, travelToCity) => {
    const fromCityData = travelFromCity?.length > 0 ? (travelFromCity && JSON.parse(travelFromCity)) || [] : []
    const toCityData = travelToCity?.length > 0 ? (travelToCity && JSON.parse(travelToCity)) || [] : []

    const foreignCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        foreignCityKeys.push(city.key)
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        foreignCityKeys.push(city.key)
      }
    }

    if (foreignCityKeys.length > 0) {
      try {
        const res = await cityFetch.GET('[ids]', { ids: foreignCityKeys.join(',') })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityDetails = (cityData, cityDetails) => {
            if (!cityData || !cityDetails || !Array.isArray(cityData) || !Array.isArray(cityDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            cityDetails.forEach(cityDetail => {
              if (cityDetail.fullName && cityDetail.countryCode !== 'CN') {
                const countryName = cityDetail.fullName.split(',')[0]
                newCityData.forEach(city => {
                  if (city.key === cityDetail.id && !city.label.includes(`(${countryName})`)) {
                    city.label = `${city.label}(${countryName})`
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityDetails(fromCityData, res.items)
          const enhanceToCityData = processCityDetails(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市详情失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  // 获取日期
  const formatTripDate = item => {
    const format = i18n.currentLocale === 'en-US' ? 'MM.DD' : 'MM月DD日'
    let date = getValue(item, 'DATE')
    return moment(getValue(item, 'DATE')).format(format) + ' ' + getWeek(date)
  }
  // 获取地址
  const formatTripAddress = item => getValue(item)

  /**
   * 获取不同类型旅程 对应字段值
   * @param value 每条数据item
   * @param matchType 匹配日期还是地址
   * @returns
   */
  const getValue = (item, matchType?: string) => {
    let result = ''
    const TYPE = item?.addTypeWithIcon?.type
    const value = item?.dataLinkForm
    const inCheckInDateTravelList = checkInDateTravelList.includes(TYPE)

    if (matchType === 'DATE') {
      if (inCheckInDateTravelList) {
        result = reconsitutionValue(value, '入住日期')
      } else {
        result = reconsitutionValue(value, '行程日期')
      }
    } else {
      if(getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')){
        return (
          <>
            <Tooltip title={startCityEnhance || ''}>
              <span className="city">{startCityStr}</span>
            </Tooltip>
            {endCityStr?.length > 0 && (
              <Tooltip title={endCityEnhance || ''}>
                <span className="city">&nbsp;-&nbsp;{endCityStr}</span>
              </Tooltip>
            )}
          </>
        )
      }else{
        result = reconsitutionValue(value, 'name')
      }
    }
    return result
  }
  // 获取对象包含key中得某个字符串，得value
  const reconsitutionValue = (value, property) =>
    value[Reflect.ownKeys(value)?.find((e: string) => e.indexOf(property) > 0)]

  // render 前往商城button
  const rendereButton = item => {
    const buttonText = item.hasOrder ? i18n.get('查看订单') : i18n.get(formatTripButtonText?.(item))
    const handleClick = item.hasOrder
      ? () => handleClickOrder(item.addTypeWithIcon.type)
      : () => handleCtripToOrder?.(item.addTypeWithIcon.type)
    return (
      <div className={`trip-item-button ${loading ? '' : 'ekb-skeleton-normal ekb-skeletons-text'}`}>
        {loading && (
          <Button type="primary" onClick={handleClick} {...({} as any)}>
            {buttonText}
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={styles['trip-item-wrap']}>
      <div className={'trip-item'} key={index}>
        <div className={`trip-item-left`}>
          <div
            className={`trip-item-left-icon ${loading ? '' : 'ekb-skeleton-normal'}`}
            style={{ backgroundColor: item.addTypeWithIcon.color }}
          >
            {loading && <img src={item.addTypeWithIcon.icon} />}
          </div>
          <div className={`trip-item-left-head`}>
            <span className={`trip-item-left-head-title ${loading ? '' : 'ekb-skeleton-normal ekb-skeletons-text'}`}>
              {formatTripAddress?.(item)}
            </span>
            <span className={`trip-item-left-head-des ${loading ? '' : 'ekb-skeleton-normal ekb-skeletons-text'}`}>
              {formatTripDate?.(item)}
            </span>
          </div>
        </div>
        {rendereButton(item)}
      </div>
    </div>
  )
}

export default TripPlanningItem
