import { get, isObject } from "lodash";
import { BillActionBase, BillActionBaseProps } from "./base-action";
import { fnFormatMoneyValue, getDefSpecificationDS, getValidateErrorByShow, setFormMultiplePayeesMode, updateFormData } from "../../util/billUtils";
import { checkYegoOrder, setValidateError } from '../../bills.action'
import { ButtonProps, message, Modal } from "@hose/eui";
import { app, app as api } from "@ekuaibao/whispered";
import { getBoolVariation } from "../../../../lib/featbit";
import { endFlowFormDataCollectionStatistics } from "../../../../lib/flowPerformanceStatistics";
import { FlowItem } from "../types";
import { handleSynchronous } from "../../parts/right-part/billInfo/validateTripSync";

export class SubmitAction extends BillActionBase<BillActionBaseProps> {
  label = i18n.get('提交送审')

  constructor(props: BillActionBaseProps, buttonProps: ButtonProps) {
    super(props, buttonProps)
  }

  onAction = async () => {
    try {
      const submitDone = await this.handleSubmit()
      if (!submitDone) {
        throw new Error('fail in check submit data')
      }
    } catch (e) {
      throw e
    }
  }

  handleSubmit = async () => {
    const { backlog, bus } = this.props
    const dataSource = get(backlog, 'flowId') || {} as FlowItem
    // 1. 检查费用明细是否在编辑中
    // @ts-ignore
    if (bus.$isTableEdit) {
      message.info(i18n.get('保存完当前编辑的费用再进行其他操作'))
      return false
    }
    // 2. 确保单据模板数据存在
    const specificationGroupsList = app.getState('@custom-specification').specificationGroupsList
    if (specificationGroupsList && specificationGroupsList?.length === 0) {
      await api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned')
    }

    // 3. 确保按钮没有在加载或禁用状态
    if (this.buttonProps.loading || this.buttonProps.disabled) {
      return false
    }

    // 4. 获取当前单据模板
    const currentSpecification = getDefSpecificationDS(dataSource, specificationGroupsList)

    // 5. 确保单据模板为最新版本
    if (currentSpecification?.mustUpdateTemplate && !currentSpecification?.active) {
      Modal.error({ title: i18n.get('未更新模板'), content: i18n.get('此单据未更新模板,无法提交') })
      return false
    }

    // 6. 校验hab组件
    const habError = await this._handleForHabForms()
    if (habError.length) {
      const { habform, err } = habError[0]
      habform.scrollToField(err.errorFields[0].name[0])
      message.error(err.errorFields[0].errors[0])
      return false
    }

    const components = get(dataSource, 'currentSpecification.components') || get(dataSource, 'form.specificationId.components') || []
    try {
      let formValue = await bus.getValueWithValidate(0)

      if (getBoolVariation('ao-84-optimize-datalinkedittable', false)) {
        await this._datalinkEditValidate(bus, components, formValue)
      }

      this.checkTrips(formValue)

      formValue = await this.checkLedgerCalcEnd(formValue)

      await this.checkYegoOrder(formValue)

      formValue = await fnFormatMoneyValue({ data: formValue, specification: get(dataSource, 'currentSpecification') || get(dataSource, 'form.specificationId') })

      setFormMultiplePayeesMode(formValue, dataSource)

      updateFormData(dataSource, formValue)

      await this.setButtonStatue(true)

      // @ts-ignore
      if (bus.$showType === 'TABLE_NO_GROUP') {
        // 费用类型表格编辑埋点
        // @ts-ignore
        api?.logger.info(`费用类型表格编辑保存草稿单据埋点`, { formValue })
      }
      endFlowFormDataCollectionStatistics()
      const res = await bus.invoke('submit:bill:click', formValue)

      // 有返回值时，表明提交成功，否则提交失败
      await this.setButtonStatue(false)
      if (res) {
        await this.syncTrip(res, dataSource)
        return true
      } else {
        return false
      }
    } catch (e) {
      await this.setButtonStatue(false)
      this._handleErrorForFields(e, components)
      this._toastErrorInfo(e)
      throw e
    }
  }

  delayForRender = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true)
      }, 0)
    })
  }

  setButtonStatue = async (active: boolean) => {
    this.buttonProps.loading = active
    this.buttonProps.disabled = active
    await this.delayForRender()
  }

  _datalinkEditValidate = async (bus: any, components: any, fromValue: any) => {
    const dataLinkEdits = components.filter(v => v.type === 'dataLinkEdits' && v.showType === 'TABLE' && ['INSERT', 'UPDATE', 'MORE'].includes(v.behaviour))
    const allDataLinkEdits = dataLinkEdits.map((field: any) => {
      if (bus.has(`submit:bill:click:validate:${field?.field}`)) {
        return bus.invoke(`submit:bill:click:validate:${field?.field}`)
      }
      return Promise.resolve()
    })
    const allDataLinkEditsRes = await Promise.all(allDataLinkEdits)
    if (allDataLinkEditsRes.some(v => v === 'failed')) {
      throw new Error('提交失败')
    }
  }

  _toastErrorInfo = (e: any) => {
    if (e && (e['details'] || e['trips'] || e['remuneratin'])) {
      let { errors = [] } = e['details'] || e['trips'] || e['remuneratin']
      message.error(errors[0]?.message)
    }
  }

  _setFieldsValidateError = ({ bill, detail, trip }: { bill?: string[], detail?: string[], trip?: string[] }) => {
    api.dispatch(setValidateError({ bill, detail, trip }))
  }

  _handleErrorForFields = (e: any, components: any) => {
    const errors = getValidateErrorByShow(components, Object.keys(e))
    if (!errors.length) {
      this._setFieldsValidateError({ bill: Object.keys(e) })
    }
  }

  // copy from RightPartFooter.js @liudan
  _handleForHabForms = async () => {
    //单据上有hab组件需要做hab里的表单校验
    const { bus } = this.props
    const habError = []
    // @ts-ignore
    if (bus?.euiHabForms) {
      const validateAndHandleErrors = async habform => {
        await habform.validateFields().catch(err => {
          habError.push({ habform, err })
        })
      }
      // @ts-ignore
      await Promise.all(Object.values(bus.euiHabForms).map(habform => habform && validateAndHandleErrors(habform)))
    }
    return habError
  }

  checkTrips = (formValue: any) => {
    const trips = get(formValue, 'trips', [])

    if (trips.length === 0) {
      return
    }

    for (let item of trips) {
      const {
        specificationId: { components },
        tripForm
      } = item
      for (let i of components) {
        const { optional, field } = i
        if (optional !== undefined && !optional && !tripForm[field]) {
          throw { trips: { errors: [{ message: i18n.get('行程信息填写不完整') }] } }
        }
      }
    }
  }

  checkLedgerCalcEnd = (formValue: any) => {
    return api.invokeService('@remuneration:check:ledger:calcEnd', {
      props: this.props,
      formValue,
      isShowRemunerationTab: app.getState('@remuneration').isShowRemunerationTab
    })
  }

  checkYegoOrder = async (formValue: any) => {
    const { expenseLink, details } = formValue
    if (expenseLink && api.getState('@common').powers.YEEGO) {
      const data = await checkYegoOrder({
        // @ts-ignore
        expenseLink: isObject(expenseLink) ? expenseLink.id : expenseLink,
        details
      })
      if (data.value) {
        return
      } else {
        Modal.confirm({
          title: i18n.get('单据关联的申请存在订单未导入,是否提交?'),
          onOk: () => {},
          onCancel: () => {
            throw new Error()
          }
        })
      }
    }
  }


  syncTrip = async (res: any, dataSource: FlowItem) => {
    const { state, id, params, current } = res || {} as any
    const { bus } = this.props
    const { formType } = dataSource
    const supplierBills = ['reconciliation', 'settlement', 'payment']
    const isRouteReconciliation = location.hash.endsWith('/reconciliation')
    // 增加自动同步行程
    let isEnableDraftConfig = await bus.invoke('get:enableDraftConfig:value')
    if (isEnableDraftConfig) {
      const hasTrip = get(current, 'flow.form.u_行程规划')?.length ? true : false
      if (hasTrip) handleSynchronous(id, 'freeflow.submit')
    }
    // @ts-ignore
    if ((supplierBills.includes(formType) && isRouteReconciliation) || bus.$_fromPlanQuery) {
      if (state === 'success') {
        api.emit('update:reconiliation:data')
        // scanViewData && scanViewData.fn()
        // this.onCloseModal(true)
      } else {
        api.emit('update:reconiliation:bill', { ...params, id, state: 'draft' })
      }
    } else {
      // scanViewData && scanViewData.fn(res) // 弹窗关闭
      // if (state === 'success' || get(current, 'flow.state') === 'pending') {
      //   return
      // }
    }
  }
}