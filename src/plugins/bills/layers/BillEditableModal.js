/**************************************
 * Created By LinK On 2019/2/14 16:35.
 **************************************/
import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import MessageCenter from '@ekuaibao/messagecenter'
import BillInfoEditableContainer from '../parts/right-part/billInfo/BillInfoEditableContainer'
import RightPartFooter from '../parts/right-part/billInfo/RightPartFooter'
import { app as api } from '@ekuaibao/whispered'

@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class BillEditableModal extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = props.bus || new MessageCenter()
    this.state = {
      data: props.data,
      riskData: props.riskData || {}
    }
  }

  updateBillData = data => {
    const { id } = data
    if (id) {
      api.invokeService('@bills:get:flow-info', { id }).then(result => {
        const dataSource = result.value
        api.invokeService('@bills:get:flow:risk:warning', { id, level: 'OutOfLimitReject' }).then(riskData => {
          riskData.isForbid = true
          this.setState({ data: dataSource, riskData })
        })
      })
    }
  }

  componentWillMount() {
    api.on('update:reconiliation:bill', this.updateBillData)
  }

  componentWillUnmount() {
    api.un('update:reconiliation:bill', this.updateBillData)
  }

  handleCancel = () => {
    api.emit('update:reconiliation:data', { isCancel: true })
    this.props.onCancel && this.props.onCancel()
  }

  render() {
    const { data, riskData, showUpDown } = this.props
    return (
      <div className="dis-f fd-c flex-1" style={{ width: '100%', height: '100%' }}>
        <BillInfoEditableContainer bus={this.bus} dataSource={data} riskData={riskData} inModal showUpDown={showUpDown} />
        <RightPartFooter bus={this.bus} dataSource={data} scanViewData={{ fn: this.handleCancel }} scene={'OWNER'}/>
      </div>
    )
  }
}
