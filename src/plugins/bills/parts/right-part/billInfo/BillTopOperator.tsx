import React, { useState, useRef } from 'react'
import { <PERSON><PERSON>, Tooltip } from '@hose/eui';
import { OutlinedTipsClose, OutlinedDirectionUp, OutlinedDirectionDown } from '@hose/eui-icons'
import FullscreenComp from '../../../../../elements/puppet/details/FullscreenComp';
import styles from './BillTopOperator.module.less'
import { useBillDetailsV2 } from '../../../bill-details-new/context'

interface BillTopOperatorProps {
  showHeaderClose?: boolean
  showFullScreenDrawer?: boolean
  bus: any
  layer: any
  showUpDown?: boolean
  onUpDown?: (type: 'prev' | 'next') => void
}

const BillTopOperator = ({
  showHeaderClose = true,
  showFullScreenDrawer = true,
  showUpDown = false,
  onUpDown,
  bus,
  layer,
}: BillTopOperatorProps) => {
  const [isFullscreen, setIsFullscreen] = useState(false)
  const { goPrev, goNext, disabledUpDown } = useBillDetailsV2()
  const containerRef = useRef<HTMLDivElement>(null)


  const handleChangeModalSize = () => {
    const anchor = containerRef.current
    if (!anchor) return
    // 通过closest获取最近的drawer-content-wrapper
    const wrapper = anchor.closest('.ant-drawer-content-wrapper, .eui-drawer-content-wrapper') as HTMLElement | null
    if (!wrapper) return
    if (isFullscreen) {
      wrapper.classList.remove('fullScreenDrawer')
    } else {
      wrapper.classList.add('fullScreenDrawer')
    }
    // 为配合实现单据多列的响应式布局，触发mbox上的windowSizeChange方法
    window.onresize(undefined)
    setIsFullscreen(!isFullscreen)
  }

  const handleClose = ()=>{
    bus.invoke('check:value:changed').then(
      () => {
        layer?.emitCancel()
      },
      (error: string) => {
        if (error === 'cancel') return
        layer?.emitCancel()
      }
    )
  }

  const handleGoPrev = () => {
    onUpDown ? onUpDown('prev') : goPrev()
  }

  const handleGoNext = () => {
    onUpDown ? onUpDown('next') : goNext()
  }

  return (
    <section className={styles['bill-top-operator']} ref={containerRef}>
      <div className={styles.left}>
        {showUpDown && (
          <>
            <Tooltip title={i18n.get('上一条')}>
              <Button category='text' disabled={disabledUpDown.includes('prev')} icon={<OutlinedDirectionUp />} onClick={handleGoPrev}></Button>
            </Tooltip>
            <Tooltip title={i18n.get('下一条')}>
              <Button category='text' disabled={disabledUpDown.includes('next')} icon={<OutlinedDirectionDown />} onClick={handleGoNext}></Button>
            </Tooltip>
          </>
        )}
      </div>
      <div className={styles['right']}>
        {showFullScreenDrawer && (
          <span className="mr-8">
            <FullscreenComp label={''} isFullscreen={isFullscreen} onFullscreen={handleChangeModalSize} fontSize={16} />
          </span>
        )}
        {showHeaderClose && <Tooltip title={i18n.get('关闭')}>
          <Button category='text' icon={<OutlinedTipsClose />} onClick={handleClose}></Button>
        </Tooltip>}
      </div>
    </section>
  )
}

export default BillTopOperator
