/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/27.
 */
import React, { PureComponent } from 'react'
import { app as api } from '@ekuaibao/whispered'
import classNames from 'classnames'
import BillInfoReadOnlyContainer from '../../parts/right-part/billInfo/BillInfoReadOnlyContainer'
import BillInfoFooter from '../../layers/bill-info-popup/BillInfoFooter'
import RightPartFooter from '../../parts/right-part/billInfo/RightPartFooter'
import MessageCenter from '@ekuaibao/messagecenter'
import messageCenter from '@ekuaibao/lib/lib/message-center'
import { getNodeValueByPath } from '@ekuaibao/lib/lib/lib-util'
import { showMessage, showModal } from '@ekuaibao/show-util'
import { EnhanceConnect } from '@ekuaibao/store'
import { ACTIONBUTTONS } from '../util'
import { delFlow, retractFlow, setValidateError } from '../../bills.action'
import { get, cloneDeep } from 'lodash'
import BillInfoEditableContainer from '../../parts/right-part/billInfo/BillInfoEditableContainer'
import {
  formatCopyBillData,
  confirmCopy,
  setFormMultiplePayeesMode,
  fnPushDingTalkShareBtn
} from '../../util/billUtils'
import { related } from '../../../../elements/feeDetailViewList/Related'
import { provider } from '@ekuaibao/react-ioc'
import { PermissionVm } from '../../vms/Permission.vm'
import styles from './NewBillInfoView.module.less'
import { enableCustomExtendButton } from '../../../../lib/featbit'
import { BillFooter } from '../../bill-details-new/BillFooter'

const okText = i18n.get('确认')
const cancelText = i18n.get('取消')

@EnhanceConnect(
  state => ({
    userInfo: state['@common'].userinfo.data,
    staffs: state['@common'].staffs,
    CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
    budgetPower: state['@common'].powers.Budget,
    autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
    nullifyPower: state['@common'].powers.KA_DOCUMENT_VOID
  }),
  { delFlow, retractFlow, setValidateError }
)
@provider(['permission', PermissionVm])
export default class NewBillInfoView extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {}
    this.buttonMap = {
      'freeflow.print': this.handlePrint,
      'freeflow.comment': this.handleComment,
      'freeflow.delete': this.handleDel,
      'freeflow.retract': this.handleRetract,
      'freeflow.submit': this.handleSubmit,
      'freeflow.edit': this.handleSave,
      'freeflow.urge': this.handleReminde,
      'freeflow.copy': this.handleCopy,
      'freeflow.read': this.handleRead,
      'freeflow.printed': this.handlePrinted
    }
    this.lastTime = 0
    this.bus = props.bus || new MessageCenter()
  }

  handleRead = backlog => {
    const flowData = backlog.flowId
    api.invokeService('@audit:marked:read', { ids: [flowData.id] }).then(_ => {
      showMessage.success(i18n.get('操作成功'))
      this.reloadData()
    })
  }

  handlePrinted = async backlog => {
    const { handleActionImplementation } = await api.require('@audit/service').load()
    handleActionImplementation.call(this, {
      type: 16,
      backlog,
      fn: this.reloadData()
    })
  }

  handleCopy = backlog => {
    const { flowId } = backlog
    const flow = cloneDeep(flowId)
    const details = get(flow, 'form.details')
    if (details?.length) {
      details.forEach(el => {
        delete el?.feeTypeForm?.detailNo
      })
    }
    related.clearRelatedData()
    confirmCopy(flow).then(async _ => {
      const data = await formatCopyBillData(flow)
      api.open('@bills:BillEditableModal', { data })
    })
  }

  componentDidMount() {
    api.dataLoader('@common.staffs').load()
    messageCenter.on('on-click-modify-flow', this.handleModifyFlowChange)
    this.bus.watch('bills:update:flow', this.handleBillUpdate)
    this.handleCheckNulllify()
  }

  componentWillUnmount() {
    messageCenter.un('on-click-modify-flow', this.handleModifyFlowChange)
    this.bus.un('bills:update:flow', this.handleBillUpdate)
  }

  handleSave = () => {
    const { backlog = {} } = this.props
    this.bus
      .getValueWithValidate(1)
      .then(formValue => {
        this.setState({ saveLoading: true })
        setFormMultiplePayeesMode(formValue, get(backlog, 'flowId'))
        return this.bus.invoke('save:bill:click', formValue)
      })
      .then(_ => {
        this.setState({ saveLoading: false })
        this.reloadData()
      })
      .catch(e => {
        //校验失败
        this.setState({ submitLoading: false })
        if (e && (e['details'] || e['trips'])) {
          let { errors = [] } = e['details'] || e['trips']
          let { message } = errors[0]
          showMessage.error(message)
        }
      })
  }

  handleSubmit = () => {
    const { backlog = {} } = this.props
    this.bus
      .getValueWithValidate()
      .then(formValue => {
        setFormMultiplePayeesMode(formValue, get(backlog, 'flowId'))
        this.setState({ submitLoading: true })
        return this.bus.invoke('submit:bill:click', formValue)
      })
      .then(res => {
        this.setState({ submitLoading: false }, () => {
          !!res && this.reloadData()
        })
      })
      .catch(e => {
        //校验失败
        console.log(e)
        this.setState({ submitLoading: false })
        if (e && (e['details'] || e['trips'])) {
          let { errors = [] } = e['details'] || e['trips']
          let { message } = errors[0]
          showMessage.error(message)
        }
      })
  }
  handleDel = backlog => {
    let { delFlow } = this.props
    let { id } = backlog.flowId
    let _this = this
    showModal.confirm({
      title: i18n.get('您是否确认要删除该单据?'),
      className: 'confirmDelModal-wrapper',
      content: '',
      okText: i18n.get('删除'),
      cancelText: cancelText,
      onOk() {
        if (id) {
          delFlow(id).then(_ => {
            _this.reloadData()
          })
        }
      }
    })
  }
  handleRetract = backlog => {
    let { retractFlow } = this.props
    let { id } = backlog.flowId
    let _this = this
    showModal.confirm({
      title: i18n.get('您是否确认要撤回该单据?'),
      okText: okText,
      cancelText: cancelText,
      onOk() {
        retractFlow(id).then(action => {
          if (!action.error) {
            _this.reloadData()
          }
        })
      }
    })
  }

  handleBillUpdate = (needNotClose = false) => {
    let { invokeService, params, reload } = this.props
    this.reloadData(needNotClose)
    return api.invokeService(invokeService, params).then(data => {
      const value = data.value
        ? data.value.flowId
          ? data.value
          : { flowId: data.value }
        : data.flowId
        ? data
        : { flowId: data }
      this.setState({ backlog: value })
      reload && reload()
      return { backlog: value }
    })
  }
  handleReminde = backlog => {
    let newTime = new Date().valueOf()
    if (newTime - this.lastTime > 60000) {
      //60秒内只能执行一次催办功能
      this.fnReminde(backlog)
      this.lastTime = newTime
    } else {
      showMessage.warning(i18n.get('操作频繁'))
    }
  }

  fnReminde = backlog => {
    let {
      flowId: { id, plan }
    } = backlog
    let { taskId } = plan
    api.invokeService('@bills:bill:reminde', id, taskId).then(_ => {
      this.reloadData()
    })
  }

  handleModifyFlowChange = (node, flow, isVisibilityStaffs) => {
    let { backlog } = this.props
    api
      .open('@organizationManagement:SelectStaff', {
        title: i18n.get('选择人员'),
        staffLimitData: node.isAllStaffs ? undefined : node.staffIds,
        required: true,
        fetchDataSourceAction: {
          staff: api.invokeService('@organizationManagement:get:visibility:staffs'),
          department: api.invokeService('@organizationManagement:get:visibility:departments')
        },
        data: [
          {
            type: 'department-member',
            checkIds: node?.approverId?.id ? [node.approverId.id] : []
          }
        ]
      })
      .then(checkedList => {
        const staffData = checkedList[0]
        const staffs = staffData?.checkIds
        const staffId = staffs[0]
        const params = {
          id: node.id,
          flowId: flow.id,
          approverId: staffId,
          name: 'freeflow.select.approver'
        }
        api
          .invokeService('@audit:set:plan-node', params)
          .then(_ =>
            api.invokeService('@audit:get:backlog-info', {
              id: backlog.id,
              type: backlog.type
            })
          )
          .then(backlog => {
            this.setState({ backlog })
          })
      })
  }
  handleCloseDrawer = reload => {
    //复制单据时不需要emitCancel
    const { callBack, layer } = this.props
    if (reload) layer.emitCancel()
    callBack && callBack()
    api.invokeService('@layout5:refresh:menu:data')
  }
  handleCheckNulllify = () => {
    const { nullifyPower, backlog } = this.props
    const { id, state } = backlog?.flowId || {}
    if (nullifyPower && state && state !== 'new') {
      return api.invokeService('@bills:get:check:nulllify:rule', { specId: id, state }).then(data => {
        this.setState({ canNullify: { done: true, value: data?.value } })
      })
    } else {
      this.setState({ canNullify: { done: true, value: false } })
    }
  }
  handleGetBillNewData = () => {
    let { invokeService, params } = this.props
    return api.invokeService(invokeService, params)
  }
  //审批条件
  checkApproveCondition(doc) {
    if (this.props.isShowCondition && doc.state !== 'pending') {
      let { plan } = doc
      if (!plan) return null
      let nodes = plan.nodes
      let conditions = []
      let taskId = plan.taskId
      let { userInfo } = this.props
      nodes.forEach(node => {
        if (taskId === node.id && node.agreeType === 'FAILED_AUTO_AGREE') {
          //自动同意失败
          conditions.push({ message: i18n.get('自动同意失效，需要您指定下一审批人') })
        }
        if (taskId === node.id && node.conditionalDescription && node.conditionalDescription.length) {
          let flag = node.type !== 'countersign' ? node.approverId && node.approverId.id === userInfo.staff.id : false
          let conditionalDescription = flag
            ? node.conditionalDescription.replace(i18n.get('需要此环节审批'), i18n.get('需要您审批'))
            : node.conditionalDescription
          if (node.config.showConditionInBill) conditions.push({ message: conditionalDescription })
        }
      })
      return conditions
    }
    return null
  }

  reloadData = (needNotClose = false) => {
    if (!needNotClose) {
      const { callBack } = this.props
      const autoClose = get(this.props, 'params.autoClose', false)
      if (autoClose) this.props.layer.emitCancel()
      callBack && callBack()
    }
    api.invokeService('@layout5:refresh:menu:data')
  }

  handlePrint = backlog => {
    const { doPrint } = api.require('@audit/service-print')
    let dataSource = backlog.flowId
    let obj = api.invokeService('@share:get:print:param', dataSource)
    let data = [obj]
    doPrint(data, false, this.reloadData)
  }

  handleComment = backlog => {
    api.open('@bills:BillCommentModal', { flow: backlog.flowId }).then(params => {
      let { currRoleForMine } = this.props
      let args = { params, id: backlog.flowId.id }
      if (currRoleForMine) {
        args.privilegeId = currRoleForMine.id
      }
      api.invokeService('@bills:comment:flow', args).then(this.handleBillUpdate)
    })
  }

  onFooterButtonsClick = async (type, line) => {
    const { handleActionImplementation } = await api.require('@audit/service').load()
    handleActionImplementation.call(this, {
      type,
      backlog: line,
      fn: () => {
        this.reloadData()
      }
    })
  }
  onOpenOwnerLoanList = async (type, line) => {
    const { handleActionImplementation } = await api.require('@audit/service').load()
    handleActionImplementation.call(this, { type, backlog: line })
  }

  renderRiskTips() {
    let { riskTip } = this.props
    let backlog = this.state.backlog || this.props.backlog
    let { flowId } = backlog
    let approveCondition = this.checkApproveCondition(flowId)
    let tips = []
    if (
      flowId.state === 'approving' ||
      flowId.state === 'paying' ||
      flowId.state === 'receiving' ||
      flowId.state === 'sending'
    ) {
      if (approveCondition && approveCondition.length > 0) {
        approveCondition.forEach((item, index) => {
          tips.push(
            <div
              style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}
              key={index}
            >
              {item.message}
            </div>
          )
        })
      }
    }

    if (flowId.state !== 'rejected' && flowId.state !== 'draft') {
      const details = getNodeValueByPath(flowId, 'form.details', [])
      details.find((item, index) => {
        if (item.feeTypeForm.invoiceForm && item.feeTypeForm.invoiceForm.type === 'wait') {
          return tips.push(
            <div
              key={`${index}-invoice`}
              style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}
            >
              {i18n.get('有「待开发票」的消费')}
            </div>
          )
        }
      })
    }

    if (riskTip) {
      tips.push(
        <div
          style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}
          key={tips.length}
        >
          {riskTip}
          {
            <div
              onClick={e => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              <a onClick={() => this.onOpenOwnerLoanList(9, backlog)}>{i18n.get('点击查看')}</a>
            </div>
          }
        </div>
      )
    }

    return tips
  }

  getActionsFooter(actions, backlog) {
    let footer = []
    const forbidOwnerPrint = get(backlog, 'flowId.form.forbidOwnerPrint', false)
    actions.forEach(item => {
      let itemaction = item.action || item
      if (
        itemaction === 'freeflow.copy' &&
        this.props.autoExpenseWithBillStriction &&
        backlog?.flowId?.formType === 'expense'
      ) {
        return
      }
      let buttonAction = this.buttonMap[itemaction]
      if (ACTIONBUTTONS[itemaction]) {
        if (buttonAction) {
          let button = ACTIONBUTTONS[itemaction](buttonAction, backlog)
          if (button) {
            if (!forbidOwnerPrint && itemaction === 'freeflow.print') {
              footer.push({ ...item, ...button })
            } else if (itemaction !== 'freeflow.print') {
              footer.push({ ...item, ...button })
            }
          }
        } else {
          let button = ACTIONBUTTONS[itemaction](this.onFooterButtonsClick, backlog)
          if (button) {
            footer.push({ ...item, ...button })
          }
        }
      }
    })

    return footer
  }

  isModal = () => {
    return this.props.enhancer === 'modal'
  }

  postAction = (actionName) => {
    if (actionName === 'freeflow.delete') {
      this.props.layer.emitCancel()
    }
  }

  renderFooterActionsV2 = (dataSource) => {
    const { from, layer } = this.props;
    if (!dataSource) return null;
    return (
      <BillFooter
        bus={this.bus}
        flow={dataSource}
        layer={layer}
        forceUpdateFlow={() => this.handleBillUpdate(true)}
        postAction={this.isModal() ? this.postAction : undefined}
        from={from}
        closeAfterAction
      />
    )
  }

  render() {
    let {
      isEditConfig,
      onOpenOwnerLoanList,
      backlogType,
      buttons,
      source,
      stackerManager,
      userInfo,
      riskTip,
      from,
      hasPopupTitle,
      setValidateError,
      showHeaderClose,
      showFullScreenDrawer,
      layer,
      customStyle = {},
      scene,
    } = this.props
    const { canNullify } = this.state
    let backlog = this.state.backlog || this.props.backlog
    if (this.state.backlog && this.props.backlog && this.props.backlog.id === -1) {
      this.state.backlog.id = -1
    }
    let { flowId } = backlog
    if (!flowId) {
      return false
    }

    let { state } = flowId
    let footerButtons = buttons[userInfo.staff.id] || []
    let isCarbonCopy = backlogType && backlogType === 'carbonCopy'
    let isSuppleInvoiceBtn = flowId.state !== 'rejected' && flowId.state !== 'draft'
    let footer = this.getActionsFooter(footerButtons, backlog)
    flowId.countDownDuration = backlog.countDownDuration
    flowId.autoApproveType = backlog.autoApproveType

    let ownerId = typeof flowId.ownerId === 'object' ? flowId.ownerId.id : flowId.ownerId
    const isEditState = state === 'new' || state === 'draft' || state === 'rejected'
    const canEdit = ownerId === userInfo.staff.id && isEditState

    if (!canEdit) {
      fnPushDingTalkShareBtn(footer, get(flowId, 'id'))
    }

    //----判断我自己是不是寄单节点,如果不是寄单节点不能增加和编辑寄送信息-----
    const plan = flowId && flowId.plan
    const nodes = (plan && plan.nodes) || []
    const expressNode = nodes.find(node => node.expressConfig && node.expressConfig.type === 'send')
    let isSend = expressNode && expressNode.approverId && expressNode.approverId.id === userInfo.staff.id
    if (!isSend && expressNode && expressNode.counterSignersCandidate) {
      expressNode.counterSignersCandidate.forEach(signer => {
        if (!isSend) isSend = signer.signerId === userInfo.staff.id
      })
    }
    const isModal = this.isModal()

    const footerEl =
      from === 'from_myBill' ? (
        <RightPartFooter
          bus={this.bus}
          dataSource={flowId}
          from={from}
          setValidateError={setValidateError}
          closeDrawer={this.handleCloseDrawer}
          canNullify={canNullify}
          fnGetBillNewData={this.handleGetBillNewData}
          scene={scene}
          actionMap={this.buttonMap}
        />
      ) : (
        enableCustomExtendButton() ? this.renderFooterActionsV2(flowId) :
        <BillInfoFooter
          footer={footer}
          flowId={flowId?.id}
          scene={scene}
          actionMap={this.buttonMap}
          needConfigButton={true}
        />
      )

    return (
      <div
        id={'NewBillInfoView'}
        style={customStyle}
        className={classNames(styles['new-bill-info-view'], {
          [styles['new-bill-info-view-modal']]: isModal
        })}
      >
        {canEdit ? (
          <BillInfoEditableContainer
            bus={this.bus}
            dataSource={flowId}
            riskData={riskTip}
            isModal={isModal}
            isNewSearchInfo={true}
            showHeaderClose={showHeaderClose}
            showFullScreenDrawer={showFullScreenDrawer}
            layer={layer}
            billFooter={footerEl}
          />
        ) : (
          <BillInfoReadOnlyContainer
            onOpenOwnerLoanList={onOpenOwnerLoanList && onOpenOwnerLoanList.bind(this, backlog)}
            renderRiskTips={this.renderRiskTips()}
            bus={this.bus}
            showExpressButton={isSend}
            dataSource={flowId}
            suppleInvoiceBtn={isSuppleInvoiceBtn}
            source={source}
            stackerManager={stackerManager}
            isModal={isModal}
            showHeaderClose={showHeaderClose}
            showFullScreenDrawer={showFullScreenDrawer}
            isEditConfig={isEditConfig}
            isShowWarningTips={isCarbonCopy}
            isNewSearchInfo={this.props.params.isNewSearchInfo || false}
            hasPopupTitle={hasPopupTitle}
            layer={layer}
            billFooter={footerEl}
          />
        )}
      </div>
    )
  }
}
