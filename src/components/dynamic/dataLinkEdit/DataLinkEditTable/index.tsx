import { app as api } from '@ekuaibao/whispered'
import React, { Component } from 'react'
import styles from './DataLinkEditTable.module.less'
import { cloneDeep, divide, isEqual } from 'lodash'
import DataLinkEditHeader from '../DataLinkEditHeader'
import EditTableContainer from './EditTableContainer'

import { parseAsMeta } from '@ekuaibao/lib/lib/parse'
import { getBoolVariation } from '../../../../lib/featbit'
interface IProps {
  isEdit: boolean
  field: any
  dataSource: any[]
  templates: any[]
  templatesold: any[]
  onChange: (data: any) => void
  onSelect?: () => void
  flowBus: any
  bus: any
  flowFormFieldsValue?:any
  onFieldEdit?: (data: any, index: any) => void
  editingKey?: string | number
  onGetActiveDataIndex: (editingKey: string | number) => void
  isHabSetValue?: boolean
}

interface IState {
  data: any[]
  dataLinkTemplateId: string
  oldDataLinkTemplateId: string
  template: any
  templates: any[]
  current: number
  pageSize: number
}
class DataLinkEditTable extends Component<IProps, IState> {
  constructor(props) {
    super(props)
    this.state = {
      data: props.dataSource || [],
      templates: [],
      template: props.field?.name === 'budgetAdjustDetails' ? [] : {},
      dataLinkTemplateId: '',
      oldDataLinkTemplateId: '',
      current: 1,
      pageSize: 5
    }
  }

  componentWillMount() {
    api.dataLoader('@common.staffs').load()
  }

  componentDidMount(): void {
    this.fnUpdateProps(this.props.templates, this.props.templatesold, this.props.dataSource)
  }

  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (
      nextProps.templates.length != this.props.templates.length ||
      nextProps.templatesold.length != this.props.templatesold.length ||
      !isEqual(nextProps.dataSource, this.props.dataSource)
    ) {
      this.fnUpdateProps(nextProps.templates, nextProps.templatesold, nextProps.dataSource)
    }
  }

  fnUpdateProps = (templates, templatesold, dataSource) => {
    if (this.props.field?.name === 'budgetAdjustDetails') {
      this.setState({ template: templates, data: dataSource })
      return
    }
    let { dataLinkTemplateId, oldDataLinkTemplateId } = this.state
    this.setState({ data: cloneDeep(dataSource) })
    let arrp = templates.filter(i => {
      return i.entity.parentId === ''
    })
    // @ts-ignore
    let arrt = [...templates]
    if (dataLinkTemplateId !== '') {
      let obj = templatesold.find(t => t.templateId === dataLinkTemplateId)
      let oldobj = templatesold.find(t => t.templateId === oldDataLinkTemplateId)
      if (oldobj && oldobj.entity.active === false) {
        arrt.push(oldobj)
      }
      let newst = {} as any

      if (obj && obj.components) {
        newst.template = cloneDeep(obj)
      } else if (arrp.length > 0) {
        newst.template = cloneDeep(arrp[0])
        newst.dataLinkTemplateId = arrp[0].templateId
      }
      this.setState({
        ...newst,
        templates: arrt
      })
    } else {
      if (arrt.length == 0 && arrp.length > 0) {
        this.setState({
          dataLinkTemplateId: arrp[0].templateId,
          template: cloneDeep(arrp[0]),
          templates: []
        })
      } else if (arrt.length > 0) {
        this.setState({
          dataLinkTemplateId: arrt[0].templateId,
          template: cloneDeep(arrt[0]),
          templates: arrt
        })
      }
    }
  }

  handleGetData = (data, index) => {
    const { onChange, onGetActiveDataIndex, dataSource, field } = this.props
    const newData = cloneDeep(data).map(item => {
      const dataLinkId = item.dataLinkId || null
      const dataLinkTemplateId = item.dataLinkTemplateId
      delete item.disabledByState
      delete item.dataLinkId
      delete item.dataLinkTemplateId
      delete item.index
      return {
        dataLinkId,
        dataLinkTemplateId: dataLinkTemplateId || this.state.dataLinkTemplateId,
        dataLinkForm: item
      }
    })
    const otherDataLinkTemplateIdData = dataSource.filter(it => it.dataLinkTemplateId !== this.state.dataLinkTemplateId)
    onChange?.(newData.concat(otherDataLinkTemplateIdData))
    onGetActiveDataIndex?.(index)
  }

  handleApportionFormChange = (data) => {
    const {onChange} = this.props
    console.log(this.props, data)
    const _data= this.props.dataSource.map((item,index) => ({
      ...item,
      ...data[index]
    }))
    onChange?.(_data)
  }

  handleChangeId = dataLinkTemplateId => {
    const { templates } = this.state
    const template = templates.find(t => t.templateId === dataLinkTemplateId)
    this.setState({ dataLinkTemplateId, template })
  }

  getDataSource = (data, dataLinkTemplateId, name, template) => {
    const parentId = template?.entity?.parentId
    const dataSource = data
      .filter(item => item.dataLinkTemplateId === dataLinkTemplateId)
      .map((item, index) => {
        if (name === 'E_system_payment_plan_write') {
          this.props.flowBus.$_settleAmountTotal = data.reduce((pre, curr) => {
            return pre + Number(curr.dataLinkForm[`E_${parentId}_settleAmount`]?.standard) || 0
          }, 0)
        }
        const disabledByState = ['COR_SETTLE_PENDING', 'COR_SETTLE_PAYING', 'COR_SETTLE_PAID'].includes(
          item.dataLinkForm[`E_${parentId}_settleState`]
        )
        const { dataLinkForm = {}, dataLinkId, dataLinkTemplateId, ...other } = item
        return { ...dataLinkForm, dataLinkId, dataLinkTemplateId, disabledByState, index, ...other }
      })
    return dataSource
  }

  handleFieldEdit = (row, index) => {
    const { onFieldEdit } = this.props
    const cloneRow = cloneDeep(row)
    const dataLinkId = cloneRow.dataLinkId || null
    const dataLinkTemplateId = cloneRow.dataLinkTemplateId
    delete cloneRow.disabledByState
    delete cloneRow.dataLinkId
    delete cloneRow.dataLinkTemplateId
    delete cloneRow.index
    const data = {
      dataLinkId,
      dataLinkTemplateId: dataLinkTemplateId || this.state.dataLinkTemplateId,
      dataLinkForm: cloneRow
    }
    onFieldEdit?.(data, index)
  }

  formatComponents = template => {
    const { field } = this.props
    const fields = template?.tableTemplate?.selectedFields || []
    const components = template?.tableTemplate?.components || []
    const editableFields = template?.tableTemplate?.editableFields || []
    let parseComponents = parseAsMeta({ components }, cloneDeep(fields), true)
    const editableFieldsMap = {}
    editableFields.forEach(it => {
      editableFieldsMap[it?.name] = it
    })
    parseComponents.forEach(item => {
      if (!editableFieldsMap[item.name]) {
        item.editable = false
      }
    })
    const LIST = ['出发地', '目的地', '途经地', '起始时间', '结束时间', '行驶总时间', '补助金额']
    if (field?.referenceData?.platformId?.type === 'PRIVATE_CAR') {
      parseComponents = parseComponents.filter(item => {
        const lastName = item?.field?.split('_')?.[2]
        return LIST.includes(lastName)
      })
    }
    return parseComponents
  }

  handlePageChange = (current, pageSize) => {
    this.setState({ current, pageSize })
  }

  handleJumpLastPage = (total) => {
    const { pageSize } = this.state
    this.setState({ current: Math.ceil(total / pageSize), pageSize })
  }

  render() {
    const { templates, template, dataLinkTemplateId, data, current, pageSize } = this.state
    const {
      field,
      isEdit,
      field: { name, behaviour },
      flowFormFieldsValue,
      bus,
      onSelect,
      onFieldEdit,
      editingKey,
      onGetActiveDataIndex,
      external,
      flowId,
      isForbid,
      onRowClick,
      isHabSetValue,
      flowBus
    } = this.props
    const isBudgetAdjust = name === 'budgetAdjustDetails'
    let components = isBudgetAdjust ? template : this.formatComponents(template)
    const apportionConfigs = template?.apportionConfigs || []
    const dataSource = isBudgetAdjust ? data : this.getDataSource(data, dataLinkTemplateId, name, template) || []

    return (
      <div className={styles.dataLinkEditTableWrapper}>
        <DataLinkEditHeader
          field={field}
          isEdit={isEdit}
          templates={isBudgetAdjust ? undefined : templates}
          dataLinkTemplateId={isBudgetAdjust ? undefined : dataLinkTemplateId}
          onChangeDataLinkTemplateId={this.handleChangeId}
          external={external}
          flowId={flowId}
          isForbid={isForbid}
        />
        <EditTableContainer
          components={components}
          apportionConfigs={apportionConfigs}
          dataSource={dataSource}
          noTransColumns={isBudgetAdjust}
          isEdit={isEdit}
          canCreate={isEdit && !['UPDATE', 'REF'].includes(behaviour) && !isBudgetAdjust}
          onGetData={this.handleGetData}
          onApportionFormChange={this.handleApportionFormChange}
          bus={bus}
          flowFormFieldsValue={flowFormFieldsValue}
          onSelect={onSelect}
          onFieldEdit={onFieldEdit ? this.handleFieldEdit : null}
          editingKey={editingKey}
          onGetActiveDataIndex={onGetActiveDataIndex}
          entityId={field?.referenceData?.id}
          field={field}
          onRowClick={onRowClick}
          isHabSetValue={isHabSetValue}
          pagination={{
            current,
            pageSize,
            showSizeChanger: true,
            pageSizeOptions: [
              5, 10, 20, 50
            ],
            onChange: this.handlePageChange,
            onShowSizeChange: this.handlePageChange
          }}
          onJumpLastPage={this.handleJumpLastPage}
          allRowsEditable={true}
          flowBus={flowBus}
        />
      </div>
    )
  }
}

export default DataLinkEditTable
