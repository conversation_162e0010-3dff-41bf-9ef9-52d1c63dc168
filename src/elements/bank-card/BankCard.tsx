import './BankCard.less'
import React, { PureComponent } from 'react'
import classNames from 'classnames'
import { T } from '@ekuaibao/i18n'
import { Props } from './components/intreface'
import { EnhanceConnect } from '@ekuaibao/store'
import _compact from 'lodash/compact'
import { payFromChannelMap } from './components/account-list-consts'
import {
  FilledGeneralCollect,
  FilledTipsCheck,
  OutlinedGeneralCollection,
  FilledGeneralSetting,
  OutlinedGeneralMember,
  OutlinedGeneralGroup
} from '@hose/eui-icons'
import { Divider, Ellipsis, Tag, Tooltip } from '@hose/eui'

const BANK_DISABLE_BG = require('./images/bank_disable_bg.svg')
const BANK_BG = require('./images/bank_bank_bg.svg')
const BANK_CORPORATE_BG = require('./images/bank_corporate_bg.svg')
const BANK_OTHER_BG = require('./images/bank_other_bg.svg')
const BANK_WECHAT_BG = require('./images/bank_wechat_bg.svg')
const getColorBg = (type: string, disable: boolean) => {
  if (disable) {
    return 'disable-status'
  }
  switch (type) {
    case 'WEIXIN':
      return 'wx-status'
    case 'BANK':
      return 'bank-status'
    default:
      return 'account-status'
  }
}
interface State {
  channel: string
}

@EnhanceConnect(state => ({
  allCurrencyRates: state['@common'].allCurrencyRates,
  standardCurrency: state['@common'].standardCurrency,
  payeeConfig: state['@common'].payeeConfig,
  FOREIGN_CURRENCY_PAY: state['@common'].powers.FOREIGN_CURRENCY_PAY,
  globalFields: state['@common'].globalFields.data
}))
export default class BankCard extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    this.state = { channel: props.data.defaultChannel || 'OFFLINE' }
  }

  componentWillReceiveProps(np) {
    if (np.data !== this.props.data) {
      this.setState({ channel: np.data.defaultChannel || 'OFFLINE' })
    }
  }

  channelClick = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
  }

  handleClickSetDefault = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleSetDefault } = this.props
    handleSetDefault && handleSetDefault()
  }

  handleClickCancelDefault = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleClickCancelDefault } = this.props
    handleClickCancelDefault && handleClickCancelDefault()
  }

  handleCollect = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleCollect } = this.props
    handleCollect && handleCollect()
  }

  handleCancelCollect = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleCancelCollect } = this.props
    handleCancelCollect && handleCancelCollect()
  }

  payeeConfig = () => {
    const { payeeConfig, data } = this.props

    return data.type === 'PERSONAL' ? payeeConfig?.personalAccountConfig : payeeConfig?.publicAccountConfig
  }

  getBgImg = (sort: string) => {
    const { data, payeeConfig } = this.props
    if (!data.filterActive) {
      return BANK_DISABLE_BG
    } else if (data.type === 'PUBLIC' && payeeConfig?.publicAccountConfig?.publicBackgroundCls) {
      return BANK_CORPORATE_BG
    }
    switch (sort) {
      case 'WEIXIN':
        return BANK_WECHAT_BG
      case 'BANK':
        return BANK_BG
      default:
        return BANK_OTHER_BG
    }
  }

  renderBankCard = () => {
    const {
      isManangePage,
      data,
      onClick,
      payFromChannel,
      isDefault,
      isSettingDepency,
      showCancleDefault = false,
      showCollectionBtns = false
    } = this.props
    const fullVisible = data.visibility && data.visibility.fullVisible
    const accountType = data.type === 'PERSONAL' ? i18n.get('个人账户') : i18n.get('对公账户')
    let bankName = data?.branchId?.name || data?.branch || data.bank || data.unionBank
    const bankIcon = data.icon || data.unionIcon
    const accountNo = data.accountNo || data.cardNo
    const isVIRTUALCARD = data.sort === 'VIRTUALCARD' // 数科虚拟卡
    const isPayeeManager = isVIRTUALCARD ? false : payFromChannelMap.manage === payFromChannel
    const isDefaultAndPersnal = !isDefault && payFromChannelMap.manage !== payFromChannel && !isSettingDepency
    const isShowCancelDefault =
      isDefault &&
      isManangePage &&
      (payFromChannelMap.select === payFromChannel || payFromChannelMap.personal === payFromChannel)
    const isCorporation = data.owner === 'CORPORATION' ? i18n.get('企业') : data.staffId && data.staffId.name

    const showCancle = showCancleDefault ? data.filterActive && isShowCancelDefault : isShowCancelDefault
    const remarkDisplay = this.payeeConfig()?.remarkDisplay
    const remark = data?.remark || ''
    const showRemark = remark && remarkDisplay
    bankName = data.sort === 'OVERSEABANK' ? data.swiftCode + ' | ' + data.bankName : bankName
    // 展示取消收藏按钮的条件判断
    const showCancelCollectBtn = data?.favoriteStatus === true ? true : false

    return (
      <div className="bank-card">
        <img src={this.getBgImg(data.sort)} className="bank-item-top-bg"></img>
        <div className="bank-card-info">
          <Tooltip placement="topLeft" title={data.accountName} getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
            <div className="bank-name">
              <Ellipsis direction="end" content={data.accountName} />
            </div>
          </Tooltip>
         
          <div className="top-action">
            {!isVIRTUALCARD && data.filterActive && isDefaultAndPersnal && (
              <div className="default-action" onClick={this.handleClickSetDefault}>
                {i18n.get('设为默认')}
              </div>
            )}
            {!isVIRTUALCARD && showCancle && (
              <div className="default-action" onClick={this.handleClickCancelDefault}>
                {i18n.get('取消默认')}
              </div>
            )}
            {!isSettingDepency && showCollectionBtns && !showCancelCollectBtn && (
              <div className="collect-action" onClick={this.handleCollect}>
                <Tooltip placement="top" title={i18n.get('收藏')} getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
                  <OutlinedGeneralCollection fontSize={16} />
                </Tooltip>
              </div>
            )}
            {!isSettingDepency && showCollectionBtns && showCancelCollectBtn && (
              <div className="collect-action visible" onClick={this.handleCancelCollect}>
                <Tooltip placement="top" title={i18n.get('取消收藏')} getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
                  <FilledGeneralCollect color="var(--eui-function-warning-400)" fontSize={16} />
                </Tooltip>
              </div>
            )}
             {
              isPayeeManager && <FilledGeneralSetting color="var(--eui-primary-pri-500)" fontSize={20} className='cur-p' onClick={onClick} />
             }
          </div>
        </div>
        <div className="bank-card-no">{formatCardNo(accountNo)}</div>
        <div className="bank-card-tag">
          <div className="grow horizontal">
            <Tag size="small" fill="outline">
              {data.type === 'PERSONAL' ? (
                <OutlinedGeneralMember fontSize={12} className="mr-2" />
              ) : (
                <OutlinedGeneralGroup className="mr-2" fontSize={12} />
              )}
              {accountType}
            </Tag>
            {fullVisible && (
              <Tag size="small" className="ml-4" fill="outline">
                <T name="共享" />
              </Tag>
            )}
            {isDefault && (
              <Tag size="small" className="ml-4" color="pri">
                <T name="默认" />
              </Tag>
            )}
          </div>
          {showRemark && (
            <Tooltip title={remark} getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
              <div className="bank-remark">
                <Ellipsis direction="end" content={i18n.get('备注：') + remark} />
              </div>
            </Tooltip>
          )}
        </div>
        <div className="bank-card-footer">
          <div className="bank-card-icon">
            {bankName && <img className="bank-icon" src={`${bankIcon}?********`} alt={bankName} />}
            <Tooltip placement="topLeft" title={bankName} getPopupContainer={(triggerNode: any) => triggerNode.parentNode}>
              {data.sort === 'OVERSEABANK' ? (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div className="bank-branch">{data.swiftCode}</div>
                  <Divider type="vertical" style={{ height: '16px' }} />
                  <div className="bank-branch">
                    <Ellipsis direction="end" content={data.bankName} />
                  </div>
                </div>
              ) : (
                <div className="bank-branch">
                  <Ellipsis direction="end" content={bankName} />
                </div>
              )}
            </Tooltip>
          </div>
          {data.owner && (
            <div className="bank-card-owner">
              <T name="所有者:" /> {isCorporation}
            </div>
          )}
        </div>
      </div>
    )
  }

  render() {
    const {
      onClick,
      className,
      data,
      isSelf,
      payFromChannel,
      inSelectPayMethodModal,
      isSelected,
      payeeConfig
    } = this.props
    // 当前类型是公共账户并且设置了当前账户底色
    const isPublicConfig = data.type === 'PUBLIC' && payeeConfig?.publicAccountConfig?.publicBackgroundCls
    let bgClass = isPublicConfig ? 'public-status' : getColorBg(data.sort, !data.filterActive)
    const isSlefPersonal = payFromChannelMap.personal === payFromChannel && isSelf
    const fnOnClick = inSelectPayMethodModal
      ? onClick
      : isSlefPersonal || payFromChannelMap.personal !== payFromChannel
      ? // 企业钱包，数科钱包，数科虚拟卡不可点击
        data.sort === 'WALLET' || data.sort === 'CORPWALLET' || data.sort === 'VIRTUALCARD'
        ? null
        : onClick
      : null

    bgClass = !data.active ? 'disable-status' : bgClass

    return (
      <div
        className={classNames('bank-card-wrap', bgClass, className, {
          'cur-n': data.sort === 'WALLET',
          'card-selected': isSelected
        })}
        onClick={fnOnClick}
      >
        {this.renderBankCard()}
        {isSelected && (
          <div className="card-item-mask">
            <FilledTipsCheck className="mask-icon" fontSize={16} />
          </div>
        )}
      </div>
    )
  }
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1   ')
}
