import { BillActionBase } from "./base-action";
import { Modal, message as EUIMessage } from "@hose/eui";
import { delFlow } from "../../bills.action";
import { validTripSync, enableDraftConfig } from "../../parts/right-part/billInfo/validateTripSync";
import { get, reject } from "lodash";
import { app } from "@ekuaibao/whispered";
import { checkQuickExpends } from "../../util/billUtils";
import { TITLE_CONFIG } from "../../util/config";

export class DeleteAction extends BillActionBase {
  label = i18n.get('删除单据')

  onAction = () => {
    return this.handleConfirmDelete()
  }

  handleConfirmDelete = async () => {
    let isEnableDraftConfig = await enableDraftConfig({ dataSource: this.props.backlog.flowId })
    if (isEnableDraftConfig) {
      let res_TripSync = await validTripSync(this.props)
      if (!res_TripSync) return
    }

    const { backlog, bus } = this.props
    const baseType = ['settlement', 'reconciliation']
    // @ts-ignore
    return bus?.getValue().then(async value => {
      const details = get(value, 'details')
      const feeData = details ? await bus.invoke('get:moveTo:RecordExpendsDetails') : []
      const cDetails = !!details ? details : []
      const data = cDetails.concat(feeData)
      const billType = get(backlog.flowId, 'formType')
      const amountList = !!data.length ? data.filter(v => get(v, 'feeTypeForm.amount.standard')) : []
      return !!amountList.length && !baseType.includes(billType)
        ? this.fnOpenDeleteBillModal(amountList)
        : this.fnDeleteBill(billType)
    })
  }

  fnOpenDeleteBillModal = async amountList => {
    const { bus, layer, callBack } = this.props
    const commonState = app.getState('@common')
    const baseDataProperties = commonState.globalFields.data
    const userInfo = commonState.userinfo.data
    const autoExpenseWithBillStriction = commonState.powers.autoExpenseWithBillStriction
    const dataSource = this.props.backlog.flowId
    const isQuickExpends = await checkQuickExpends(dataSource)
    const billFormType = get(dataSource, 'formType')
    const ownerId = get(dataSource, 'ownerId.id') || get(userInfo, 'staff.id')
    const submitterId = get(dataSource, 'form.submitterId.id')
    const id = get(dataSource, 'id')
    const details = get(dataSource, 'form.details')
    const code = get(dataSource, 'form.code')
    const specificationId = get(dataSource, 'form.specificationId.id')
    return app
      .open('@bills:DeleteBillModal', {
        amountList,
        billFormType,
        baseDataProperties,
        ownerId,
        flowId: id,
        details,
        isQuickExpends,
        code,
        specificationId,
        submitterId,
        autoExpenseWithBillStriction
      })
      .then(async (res: { value: string }) => {
        await this.fnDeleteFinish()
        // 判断是否有权限删除并移动到随手记
        const { staff } = userInfo
        const canMoveToNote = staff?.id === ownerId ? true : false
        if (res.value === 'deleteAndMove' && canMoveToNote) {
          const message = isQuickExpends
            ? i18n.get('费用已被移至「快速报销」，您可在「快速报销」中继续编辑')
            : i18n.get('费用已被移至「随手记」，您可在「随手记」中继续编辑')
          EUIMessage.info(message)
        }
      })
  }

  fnDeleteBill = (type: string) => {
    const label = i18n.get(TITLE_CONFIG[type])
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: i18n.get('你确定删除单据吗？'),
        content: i18n.get('bot-been-del', { label }),
        okText: i18n.get('删除'),
        cancelText: i18n.get('取消'),
        onOk: async () => {
          try {
            await this.fnDeleteFinish()
            resolve(1)
          } catch {
            reject()
          }
        },
        onCancel: reject
      })
    })
  }

  fnDeleteFinish = async () => {
    let { backlog, bus } = this.props
    const flow = backlog.flowId
    let id = get(flow, 'id')
    const title = get(flow, 'form.title') || ''
    if (id) {
      await app.dispatch(delFlow(id))
      app.invokeService('@common:insert:assist:record', {
        title: `删除${title}单据`
      })
    }
  }
}
