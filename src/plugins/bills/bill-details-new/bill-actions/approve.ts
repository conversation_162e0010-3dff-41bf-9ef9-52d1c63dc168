import { BillActionBase, BillActionBaseProps } from './base-action'
import { getApproverRepeatConfig } from '../../bills.action'
import { action } from './constant'
import { ButtonProps, Modal } from '@hose/eui'

export class ApproveAction extends BillActionBase {
  approverRepeatStatus = false
  approverRepeatMessage = ''
  hasGetApproverRepeatConfig = false

  action = action.agree
  label = i18n.get('同意')

  constructor(props: BillActionBaseProps, buttonProps: ButtonProps) {
    super(props, buttonProps)
    this.init()
  }

  onAction = async () => {
    const { onFooterButtonsClick = this.defaultHandleActionImplementation, backlog, riskData } = this.props
    const { approverRepeatStatus, hasGetApproverRepeatConfig } = this

    if (window.isZhongDian) {
      if (!hasGetApproverRepeatConfig) {
        return
      }
      if (approverRepeatStatus) {
        Modal.info({
          content: this.approverRepeatMessage,
          okText: i18n.get('确定')
        })
        return
      }
      return onFooterButtonsClick(this.action, backlog, riskData)
    }
    return onFooterButtonsClick(this.action, backlog, riskData)
  }

  init = () => {
    return this.getApproverRepeated(this.props.backlog.flowId.id)
  }

  /**
   * 检查单据当前审批人与前序节点重复时是否无权审批
   */
  getApproverRepeated = async (flowId: string) => {
    try {
      const res = await getApproverRepeatConfig(flowId)
      if (res?.value) {
        const { isRepeated, message } = res.value
        this.approverRepeatStatus = isRepeated
        this.approverRepeatMessage = message
      }
    } finally {
      this.hasGetApproverRepeatConfig = true
    }
  }
}
