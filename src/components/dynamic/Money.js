/**************************************************
 * Created by nany<PERSON>ingfeng on 10/07/2017 15:41.
 **************************************************/
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { isEmpty } from '@ekuaibao/helpers'
import { EnhanceConnect } from '@ekuaibao/store'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import { fnDefineIsFormula, standardValueMoney } from '../../lib/misc'
import CurrencyMoney from '../../elements/currency/currency-money'
import { validatorMoney } from '../validator/validator'
import { isDisable } from '../utils/fnDisableComponent'
import { cloneDeep, get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { getV } from '@ekuaibao/lib/lib/help'
import { moneyStrToStandardCurrencyMoney } from '@ekuaibao/money-math'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
import { isObject } from 'lodash'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
import { enableMoneyOptimization } from '../../lib/featbit'

@EnhanceField({
  descriptor: {
    type: 'money'
  },
  wrapper: wrapper(),
  validator: (field, props) => (rule, value, callback) => {
    let { billSpecification, form, bus, isPermitForm } = props
    let { max, min, optional, label, name } = field
    const type = get(field, 'defaultValue.type')
    if (isPermitForm && name === 'amount') {
      return callback()
    }
    if (type === 'invoiceSum') {
      const { invoices } = form.getFieldValue('invoiceForm') || {}

      if (!optional && (!invoices || !invoices.length)) {
        return callback(i18n.get('该字段需根据导入的发票自动计算，请添加发票'))
      }
    }


    if (billSpecification) {
      let chargeAgainst = billSpecification.configs.find(v => v.ability === 'chargeAgainst')
      if (chargeAgainst && chargeAgainst.isChargeAgainst) {
        min = min * 1 < 0 ? min : -max
      }
    }
    const MultiCurrencyCostStandard = api.getState()['@common'].powers.MultiCurrencyCostStandard
    if (MultiCurrencyCostStandard && !field.editable && field?.defaultValue?.type === 'costStandard' && value?.costStandardError) {
      return callback(i18n.get('请联系管理员在币种设置中维护本位币与费标金额币种的汇率'))
    }

    if (rule.level > 0) return callback()

    if (value && value.standard) {
      const result = validatorMoney(value, max, min, callback, field)
      if (name === 'contractAmount' && bus.$_settleAmountTotal) {
        if (Number(value?.standard || 0) < bus.$_settleAmountTotal) {
          return callback('合同金额不能小于结算总额')
        }
      }
      return result ?? callback(required(field, value))
    }


    if (!optional && (!value || value.standard === undefined || value.standard === '' || value.standard == null)) {
      let errStr = i18n.get('not-empty', { label: i18n.get(label) })
      if (field?.field === 'receivingAmount' && value?.foreignNumCode && !value?.rate) {
        errStr = i18n.get('金额及汇率不能为空，请联系管理员维护企业汇率')
      }
      return callback(errStr)
    }

    callback(required(field, value))
  }
})
@EnhanceConnect(state => {
  return {
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
    standardCurrency: state['@common'].standardCurrency,
    historyCurrencyInfo: state['@bills'].historyCurrencyInfo,
    allCurrencyRates: state['@common'].allCurrencyRates,
    MultiCurrencyCostStandard: state['@common'].powers.MultiCurrencyCostStandard
  }
})
export default class Money extends PureComponent {
  constructor(props) {
    super()
    const data = this.moneyStr2StandardMoneyValue('')
    this.state = { data }
  }

  componentWillReceiveProps(nextProps) {
    const { isDetail, value, dimentionCurrencyInfo, field, form } = this.props
    if (isDetail && nextProps?.value && nextProps?.value !== value) {
      this._postEvent(nextProps.value)
    }

    if (nextProps?.MultiCurrencyCostStandard && nextProps?.value !== value && !field.editable && field?.defaultValue?.type === 'costStandard' && nextProps?.value?.costStandardError) {
      form?.validateFields?.([field?.name])
    }

    if (dimentionCurrencyInfo?.currency?.id !== nextProps?.dimentionCurrencyInfo?.currency?.id) {
      this.initValue(nextProps?.dimentionCurrencyInfo?.currency)
    }
  }

  moneyStr2StandardMoneyValue = money => {
    const standCurrency = api.getState('@common.standardCurrency')
    return moneyStrToStandardCurrencyMoney(money, standCurrency)
  }

  preGetValue = () => {
    const { value } = this.props
    if (value && typeof value === 'object' && value.standard && !value.standardStrCode) {
      return this.moneyStr2StandardMoneyValue(value.standard)
    }
    return value
  }

  _valueChange = ({ isValidator = true, data }) => {
    const { getExpenseStandardItemsLength, external } = this.props
    if (!isNaN(Number(data.standard)) && Number(data.standard) !== Number(this.state.data.standard)) {
      this.setState({ data })
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    }
    getExpenseStandardItemsLength && this.getExpenseStandardItemsLength(data)

    if (isValidator) return this.setValue(data)
    this._postEvent(data)
  }

  getExpenseStandardItemsLength = data => {
    let { getExpenseStandardItemsLength } = this.props
    let { standard = 0 } = data
    let re = new RegExp(`^(-?([1-9]\\d*)|0)(\\.\\d{1,2})?$`)
    if (re.test(standard)) {
      getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
    }
  }

  setValue = val => {
    let { onChange, field, billSpecification } = this.props
    const allowSelectionReceivingCurrency = billSpecification?.configs?.find(v => v.ability === 'pay')
      ?.allowSelectionReceivingCurrency
    const notEmptyValue = allowSelectionReceivingCurrency && ['amount', 'receivingAmount'].includes(field.name) // 有收款币种的时候，不清空值，这里需要值给收款币种做判断
    if (val && typeof val === 'object' && !val.standard && field.editable && !notEmptyValue) {
      //选填的时候如果standard为空后台会报错，所以当standard为空的时候清空数据，但是自动计算的话不能清空所以加一个field.editable条件
      val = undefined
    }
    this._postEvent(val)
    onChange && onChange(val)
  }

  _postEvent = val => {
    const { field, bus, isDetail } = this.props
    if (field.name === 'loanMoney' || field.name === 'payMoney') {
      bus && bus.emit('loanMoney:changed', val)
    }
    if (field.name === 'reimbursementMoney') {
      bus && bus.emit('reimbursementMoney:changed', val)
    }
    if (isDetail && bus && bus.has('amount:changed')) {
      bus.invoke('amount:changed', { field: field.name, amount: val }).catch(e => { })
    }
  }

  async componentWillMount() {
    let {
      bus,
      field: { field: fieldName },
      value
    } = this.props
    if (!!~['amount', 'loanMoney'].indexOf(fieldName)) {
      bus.on('money:changed:value', this.setValue)
    }
    if (!!~['amount'].indexOf(fieldName) && bus && bus.has('amount:changed')) {
      bus.invoke('amount:changed', value).catch(e => { })
    }
    bus.on('details:change', this.onDetailsChange)
    bus.on('trip:change', this.onDetailsChange)
    bus.on('money:tax:update:value', this.onTaxChange)
    bus.on('detail:amount:change', this.handleDetailAmountChange)
    await Promise.all([
      api.dataLoader('@common.currencyConfig').load(),
      api.invokeService('@currency-manage:get:currency:modifiable'),
    ])
  }

  componentWillUnmount() {
    let {
      bus,
      field: { field: fieldName }
    } = this.props
    if (!!~['amount', 'loanMoney'].indexOf(fieldName)) {
      bus.un('money:changed:value', this.setValue)
    }
    bus.un('details:change', this.onDetailsChange)
    bus.un('trip:change', this.onDetailsChange)
    bus.un('money:tax:update:value', this.onTaxChange)
    bus.un('detail:amount:change', this.handleDetailAmountChange)
  }

  handleDetailAmountChange = amount => {
    const { onChange, field, isDetail } = this.props
    if (field.field === 'amount' && isDetail) {
      onChange && onChange(standardValueMoney(amount))
    }
  }

  async componentDidMount() {
    const { dimentionCurrencyInfo } = this.props
    this.initValue(dimentionCurrencyInfo?.currency)
    await api.dataLoader('@common.allCurrencyRates').reload()
  }

  componentDidUpdate() {
    this.delayTodo && this.delayTodo()
  }

  initValue = currency => {
    const { originalValue = {}, onChange } = this.props
    const defaultValue = get(this, 'props.field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      const details = originalValue.details || originalValue.trips
      let sumValue = standardValueMoney(0, currency)
      const { standardScale } = sumValue
      details &&
        details.forEach(el => {
          const form = el.feeTypeForm || el.tripForm
          const defVal = isObject(form[defaultValue.value])
            ? form[defaultValue.value].standard
            : form[defaultValue.value]
          if (defVal) {
            sumValue.standard = new Big(sumValue.standard).plus(Number(defVal)).toFixed(standardScale)
          }
        })
      onChange && onChange(sumValue)
      this._postEvent()
    }
  }

  delayTodo = null
  onTaxChange = () => {
    const { field } = this.props
    const type = get(field, 'defaultValue.type')

    if (type === 'invoiceSum') {
      this.delayTodo = () => {
        this.setValue(undefined)
        this.delayTodo = null
      }
    }
  }

  onDetailsChange = details => {
    const { onChange, dimentionCurrencyInfo } = this.props
    const defaultValue = get(this.props, 'field.defaultValue')
    if (defaultValue && defaultValue.type === 'sum') {
      let data = standardValueMoney(0, dimentionCurrencyInfo?.currency)
      const { standardScale } = data
      details &&
        details.forEach(el => {
          const form = el.feeTypeForm || el.tripForm
          if (form[defaultValue.value]) {
            data.standard = new Big(data?.standard || 0)
              .plus(Number(form[defaultValue.value]?.standard || 0))
              .toFixed(standardScale)
          }
        })
      onChange(data)
      this.setState({ details })
      this._valueChange({ isValidator: true, data })
    }
  }

  isSelectCurrencyDisable = () => {
    const { field, selectCurrencyDisable, foreignCurrencyLoan } = this.props
    const fieldName = getV(field, 'field', '')
    return fieldName === 'amount' || fieldName === 'companyRealPay' || !!foreignCurrencyLoan
      ? selectCurrencyDisable
      : false
  }

  isFlow = () => {
    const { businessType } = this.props
    return enableMoneyOptimization() && ['FLOW', 'DETAILS'].includes(businessType)
  }

  render() {
    // currencySelAble 是否可以修改币种，分摊时不能修改币种
    // cannotEditAmountField 不可编辑金额相关字段；仅在审批中修改时的费用明细中有取值为true的可能
    const {
      value,
      field,
      autoCalFields,
      form,
      bus,
      currencySelAble = true,
      external,
      isForbid,
      isModify,
      cannotEditAmountField,
      foreignCurrencyLoan,
      foreignNumCode,
      billSpecification = {},
      isManualRepayment = false,
      showAllFeeType,
      template,
      currencySwitch,
      shouldSaveFeetype,
      standardCurrency,
      dimentionCurrencyInfo,
      historyCurrencyInfo,
      allCurrencyRates,
      useEUI,
      noPopupContainer,
      billData,
      detalsAllCurrencyRates,
      isForeignCurrencyEdit = false
    } = this.props
    let { field: fieldName } = field
    const { configs = [] } = billSpecification
    const isAuto = fnDefineIsFormula(fieldName, autoCalFields, field)
    const placeholder = isAuto && !isEmpty(value) ? i18n.get('自动计算生成') : getPlaceholder(field)
    let disabled = isDisable(this.props)
    // 审批中修改时，如果单据的明细权限为不全可见，且当前费用明细中含有不可见的分摊时，不允许修改费用明细中的金额字段和分摊字段
    if (isModify && !showAllFeeType && !disabled && cannotEditAmountField) disabled = true
    const writtenOff = configs.find(item => item.ability === 'writtenOff')
    let dimentionCurrency = cloneDeep(dimentionCurrencyInfo)
    const historyCurrencyNumCode = get(historyCurrencyInfo, 'currency')
    const dimentionCurrencyNumCode = get(dimentionCurrencyInfo, 'currency.numCode')
    if (historyCurrencyNumCode && dimentionCurrencyNumCode && historyCurrencyNumCode === dimentionCurrencyNumCode) {
      dimentionCurrency.rates = historyCurrencyInfo.rates
    }
    let allCurrency = cloneDeep(allCurrencyRates)
    if (historyCurrencyNumCode) {
      allCurrency = historyCurrencyInfo.rates
    } else {
      allCurrency = allCurrency.filter(i => i.originalId === standardCurrency?.numCode)
    }
    return (
      <div>
        <CurrencyMoney
          foreignCurrencyLoan={foreignCurrencyLoan}
          isManualRepayment={isManualRepayment}
          foreignNumCode={foreignNumCode}
          writtenOff={writtenOff}
          selectCurrencyDisable={this.isSelectCurrencyDisable()}
          value={value}
          form={form}
          field={field}
          disabled={isAuto || disabled}
          placeholder={placeholder}
          isAuto={isAuto}
          bus={bus}
          valueChange={this._valueChange}
          currencySelAble={currencySelAble}
          external={external}
          isEdit={true}
          isForbid={isForbid}
          template={template}
          currencySwitch={currencySwitch}
          shouldSaveFeetype={shouldSaveFeetype}
          standardCurrency={standardCurrency}
          dimentionCurrency={dimentionCurrency}
          allCurrencyRates={allCurrency}
          detalsAllCurrencyRates={detalsAllCurrencyRates}
          showLabel={!field?.isSimple}
          useEUI={useEUI}
          noPopupContainer={noPopupContainer}
          billData={billData}
          isFlow={this.isFlow()}
          isForeignCurrencyEdit={isForeignCurrencyEdit}
        />
      </div>
    )
  }
}
