import { getBoolVariation } from '../lib/featbit'
import { Fetch } from '@ekuaibao/fetch'
import { dynamicImportTranslatePlugin } from '../lib/i18n'
import { initTranslator } from './hyper-translator'
// 给 i18nTextMap 提供初始化的兼容使用 i18n.get 模板匹配 的方法
export function i18nHelper(str: string) {
  return (args: { [key: string]: string | number }) => {
    let i18nStr = str
    for (let key of Object.keys(args)) {
      const reg = new RegExp(`{${key}}`, 'g')
      const replaceValue = String(args[key])
      i18nStr = i18nStr.replace(reg, replaceValue)
    }
    return i18nStr
  }
}

export async function enableSettingTranslate() {
  try {
    const target = getLanguageByLanguageCode(Fetch.lang)
    setTimeout(async () => {
      if (getBoolVariation('ao-20-hyper-translator')) {
        await dynamicImportTranslatePlugin()
        const translateConfig = window.TranslateConfig
        initTranslator(translateConfig, target)
      } else {
        if (!['zh-CN', 'en-US'].includes(target)) {
          await dynamicImportTranslatePlugin()
          const translateConfig = window.TranslateConfig
          translateConfig.initTranslate()
          translateConfig.setToLanguage(target)
        } else {
          return
        }
      }
    }, 200)
  } catch (e) {
    console.log(e)
  }
}

/**
 * 后端存储的为语言 code，需要转换为对应的 id作为 plugin 的 target
 * language可能作为openAPI的参数，所以需要更通用的定义
 */
export const getLanguageByLanguageCode = (code: string) => {
  const languageOptions = [
    { code: 'zh-CN', label: '中文', icon: '', id: 'zh-CN' },
    { code: 'zh-TW', label: '繁體中文', icon: '', id: 'chinese_traditional' },
    { code: 'en-US', label: 'English', icon: '', id: 'en-US' },
    { code: 'ja-JP', label: '日本語', icon: '', id: 'japanese' },
    { code: 'ko-KR', label: '한국의', icon: '', id: 'korean' },
    { code: 'fr-FR', label: 'Français', icon: '', id: 'french' },
    { code: 'de-DE', label: 'Deutsch', icon: '', id: 'deutsch' },
    { code: 'es-ES', label: 'Español', icon: '', id: 'spanish' },
    { code: 'it-IT', label: 'Italiano', icon: '', id: 'italian' },
    { code: 'pt-PT', label: 'Protuguês', icon: '', id: 'portuguese' },
    { code: 'ms-MY', label: 'Bahasa Melayu', icon: '', id: 'malay' },
    { code: 'ru-RU', label: 'Русский', icon: '', id: 'russian' },
    { code: 'bn-BD', label: 'বাংলা', icon: '', id: 'bengali' },
    { code: 'hi-IN', label: 'हिंदी', icon: '', id: 'hindi' },
    { code: 'ar-IL', label: 'العربية', icon: '', id: 'arabic' },
    { code: 'th-TH', label: 'ไทย', icon: '', id: 'thai' },
    { code: 'tr-TR', label: 'Türkçe', icon: '', id: 'turkish' },
    { code: 'vi-VN', label: 'tiếng việt', icon: '', id: 'vietnamese' }
  ]
  return languageOptions.find(item => item.code === code)?.id || 'zh-CN'
}
