import React, { useEffect, useState, useRef } from 'react'
import LoaderWithLegacyData from '../../../../elements/data-grid-v2/LoaderWithLegacyData'
import { createNodeNameColumn, createNodeStaffColumn } from '../../../../elements/data-grid-v2/CreateColumn'
import { EnhanceConnect } from '@ekuaibao/store'
import { app as api } from '@ekuaibao/whispered'
import { Resource } from '@ekuaibao/fetch'
import { searchMyBill } from '../fetchUtils'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { initSpecificationScenes, getDefaultMapping, initMapping } from '../utils'
import { fnIsRiskError } from '../../riskWarning/formatRiskWarningData'
import { searchOptions } from '../../../../elements/data-grid-v2/element/util'
import { enableHidingFinishedBills, supportBillDetailsSwitchingInDrawer } from '../../../../lib/featbit'
const scenesType = 'MyBill'
const myBill = new Resource('/api/flow/v2/filter')
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const BillTableViewContent = ({ bus, baseDataProperties, specifications, showAIIcon }) => {
  const [scenes, setScenes] = useState([])
  const [mapping, setMapping] = useState(getDefaultMapping())
  const [readyGetSence, setReadyGetSence] = useState(false)
  const currentDataSource = useRef(null)

  // 初始化表格筛选项
  const initScenes = data => {
    initSpecificationScenes({ data, specifications, setScenes, setReadyGetSence })
  }

  const _handleTableRowClickV2 = async (line: any, index?: number) => {
    const { id } = line
    const isFromDataGridTable = index !== undefined
    api.open('@bills:BillInfoDrawerV2', {
      currentId: id,
      bus,
      // 如果不是点击table中的行打开的则无法切换单据，
      // 这里是刷新时序导致的，在新建提交之后会出现切换按钮表现异常
      flows: isFromDataGridTable ? (currentDataSource.current || []) : [],
      params: { id, autoClose: true, isNewSearchInfo: true },
      callBack: bus.reload,
      scene: 'OWNER',
      from: 'from_myBill'
    })
  }

  // 处理表格中每行的点击事件
  const _handleTableRowClick = async (line, index) => {
    const {
      id,
      formType,
      form: { code }
    } = line

    if (supportBillDetailsSwitchingInDrawer()) {
      _handleTableRowClickV2(line, index)
      return
    }

    startOpenFlowPerformanceStatistics()
    const getFlowAction = async () => {
      const res = await api.invokeService('@bills:get:flow-action', { code })
      const {
        value: { buttons }
      } = res
      return buttons
    }

    const getRiskTip = async () => {
      let isForbid = line.state === 'rejected' || line.state === 'draft'
      const level = isForbid ? 'OutOfLimitReject' : ''
      const riskTip = await api.invokeService('@bills:get:flow:risk:warning', level ? { id, level } : { id })
      isForbid = isForbid && fnIsRiskError(riskTip?.value?.riskWarning)
      riskTip.isForbid = isForbid
      return riskTip
    }

    const getFlowInfo = async () => {
      const resp = await api.invokeService('@bills:get:flow-info', { id })
      const { value: flowId } = resp
      return flowId
    }
    const promiseAllDataNeeded = Promise.all([getFlowAction(), getRiskTip(), getFlowInfo()])

    if (bus.has('check:value:changed')) {
      await bus.invoke('check:value:changed') // 检查是否有内容改动
    }

    const [buttons, riskTip, flowId] = await promiseAllDataNeeded

    //  获取标题
    const title = riskTip.isForbid ? i18n.get('编辑单据') : `${i18n.get(billTypeMap()[formType])}${i18n.get('详情')}`
    //  打开弹窗
    api.open('@bills:BillInfoDrawer', {
      title,
      buttons,
      riskTip,
      backlog: { id: -1, flowId },
      invokeService: '@bills:get:flow-info',
      params: { id: flowId.id, autoClose: true, isNewSearchInfo: true },
      callBack: bus.reload,
      from: 'from_myBill',
      isAlwaysOk: true,
      // mask: ['pending', 'approving', 'receiving', 'sending', 'paying', 'paid', 'archived', 'receivingExcep'].includes(
      //   flowId?.state
      // ),
      mask: false,
      bus,
      customStyle: {
        'overflow-x': 'auto'
      }
    })
  }

  useEffect(() => {
    bus.on('table:row:click', _handleTableRowClick)
    bus.on('initScenes:action', initScenes)

    if (specifications.length === 0) {
      api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned', {}, { hiddenLoading: true })
    } else {
      // 初始化筛选项的mapping
      initMapping({ specifications, mapping, setMapping })
      // 获取场景列表
      myBill.GET('/$type', { type: scenesType }, null, { hiddenLoading: true }).then(res => {
        const { value } = res
        initScenes(value)
      })
    }

    return () => {
      bus.un('table:row:click', _handleTableRowClick)
      bus.un('initScenes:action')
    }
  }, [specifications?.length])

  const fetchPaying = async (params: any = {}, dimensionItems = {}) => {
    // 在请求时处理query中的filterBy
    params.partialPayState = true

    if (enableHidingFinishedBills()) {
      // 如果有过滤，不需要默认status
      if (params.filters?.state?.state?.length > 0) {
        params.status = {}
      } else {
        params.status = {
          state: ['rejected', 'draft', 'sending', 'receiving', 'receivingExcep', 'approving', 'pending', 'paying', 'paid']
        }
      }
    } else {
      params.status = {
        state: ['rejected', 'draft', 'sending', 'receiving', 'receivingExcep', 'approving', 'pending', 'paying']
      }
    }

    const { scene = 'all' } = params
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')

    const res = await searchMyBill(params, findScene, dimensionItems)
    currentDataSource.current = res.dataSource
    return res
  }
  //按可视列查询列表时需等待筛选场景获取可见列
  if (!readyGetSence && api.getState()['@common'].toggleManage?.['tg_flowlist_filed_filter']) return null
  return (
    <div className="billTableView-content">
      <LoaderWithLegacyData
        newSearch={true}
        searchOptions={searchOptions('form')}
        scenes={scenes}
        fetch={fetchPaying}
        scenesType={scenesType}
        prefixColumns={{ state: '$', '*': 'form', ownerId: '$' }}
        bus={bus}
        resource={myBill}
        baseDataProperties={baseDataProperties}
        mapping={mapping}
        isMultiSelect={false}
        expansionScenes={true}
        createNodeStaffColumn={createNodeStaffColumn}
        createNodeNameColumn={createNodeNameColumn}
        showAIIcon={showAIIcon}
      />
    </div>
  )
}

export default EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  specifications: state['@custom-specification'].specificationGroupsList
}))(BillTableViewContent)
