import React from 'react'
import Money from '../../../../elements/puppet/Money'
import EkbHighLighter from '../../../../elements/EkbHighLighter'
import styles from './ListBillItem.module.less'
import { get } from 'lodash'
import { getMyBillApproveStatus } from '../newBillItem/utils'
import { billStateMap } from '../newBillItem/staticUtil'
import classnames from 'classnames'
import { Space, Tooltip, Tag, Ellipsis } from '@hose/eui'
import { getSpecificationName } from '../../util/billUtils'
import { app as api } from '@ekuaibao/whispered'
import moment from 'moment'
import { SourceSignEnum } from '../../enum'
import {AIAgentTag} from '../../../../elements/ai-agent-utils'
const { startOpenFlowPerformanceStatistics } = api.require<any>('@lib/flowPerformanceStatistics')
import { AI_CHAT_CREATE_BILLS_KEY, AichatCreateBillCache } from '../../../../lib/constants'

interface NewListItemProps extends StringAnyProps {
  isSelect: boolean
  dataSource: any
  searchKey: string
  onClick: (param: any) => void
  isMyBill: boolean
  isShowCode?: boolean
}


export default class ListBillItem extends React.Component<NewListItemProps, {}> {
  static defaultProps = {
    isMyBill: true,
    isShowCode: true
  }

  handleClickItem = () => {
    const { onClick, dataSource } = this.props
    if (typeof onClick === 'function') {
      startOpenFlowPerformanceStatistics()
      onClick(dataSource)
    }
  }

  renderMyBillMoney = () => {
    const { dataSource: { form } } = this.props
    return <div className="bill-item-money" >
      {form.amount && Number(form.amount) !== 0 ? (
        <Money className="bold" class symbolSize={14} showStrCode={true} showSymbol={false} valueSize={14} value={form.amount} isShort={true} />
      ) : null}
    </div>
  }

  renderMyBillSubmitDate = () => {
    const { dataSource: { form } } = this.props
    const submitDate = get(form, 'submitDate', '')
    return <span className="placeholder fs-12">{moment(submitDate).format('YYYY-MM-DD HH:mm')}</span>
  }

  renderMyBillApproveStatus = () => {
    const { dataSource } = this.props
    const { flow } = dataSource
    const { state } = flow
    const { name, str } = getMyBillApproveStatus(dataSource)
    const { color, icon } = billStateMap()[state] || {}
    return <span className="bill-item-log" style={{ color: color }}>
      <div>{icon}</div>
      {!!name ? <Tooltip title={name}>
        <span className="operator-span">{name}</span>
      </Tooltip> : null}
      <span className="approve-status">{str}</span>
    </span>
  }

  renderMyBillTitle = () => {
    const { dataSource, searchKey, } = this.props
    const { form, flow, plan } = dataSource
    const { payingFailure = false, subsidyGeneration, systemGeneration, alterFlag, sourceSign } = flow
    const { isUrgent } = plan
    let showMark = false
    const samePerson = flow?.ownerId?.id === form?.submitterId?.id ? true : false
    if (!samePerson) showMark = true
    const title = form.title || i18n.get('[无标题]')
    const aichatCreateBills: AichatCreateBillCache[] = localStorage.getItem(AI_CHAT_CREATE_BILLS_KEY) ? JSON.parse(localStorage.getItem(AI_CHAT_CREATE_BILLS_KEY)) : []
    return <div className="bill-title">
      {
        Array.isArray(aichatCreateBills) && aichatCreateBills.some(item => item.id === flow.id) ? <AIAgentTag text='AI' style={{marginRight:'8px'}}/> : null
      }
      {isUrgent && <Tag size='small' className='mr-4' fill='outline' color='danger'>{i18n.get('急')}</Tag>}
      {payingFailure && <Tag size='small' className='mr-4' fill='outline' color='danger'>{i18n.get('支付失败')}</Tag>}
      {(subsidyGeneration === 'surplus' || !!systemGeneration) && (
        <Tooltip title={i18n.get('自动创建')}>
          <Tag className='mr-4' size='small' fill='outline'>{i18n.get('自动')}</Tag>
        </Tooltip>
      )}
      {sourceSign === SourceSignEnum.SYSTEM && (
        <Tooltip title={i18n.get('系统创建')}>
          <Tag  className='mr-4' size='small' fill='outline'>{i18n.get('系统')}</Tag>
        </Tooltip>
      )}
      {alterFlag >= '1' && <Tag size='small' className='mr-4' fill='outline'>{i18n.get('变更')}</Tag>}
      {showMark && <Tag size='small' className='mr-4' fill='outline'>{i18n.get('委托')}</Tag>}
      <Tooltip title={title} placement="bottom">
        <span className="bill-title-text">
          <EkbHighLighter
            highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
            searchWords={[searchKey]}
            textToHighlight={title}
          />
        </span>
      </Tooltip>
    </div>
  }

  renderMyBillCode = () => {
    const { dataSource, searchKey, } = this.props
    const { form } = dataSource
    return <EkbHighLighter
      highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
      searchWords={[searchKey]}
      textToHighlight={form.code}
    />
  }

  renderMyBillSpecification = () => {
    const { form } = this.props.dataSource
    const name = getSpecificationName(form?.specification)
    return (
      <Tooltip title={name}>
        <div>
          <Ellipsis direction="end" content={name} />
        </div>
      </Tooltip>
    )
  }

  render() {
    const { isSelect } = this.props

    return (
      <div
        className={
          classnames(
            styles['list-bill-item-wrapper'],
            i18n.currentLocale === 'zh-CN' && styles['list-bill-item-wrapper-cn'],
            { [styles['active']]: isSelect }
          )
        }
        onClick={this.handleClickItem}
      >
        <Space direction="vertical" size={8}>
          <div className="bill-log-wrapper">
            {this.renderMyBillApproveStatus()}
            {this.renderMyBillMoney()}
          </div>
          {this.renderMyBillTitle()}
          <div className="bill-info">
            <div className="bill-info-item">
              <div className="bill-info-item-label">{i18n.get('单号')}</div>
              <div className="bill-info-item-value">{this.renderMyBillCode()}</div>
            </div>
            <div className="bill-info-item">
              <div className="bill-info-item-label">{i18n.get('模板')}</div>
              <div className="bill-info-item-value">{this.renderMyBillSpecification()}</div>
            </div>
            <div className="bill-info-item">
              <div className="bill-info-item-label">{i18n.get('时间')}</div>
              <div className="bill-info-item-value">{this.renderMyBillSubmitDate()}</div>
            </div>
          </div>
        </Space>
      </div>
    )
  }
}
