import React, { useEffect, useCallback, useState } from 'react'
import { isObject } from '@ekuaibao/helpers'
import { Fetch } from '@ekuaibao/fetch'

import { isDisable } from '../../utils/fnDisableComponent'
import { getPlaceholder } from '../../utils/fnGetFieldLabel'
import { EnhanceConnect } from '@ekuaibao/store'
import { LoadableTreeSelect } from '@hose/eui'
import { dependenceLogger } from '../../utils/fnAutoDependence'
import { SearchItem, TreeItem } from './components'
import styles from './TreeSelect.module.less'

// Hooks
import {
  useDisplayValue,
  useTreeSearch,
  useTreeData,
  useTreeSelectDependence,
  useSingleTreeValue,
  useCurrencyDimension,
  useLazyLoad
} from './hooks'

interface RefCPV2RefactoredProps {
  value: any
  field: any
  bus: any
  onChange: (value: any) => void
  allStandardCurrency?: any[]
  legalEntityCurrencyPower?: any
  billSpecification: any
  specificationId: string
  submitterId: any
  billState: string
  detailId?: string
  isDetail?: boolean
  fromSupplier?: boolean
  onComponentLoadFinished?: (field: any) => void
}

const RefCPV2Refactored: React.FC<RefCPV2RefactoredProps> = props => {
  const { value, field, bus, onChange, billState, detailId, isDetail, onComponentLoadFinished } = props

  const [autoAssignmentItem, setAutoAssignmentItem] = useState<any>(null)

  // 1. 依赖关系Hook
  const dependenceHook = useTreeSelectDependence({ field, props })

  // 2. 树形数据Hook
  const treeDataHook = useTreeData({
    fetchFunction: dependenceHook.createFetchFunction(),
    formatDataFunction: dependenceHook.formatData,
    props
  })

  // 3. 搜索Hook
  const searchHook = useTreeSearch({
    fetchFunction: dependenceHook.createFetchFunction()
  })

  // 5. 货币维度Hook（复用现有实现）
  const currencyHook = useCurrencyDimension({
    field,
    bus,
    value,
    allStandardCurrency: props.allStandardCurrency,
    legalEntityCurrencyPower: props.legalEntityCurrencyPower
  })

  // 4. 显示值处理Hook
  const { displayValue, getValue, getDisplayValue } = useDisplayValue(
    value,
    field,
    props,
    dependenceHook,
    treeDataHook.treeData,
    autoAssignmentItem
  )

  // 5. 单选值处理Hook
  const valueHook = useSingleTreeValue({ field, props, currencyHook, getValue, findNodeById: treeDataHook.findNodeById })

  // 6. 解决循环依赖 - 延迟设置避免循环依赖
  const handleCancelDependency = useCallback(() => {
    // TreeSelect的取消依赖逻辑：设置不使用依赖数据，重新加载全量数据
    dependenceHook.setUseDependenceData(false)
    return initDimensionList({ cancelRelation: true })
  }, [])

  useEffect(() => {
    dependenceHook.setCancelDependencyCallback(handleCancelDependency)
  }, [dependenceHook, handleCancelDependency])

  // 7. 处理费类变化（RefCPV2特有）
  const handleFeeTypeChange = () => {
    // 天阳 bnB3nHI6Fb3qzw XSG-13395 与原有逻辑不符合，特殊处理
    const availableIds = ['bnB3nHI6Fb3qzw']
    if (!availableIds.includes(Fetch.ekbCorpId)) {
      return
    }
    if (field?.type?.startsWith('ref:basedata.Dimension') && field?.defaultValue?.value) {
      // 重新render默认值
      onChange(null)
    }
  }

  // 8. 初始化维度列表
  const initDimensionList = async (query: any = {}) => {
    const { isSpecialCancelDependency, _isUseDep } = dependenceHook.getDependenceStatus()
    if (isSpecialCancelDependency || !_isUseDep || query?.cancelRelation) {
      // 不使用依赖数据，使用全量数据
      const result = await treeDataHook.loadNodeData('', query)
      if (result) {
        const { items, hasNextPage } = result
        dependenceHook.setDependenceList(dependenceHook.formatData(items))
        treeDataHook.initTreeState(dependenceHook.formatData(items), hasNextPage)
        return items
      }
    }
  }

  const { handleDropdownVisibleChange, getDropdownContent, loading } = useLazyLoad({
    fetchData: async () => {
      const { fromSupplier = true } = props
      const obj = fromSupplier === false ? { visible: fromSupplier, active: true } : {}
      return initDimensionList({ ...obj })
    }
  })

  // 9. 取消依赖处理已经集成到handleCancelDependency中

  // 10. 事件监听
  useEffect(() => {
    const handleDependenceChange = async (event: any, options: any) => {
      const result = await dependenceHook.processDependenceChange(event, options)
      if (result) {
        const { autoAssignmentItem, items, matchDefaultValue, hasNextPage } = result
        let newValue = value

        // 非单据新建及费类新建（费类有detailId 则为编辑）
        const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit

        // 非初始化加载时才赋值
        if (!isInitLoad && autoAssignmentItem) {
          valueHook.handleChange(autoAssignmentItem)
          newValue = autoAssignmentItem
          setAutoAssignmentItem(autoAssignmentItem)
        }

        // 没有自动赋值项且没有匹配默认值时清空
        if (!autoAssignmentItem && !matchDefaultValue) {
          valueHook.handleChange(undefined)
          newValue = undefined
        }

        dependenceLogger(event.key, newValue, props)

        currencyHook.handleLegalEntityMultiCurrency(items, value, newValue)

        // 同步更新树形数据，确保 handleLoadMore 能正常工作
        if (items) {
          treeDataHook.initTreeState(dependenceHook.formatData(items), hasNextPage)
        }

        if (!isInitLoad) {
          currencyHook.fnHandleDimentionCurrenncy(newValue, false)
        }
      }
    }

    bus.on('on:dependence:change', handleDependenceChange)
    bus.watch('fee:detail:feetype:change', handleFeeTypeChange)
    bus.on('update:dimention:currency:by:dimension:value', currencyHook.fnHandleDimentionCurrenncy)
    bus.on('set:delegator', dependenceHook.handleDelegatorChanged)

    return () => {
      bus.un('on:dependence:change', handleDependenceChange)
      bus.un('fee:detail:feetype:change', handleFeeTypeChange)
      bus.un('update:dimention:currency:by:dimension:value', currencyHook.fnHandleDimentionCurrenncy)
      bus.un('set:delegator', dependenceHook.handleDelegatorChanged)
    }
  }, [])

  // 11. 初始化逻辑
  useEffect(() => {
    setTimeout(() => {
      const id = isObject(value) ? value.id : value
      if (bus) {
        bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true })
      }
      currencyHook.fnHandleDimentionCurrenncy(value, true)
    }, 200)

    if (onComponentLoadFinished && !dependenceHook.hasEmitHandleDependenceChangeRef.current) {
      onComponentLoadFinished(field)
    }
  }, [])

  useEffect(() => {
    getDisplayValue(value)
  }, [])

  // 12. 值变化处理逻辑（让useSingleTreeValue处理）
  useEffect(() => {
    valueHook.handleValueChange()
  }, [value, field, bus])

  // 13. 处理节点变化
  const handleNodeChange = (newValue: any) => {
    valueHook.handleChange(newValue)
  }

  // 14. 渲染逻辑
  const disabled = isDisable(props)
  const { optional, multiple, defaultPlaceholder, isCanViewAllDataWithResultBlank } = field
  let placeholder = getPlaceholder(field)

  if (optional) {
    placeholder = defaultPlaceholder ? i18n.get(placeholder) : i18n.get('（选填）') + i18n.get(placeholder)
  }

  const { dependenceViewAllData } = dependenceHook.getDependenceStatus()
  const { searchMode, searchResults } = searchHook

  let showAllPath = field && field.isShowFullPath
  const treeNodeLabelProp = showAllPath ? '_fullPath' : 'title'

  const treeRenderData = searchMode
    ? searchResults.map(item =>
        treeDataHook.formatSearchItem(item, (item: any) => (
          <SearchItem {...item} searchText={searchHook.currentSearchTextRef.current} />
        ))
      )
    : treeDataHook.toRenderTreeData(treeDataHook.treeData, (item: any) => <TreeItem {...item} />)

  return (
    <LoadableTreeSelect
      data-testid="custom-dimension-tree-select"
      popupClassName={`${styles['custom-dimension-tree-select-popup']} ${searchMode ? styles['search-mode'] : ''}`}
      showSearch
      style={{ width: '100%' }}
      disabled={disabled}
      placeholder={placeholder}
      value={displayValue}
      showCheckedStrategy={'SHOW_ALL'}
      onSearch={searchHook.handleSearch}
      allowClear={optional || true}
      notFoundContent={getDropdownContent({
        isEmpty: treeRenderData.length === 0,
        loading: dependenceHook.isLoading || loading || searchHook.searchPageInfo.loading,
        notFoundContent:
          dependenceViewAllData || isCanViewAllDataWithResultBlank ? (
            <div className="cancel-dependence">
              {i18n.get('暂无匹配结果')}，{i18n.get('点击')}
              <a href="javascript:void 0" onClick={dependenceHook.cancelDependence}>
                {i18n.get('查看全量数据')}
              </a>
            </div>
          ) : (
            undefined
          )
      })}
      autoClearSearchValue={false}
      showArrow
      size="middle"
      multiple={multiple}
      onChange={handleNodeChange}
      treeNodeLabelProp={treeNodeLabelProp}
      filterTreeNode={false}
      treeExpandedKeys={treeDataHook.treeExpandedKeys}
      onTreeExpand={treeDataHook.handleTreeExpand}
      treeData={treeRenderData}
      hasMore={(node: any) => {
        if (searchMode) {
          // 搜索模式下，只有根层级有加载更多
          return searchHook.searchPageInfo.hasNextPage && !searchHook.searchPageInfo.loading
        } else {
          // 树形模式下，每个节点都可能有加载更多
          const nodeKey = node?.id || 'root'
          const pageInfo = treeDataHook.nodePageInfo[nodeKey]
          return pageInfo?.hasNextPage && !pageInfo?.loading
        }
      }}
      onLoadMore={searchMode ? searchHook.handleSearchLoadMore : treeDataHook.handleLoadMore}
      onDropdownVisibleChange={handleDropdownVisibleChange}
    />
  )
}

export default EnhanceConnect((state: any) => ({
  allStandardCurrency: state['@common'].allStandardCurrency,
  legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency
}))(RefCPV2Refactored)
