import React, { PureComponent } from 'react'
import { FilledTipsClose } from '@hose/eui-icons'
import { Button } from '@hose/eui'
import { get } from 'lodash'
import { PaymentState, Status, PayResult } from './paymentInterface'
import { finishView, keyloadView } from './element/view-util'
import { getParams, signSeqNos } from './util'
import Logo from './logo/logo'
import './payment.less'
import { CGBCMSConfirmPay } from './action'

const Btn: any = Button

export default class PaymentView extends PureComponent<StringAnyProps, PaymentState> {
  constructor(props: StringAnyProps) {
    super(props)
    const { count, money } = getParams()
    this.state = {
      status: Status.KeyLoading,
      count,
      money,
      failureReason: '支付失败'
    }
  }

  async componentDidMount() {
    this.reselectCer()
  }

  reselectCer = () => {
    this.setState({ status: Status.KeyLoading })
    setTimeout(async () => {
      try {
        const result = await cgbUkey.selectCertificate({ cspNameList: 'Uyee CSP v6.0 (SQ)' })
        if (result) {
          this.setState({ status: Status.KeySuccess })
        }
      } catch (err) {
        console.log(err)
      }
    }, 1000)
  }

  handleConfirmPay = async () => {
    this.setState({ status: Status.PayLoading })
    let data: any = {}
    try {
      data = await signSeqNos()
    } catch (e) {
      console.log('U盾抛错:', e)
      return this.setState({ status: Status.KeySuccess }, () => alert(e))
    }
    try {
      const { batchId } = getParams()
      const params = {
        batchId,
        signature: data?.signOnInfo,
        userName: data?.certInfo?.userName,
        validFrom: data?.certInfo?.validFrom,
        validTo: data?.certInfo?.validTo,
        serialNumber: data?.certInfo?.serialNumber,

      }
      const { state, failureReason } = await CGBCMSConfirmPay(params)
      if (state === PayResult.FAILURE) {
        return this.setState({ status: Status.PayFailed, failureReason })
      } else {
        return this.setState({ status: Status.PaySuccess })
      }
    } catch (e) {
      const m = JSON.stringify(e)
      const msg = get(e, 'message', m)
      console.log('msg---', msg);
    }
  }

  renderUkeyView = (): JSX.Element => {
    const { status, count, money } = this.state
    const { strCode } = getParams()
    return (
      <>
        <div className="title">{i18n.get('支付确认')}</div>
        <div className="money-wrap">
          <span>
            {i18n.get('selectedBill', { count })}
            {i18n.get('总计支付金额')}
            <span className="red">
              {strCode || 'CNY'} {money}
            </span>
          </span>
        </div>
        {keyloadView(status, this.reselectCer)}
        <Btn onClick={this.handleConfirmPay} disabled={status !== Status.KeySuccess} className="finish-button">
          {i18n.get('确认')}
        </Btn>
      </>
    )
  }

  renderUnSupport = (): JSX.Element => {
    return (
      <>
        <FilledTipsClose className="filled-tips-close" />
        <div className="right">
          <div className="tips-top">{i18n.get('系统暂不支持')}</div>
          <div className="tips-bottom">{i18n.get('目前仅支持Windows系统')}</div>
        </div>
      </>
    )
  }

  renderContent = (): JSX.Element => {
    if (isMac()) {
      return this.renderUnSupport()
    }
    const { status, failureReason } = this.state
    if (status.startsWith('ukey')) {
      return this.renderUkeyView()
    }
    return finishView(status, failureReason)
  }

  render() {
    const { status } = this.state
    const flag = isMac() || status === Status.PaySuccess || status === Status.PayFailed
    const clg = flag ? 'content-w416' : 'content-w430'
    return (
      <div id="__CGBPAYMENT" className="cgb-wrap">
        <div className="layout-header">
          <Logo className="layout-header-logo" />
        </div>
        <div className={clg}>{this.renderContent()}</div>
      </div>
    )
  }
}
