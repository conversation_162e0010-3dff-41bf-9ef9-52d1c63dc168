/**************************************************
 * Created by nany<PERSON>ingfeng on 15/07/2017 13:15.
 **************************************************/
import styles from './FeeDetailEdit.module.less'
import { MoneyMath } from '@ekuaibao/money-math'
import React, { PureComponent } from 'react'
import { Col, Row } from 'antd'
import { Modal } from '@hose/eui'
import { EnhanceConnect } from '@ekuaibao/store'
import MessageCenter from '@ekuaibao/messagecenter'
import {
  needUpdateResult,
  needUpdateCustomizeResult,
  parseAsMeta,
  parseApproveModifyLimitField
} from '../../util/parse'
import FeeTypeItem from '../../../../elements/puppet/view-item/FeeTypeItem'
import SelectCivilServiceCard from '../../../../elements/puppet/view-item/SelectCivilServiceCard'
import DynamicEditable from '../../views/DynamicEditable'
import FeeDetailBottom from './FeeDetailBottom'
import { Fetch } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import { cloneDeep, debounce, each, get, isEmpty, isEqual } from 'lodash'
import { isAllowModifyFiled } from '../../../../components/utils/fnDisableComponent'
import { inputInvoiceImport } from '../../parts/right-part/billInfo/BillImport/invoiceImport'
import WarningOrErrorTips from '../../elements/WarningOrErrorTips'
import { getBoolVariation } from '../../../../lib/featbit'
import {
  getInvoiceIdsByFeeTypeForm,
  getForeignNumCode,
  getDefaultFeeType,
  getFeeTypeById,
  initDefaultValue,
  isSelectCurrencyDisable,
  getBillTemplateAddFeeReceivingAmount,
  getAllowSelectionReceivingCurrency,
  getBillReceivingCurrency,
  addReceivingAmountValue,
  validateFeeTypeOnlyCurrency,
  validateInvoicesMoneyLessThanAmount,
  updateInvoiceDeductionAndSetFieldsValue
} from '../../../../lib/fee-util'
import ThirdPartyCard from 'ekbc-thirdParty-card/esm/thirdCard'
import {
  getValidateErrorByShow,
  fnLinkDetailEntitiesValue,
  splitTemplateToGroups,
  checkCSCMoney,
  filterCSCFields,
  billSelectStaff,
  getSpecificationHiddenFields
} from '../../util/billUtils'
const { PAYEE_INFO } = api.require('@lib/lib-util')
import ThirdCard from '../../../../elements/thirdCard/third-card'
import InvoiceCard from '../../../../elements/invoice-card'
import { getNodeValueByPath, showLoading, hideLoading } from '@ekuaibao/lib/lib/lib-util'
import { showMessage } from '@ekuaibao/show-util'
import EKBIcon from '../../../../elements/ekbIcon'
import { saveFeeDetailLog } from '../../util/trackLogs'
import {
  disableTemplateField,
  getExtraRiskWarningList,
  getRiskFieldNum,
  getRiskInTemplate,
  getValidRiskWarningData,
  setFieldsExternal
} from '../../riskWarning/formatRiskWarningData'
import {
  getAutoCalculationResult,
  getCalculateField,
  getPresetTemplate,
  getFeeTypeTemplateById,
  setValidateError,
  getOfficialCardSetting,
  getPayments,
  getCurrencyRatesById,
  getEffectiveCurrencyInfo
} from '../../bills.action'
import {
  handleDataLinkUseCount,
  filterDataLinkFields,
  filterAutoAssignDataLinkField,
  getDependenceFieldOfDataLink,
  getDataLinkPermissionFieldComponent,
  handleAutoAssignOfOneResultOfDataLink
} from '../dataLink/dataLinkUtil'
import { getAcountKey, standardValueMoney } from '../../../../lib/misc'
import { getInvoicesAmount } from '../../../../lib/InvoiceUtil'
import FeeTypeEmpty from './FeeTypeEmpty'
import { getParam, updateAutoCalResult } from '../../util/autoCalculate'
import { updateCustomizeCalResult } from '../../util/customizeCalculate'
import { getV, isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import getDefaultValue from '../../../../components/utils/getInitialValue'
import InvoiceTaxInfo from '../../../../elements/CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import { isObject, uuid } from '@ekuaibao/helpers'
import { related } from '../../../../elements/feeDetailViewList/Related'
import { handlerCurrencyMoneySelectChange, setDefaultCurrencyMoney } from '../../util/defaultCurrency.js'
import { handleGetDimensionById, constantValue } from '../../../../components/utils/fnInitalValue'
import classNames from 'classnames'
import { getIsCheckerInvoice } from '../../../../elements/InvoiceCard/InvoiceItem'
import { isElectronicAirAndTrain } from '../import-bill/invoice/ImportInvoiceUtils'
import { callFnByQueue, callFnByQueueNew } from '../../util/callFbByQueue'
import { fnParseTemplateFields, fetchAttachmentConfig } from '../../../../lib/fnParseTemplateField'
import { linkAssignmentFieldsToValue } from '../../../../components/utils/fnLinkAssignmentHelper'
import { updateReceivingAmountMoney, foreignTimesRate } from '../../../../components/utils/fnCurrencyObj'
import { useNewAutomaticAssignment } from '../../../../components/utils/fnAutoDependence'
import { newVersionOPaymenAccount } from '../../../../lib/featbit/feat-switch'
import moment from 'moment'

const svg = (
  <span style={{ display: 'inline-flex' }}>
    <EKBIcon name="#EDico-didi" style={{ color: '#1E96FA', width: '16px', height: '16px' }} />
  </span>
)

const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 16 }
}

@EnhanceConnect(
  state => {
    return {
      baseDataProperties: state['@common'].globalFields.data,
      mappingRelation: state['@tpp-v2'].mappingRelation,
      lastChoice: state['@common'].lastChoice.choiceValue,
      multiplePayeesMode: state['@bills'].multiplePayeesMode,
      payPlanMode: state['@bills'].payPlanMode,
      baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap,
      validateError: state['@bills'].validateError,
      visibleFeeTypeByFormDataMap: state['@bills'].visibleFeeTypeByFormDataMap,
      dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo,
      canUseDefaultCurrency: state['@common']?.powers?.isSetDefaultCurrency, //默认币种设置
      customizeQueryPower: state['@common'].powers.customizeQuery,
      KA_DISABLE_INVOICE: state['@common'].powers.KA_DISABLE_INVOICE,
      allStandardCurrency: state['@common'].allStandardCurrency,
      civilServiceCard: state['@common'].powers.CivilServiceCard,
      autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
      allCurrencyRates: state['@bills'].billsRateList
    }
  },
  {
    getFeeTypeTemplateById,
    getAutoCalculationResult,
    getCalculateField,
    setValidateError
  }
)
export default class FeeTypeDynamic extends PureComponent {
  constructor(props) {
    super(props)
    let { dataSource, foreignNumCode, selectCurrencyDisable, billSpecification, detailInitValue, billData } = props
    const value = dataSource ? dataSource.feeTypeForm : {}
    const cValue = detailInitValue || fnLinkDetailEntitiesValue(value)
    let allowReceivingCurrency = getAllowSelectionReceivingCurrency(billSpecification.configs, billData)
    allowReceivingCurrency && addReceivingAmountValue(billData, cValue)
    this.state = {
      hiddenFields: [],
      doc: { id: '' },
      template: undefined,
      details: props.details || [],
      value: cValue,
      max: NaN,
      min: NaN,
      externalData: {}, //分配到编辑消费详情页面中的超标提醒数据
      externalToFather: [], //点击保存时，传递给父级BillInfoEditable修改过的数据
      extraRiskWarningList: void 0,
      riskFieldNum: 0,
      riskInTemplateList: [],
      didiAuth: {},
      dimension: [],
      recommendFeeTypes: [],
      selectCurrencyDisable,
      foreignNumCode,
      currentSpecification: billSpecification,
      presetTemplateId: '', // 快速报销根据企业id获取预置单据模版id，快速报销菜单中报销明细自动计算使用
      legalEntityMultiCurrency: '', // 快速报销根据企业id获取预置法人实体多币种id，快速报销菜单中报销明细自动计算使用
      getPresetTemplateCount: 0, // 查询presetTemplateId的次数，小概率有第一次查不到的情况，重新查一次
      settlementId: '', // 公务卡设置的结算方式ID
      referenceDataType: {},
      ordersData: cValue?.ordersData || [],
      orders: cValue?.orders || [],
      propsOrdersData: cValue?.ordersData ? cloneDeep(cValue?.ordersData) : [],
      dependenceFieldOfDataLink: []
    }
    this.isTestingEnterprise = isHongShanTestingEnterprise(Fetch.ekbCorpId)
    this.sefBus = new MessageCenter()
    this.invoiceTaxInfo = new InvoiceTaxInfo()
    const getUnifyCorporationList = api.invokeService('@invoice-manage:import:getUnifyCorporationList')
    api.dispatch(getUnifyCorporationList())
    this.timer = new Date().getTime()
    this.oneResultDataLinkLock = false
    this.allowSelectionReceivingCurrency = allowReceivingCurrency
    this.saveType = '明细保存'
  }

  componentWillMount() {
    const { bus } = this.props
    api.invokeService('@tpp-v2:import:corporationBinding').then(res => {
      if (res.payload && res.payload.items && res.payload.items[0] && res.payload.items[0].platformId == 'DIDI') {
        this.setState({ didiAuth: res.payload.items[0] })
      }
    })
    bus.watch('element:ref:select:staff', this.fnSelectStaff)
    bus.watch('element:ref:select:payee', this.fnSelectPayee)
    bus.watch('element:ref:select:staffs', this.fnSelectStaffs)
    bus.watch('dynamic:value:changed', this.handleDynamicValueChange)
    bus.watch('dynamic:value:blur', this.handleDynamicValueBlur)
    bus.watch('element:select:dataLink', this.handleSelectDataLink)
    bus.watch('element:details:import:click', this.fnDetailsImportClick)
    bus.watch('element:details:import:afp:click', this.fnDetailsAifapiaoImportClick)
    bus.watch('element:details:import:ocr:click', this.fnDetailsImportOCRClick)
    bus.watch('element:details:input:import:click', this.fnDetailsInputImportClick)
    bus.watch('element:details:aliPay:import:click', this.fnDetailsAliPayImportClick)
    bus.on('cancel:action', this.handleCancel)
    bus.on('save:action', this.handleSave)
    bus.on('saveContinue:action', this.handleSaveContinue)
    bus.on('saveDraft:action', this.handleSaveDraft)
    bus.on('update:calculate:template', this.updateCalTemplate)
    api.watch('get:feetype:form:value', this.handleGetFormValue)
    api.on('external:APPORTION:edit', this.resetDetailsExternalsData)
    api.on('external:APPORTION:empty', this.emptyApportionsExternalsData)
    bus.on('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    api.dataLoader('@common.payerInfo').load()
    bus.on('invoice:disable:click', this.handleInvoiceDisable)
    api.watch('apportion:line:changed', this.handleEditApportion)
    bus.watch('send:quickExpendsParams', this.handleSendQuickExpendsParams)
    api.watch('get:current:feeType:value', this.handleCurrentFeeTypeValue)
    bus.watch('timeField:change', this.handleChangeTimeField)
  }

  componentWillUnmount() {
    const { bus, isQuickExpends } = this.props
    bus.un('element:ref:select:staff', this.fnSelectStaff)
    bus.un('element:ref:select:payee', this.fnSelectPayee)
    bus.un('dynamic:value:changed', this.handleDynamicValueChange)
    bus.un('dynamic:value:blur', this.handleDynamicValueBlur)
    bus.un('element:select:dataLink', this.handleSelectDataLink)
    bus.un('element:details:import:click', this.fnDetailsImportClick)
    bus.un('element:details:import:afp:click', this.fnDetailsAifapiaoImportClick)
    bus.un('element:details:input:import:click', this.fnDetailsInputImportClick)
    bus.un('element:details:aliPay:import:click', this.fnDetailsAliPayImportClick)
    bus.un('cancel:action', this.handleCancel)
    bus.un('save:action', this.handleSave)
    bus.un('saveContinue:action', this.handleSaveContinue)
    bus.un('saveDraft:action', this.handleSaveDraft)
    bus.un('update:calculate:template', this.updateCalTemplate)
    bus.un('element:ref:select:staffs', this.fnSelectStaffs)
    api.un('external:APPORTION:edit', this.resetDetailsExternalsData)
    api.un('get:feetype:form:value', this.handleGetFormValue)
    api.un('external:APPORTION:empty', this.emptyApportionsExternalsData)
    bus.un('element:details:import:ocr:click', this.fnDetailsImportOCRClick)
    bus.un('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
    bus.un('invoice:disable:click', this.handleInvoiceDisable)
    api.un('apportion:line:changed', this.handleEditApportion)
    bus.un('send:quickExpendsParams', this.handleSendQuickExpendsParams)
    api.un('get:current:feeType:value', this.handleCurrentFeeTypeValue)
    bus.un('timeField:change', this.handleChangeTimeField)
    if (isQuickExpends) {
      api.invokeService('@bills:update:dimention:currency', null)
    }
  }

  // 根据时间获取对应时期的本位币所对应的汇率
  handleChangeTimeField = async ({ date }) => {
    const { timeField, bus, billData, dimentionCurrencyInfo } = this.props
    bus.emit('savebtn:state:change', { disabled: true })
    const time = date || date?.[timeField] || billData?.[timeField]
    const endOfDay = moment(time)
      .endOf('day')
      .valueOf() // 获取当天最后一秒的时间戳
    const standardCurrency = dimentionCurrencyInfo?.currency || api.getState()['@common']?.standardCurrency
    if (!endOfDay) {
      const allCurrencyRates = await api.dataLoader('@common.allCurrencyRates').load()
      const allCurrency = allCurrencyRates.filter(i => i.originalId === standardCurrency?.numCode)
      const rates = allCurrency || []
      bus.emit('set:history:currency:rate', rates)
      this.setReceivingAmounRate(rates)
      this.setState({ detalsAllCurrencyRates: rates })
    } else {
      const currencyNumCode = standardCurrency.numCode
      const historyCurrencyInfo = await getEffectiveCurrencyInfo({ originalId: currencyNumCode, time: endOfDay })
      const historyCurrency = historyCurrencyInfo?.items
      bus.emit('set:history:currency:rate', historyCurrency)
      this.setReceivingAmounRate(historyCurrency)
      this.setState({ detalsAllCurrencyRates: historyCurrency })
    }
    bus.emit('savebtn:state:change', { disabled: false })
  }

  // 设置收款金额的汇率
  setReceivingAmounRate = async rates => {
    const { bus } = this.props
    const value = await bus.getFieldsValue()
    const receivingAmount = value?.receivingAmount
    // 无值的情况下
    if (receivingAmount?.standardNumCode && receivingAmount?.foreignNumCode && receivingAmount?.rate) {
      const moneyValue = foreignTimesRate(receivingAmount, receivingAmount.standardNumCode, rates)
      bus.setFieldsValue({
        receivingAmount: { ...moneyValue },
        isSetFielAmountTrigger: true
      })
    }
  }

  handleCurrentFeeTypeValue = () => {
    const { doc, currentSpecification } = this.state
    return {
      formSpecId: currentSpecification?.id ?? '',
      feeTypeSpecId: doc?.id ?? ''
    }
  }

  handleSendQuickExpendsParams = () => {
    const { presetTemplateId, legalEntityMultiCurrency } = this.state
    return { presetTemplateId, legalEntityMultiCurrency }
  }

  handleInvoiceDisable = params => {
    const { invoiceId, reason } = params
    let { disableInvoiceList = {} } = this.state
    disableInvoiceList[invoiceId] = reason?.disableReason
    this.setState({ disableInvoiceList })
  }

  updateCalTemplate = value => {
    const { bus, external } = this.props
    const { template } = this.state
    const clearValidFields = []
    const fields = Object.keys(value)
    fields.forEach(field => {
      let index = template.findIndex(el => el.field === field)
      if (index !== -1) {
        template[index] = { ...template[index], ...value[field] }
        if (value[field]['optional']) {
          clearValidFields.push(field)
        }
      }
    })
    this.setState({ template: [...template] }, () => {
      if (clearValidFields && clearValidFields.length) {
        bus.getValueWithValidate(clearValidFields)
        if (external) {
          const clearFieldMap = clearValidFields.reduce((result, cur) => {
            result[cur] = cur
            return result
          }, {})
          const keys = Object.keys(external)
          const noClearFields = keys.filter(field => !clearFieldMap[field])
          if (noClearFields?.length) {
            const noClearExternal = {}
            noClearFields.forEach(field => {
              noClearExternal[field] = external[field]
            })
            setFieldsExternal({ riskWarning: noClearExternal, bus })
          }
        }
      }
    })
  }

  handleGetFormValue = () => {
    const { bus } = this.props
    return bus.getValue()
  }

  handleGetPresetTemplate = () => {
    const { isQuickExpends, bus, allStandardCurrency } = this.props
    const { presetTemplateId, getPresetTemplateCount } = this.state
    if (isQuickExpends && !presetTemplateId && !getPresetTemplateCount) {
      // 根据企业id获取预置单据模版id, 第一次无id则需要重新查一次
      getPresetTemplate().then(async res => {
        const specificationId = get(res, 'value.specificationId')
        const legalEntityMultiCurrency = get(res, 'value.legalEntityMultiCurrency')
        if (specificationId && legalEntityMultiCurrency) {
          this.setState({ presetTemplateId: specificationId, getPresetTemplateCount: 1, legalEntityMultiCurrency })
          const result = await handleGetDimensionById(legalEntityMultiCurrency)
          const baseCurrencyId = result?.form?.baseCurrencyId
          if (baseCurrencyId) {
            const { items: rates = [] } = await api.invokeService(
              '@currency-manage:get:currency:rates:by:Id',
              baseCurrencyId
            )
            const currency = allStandardCurrency.find(item => item.id === baseCurrencyId)
            api.invokeService('@bills:update:dimention:currency', { currency, rates })
          }
        } else {
          this.setState({ getPresetTemplateCount: 1 }, () => this.handleGetPresetTemplate())
        }
      })
    }
  }

  async componentDidMount() {
    const { dataSource, isRecordExpends, visibleFeeTypes, civilServiceCard, isModify } = this.props
    this.handleGetPresetTemplate()
    const isFeeTypeEmpty = this.fnIsFeeTypeEmpty()

    const recommendFeeTypes = isModify ? [] : await this.getRecommendFeeTypes()

    if (dataSource) {
      //编辑消费详情
      this.renderFeeTypeTemplate(undefined, { isInit: true })
    } else {
      //新增消费详情
      if (isFeeTypeEmpty) {
        // 设置了默认类型 与编辑消费详情执行的逻辑是一样的
        let defaultFeeType = []
        if (isRecordExpends) {
          defaultFeeType = visibleFeeTypes.filter(oo => oo.id.endsWith('notes'))
        }
        this.renderFeeTypeTemplate(isRecordExpends ? defaultFeeType : recommendFeeTypes, { isInit: true })
      } else {
        // 没有设置默认类型
        this.setState({ doc: { id: '' } })
      }
    }
    if (civilServiceCard) {
      const res = await getOfficialCardSetting()
      const referenceDataType = get(res, 'value.referenceDataType', {})
      this.setState({ referenceDataType })

      const settlementId = get(res, 'value.settlementId')
      if (settlementId) {
        const paymentsRes = await getPayments()
        const payments = get(paymentsRes, 'items', [])
        if (payments.find(el => el.id === settlementId)) {
          this.setState({ settlementId: payments.find(el => el.id === settlementId) })
          return payments.find(el => el.id === settlementId)
        } else {
          showMessage.warning('未匹配到结算方式，请联系管理员')
        }
      }
    }
    this.setState({ recommendFeeTypes })
  }

  timeout = (ms, defaultResult) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(defaultResult)
      }, ms)
    })
  }

  getRecommendFeeTypes = async () => {
    const { dataSource, billSpecification, visibleFeeTypeByFormDataMap, submitterId } = this.props
    const invoices = getInvoiceIdsByFeeTypeForm(dataSource)
    try {
      let { items: recommendFeeTypes = [] } = await Promise.race(
        [
          api.invokeService('@bills:get:feeType:recommend', {
            specificationId: billSpecification.id,
            invoices,
            staffId: isObject(submitterId) ? submitterId.id : submitterId
          }),
          this.timeout(5000, {}),
        ]
      )
      if (recommendFeeTypes.length && visibleFeeTypeByFormDataMap) {
        recommendFeeTypes = recommendFeeTypes.filter(oo => !!visibleFeeTypeByFormDataMap[oo.id])
      }

      return recommendFeeTypes
    } catch {
      //
    }
  }

  triggerFeeTypeDependence = field => {
    const hasDepenceFeeType = field?.dependence?.find(dependence => dependence?.dependenceId === 'flow.FeeType')
    return !!hasDepenceFeeType
  }

  handleComponentLoadFinish = field => {
    if (this.triggerFeeTypeDependence(field)) {
      const { bus } = this.props
      const { doc } = this.state
      bus.emit(
        'on:dependence:change',
        { key: 'flow.FeeType', id: doc?.id, dependenceFeeType: true },
        {
          isInit: true
        }
      )
    }
  }

  // 判断当前单据是否有默认费用类型
  fnIsFeeTypeEmpty = () => {
    let { billSpecification } = this.props
    const detail = billSpecification.components.filter(a => {
      return a.type === 'details' || a.type === 'requisitionDetails'
    })
    const isFeeTypeEmpty = !!detail.length && (detail[0].isFeeTypeEmpty ? detail[0].isFeeTypeEmpty : false)
    return !isFeeTypeEmpty
  }

  getOtherFeeTypeById = (list = [], feeTypeId) => {
    let feeType = undefined
    let fn = (list, feeTypeId) => {
      each(list, item => {
        if (feeType) return
        if (item.id !== feeTypeId && !item.children?.length) {
          feeType = item
          return
        }
        fn(item.children, feeTypeId)
      })
    }
    if (list && list.length > 0) fn(list, feeTypeId)
    return feeType
  }

  renderFeeTypeTemplate = async (recommendFeeTypes = [], option) => {
    let {
      dataSource,
      bus,
      visibleFeeTypes,
      temFeeType, // 当前场景不可用的费用明细
      details,
      submitterId,
      external,
      setValidateError,
      disableFields,
      customizeQueryPower,
      isModify,
      currentNode,
      originalValue,
      billSpecification
    } = this.props
    let selectedFeeType = (dataSource && dataSource.feeTypeId) || (dataSource && dataSource.feeType)
    let feeType =
      temFeeType && visibleFeeTypes?.length > 1
        ? this.getOtherFeeTypeById(visibleFeeTypes, temFeeType.id)
        : dataSource
          ? selectedFeeType
          : recommendFeeTypes.length
            ? recommendFeeTypes[0]
            : getDefaultFeeType(visibleFeeTypes)
    this.submitterId = typeof submitterId === 'object' ? submitterId.id : submitterId

    const riskFieldNum = getRiskFieldNum(external)
    const params = { riskWarning: external, bus }
    this.getFeeTypeTemplate(feeType)
      .then(async ({ template, specification, isUseOldVersion }) => {
        const hiddenFields = await getSpecificationHiddenFields(specification)
        return { template, specification, isUseOldVersion, hiddenFields }
      })
      .then(({ template, specification, isUseOldVersion, hiddenFields }) => {
        let tem = disableTemplateField(disableFields, template)
        if (isModify) {
          tem = parseApproveModifyLimitField(currentNode, tem)
        }
        const extraRiskWarningList = getExtraRiskWarningList(template, external)
        const riskInTemplateList = getRiskInTemplate(template, external)
        setValidateError({ detail: [] })
        tem = getBillTemplateAddFeeReceivingAmount(tem, this.allowSelectionReceivingCurrency)
        specification.components = getBillTemplateAddFeeReceivingAmount(
          specification?.components,
          this.allowSelectionReceivingCurrency
        )
        this.setState(
          {
            hiddenFields,
            template: tem,
            specification,
            doc: feeType,
            externalData: external,
            extraRiskWarningList,
            riskFieldNum,
            riskInTemplateList
          },
          () => {
            setTimeout(() => {
              bus.emit(
                'on:dependence:change',
                { key: 'flow.FeeType', id: feeType.id, dependenceFeeType: true },
                {
                  isInit: option?.isInit
                }
              )
              originalValue?.billFeeForceValidation && this.initValidateByModify()
              this.initDataLinkPermissionFields(feeType)
              this.initAutoAssignOfOneResultOfDataLink()
            }, 200)
          }
        )
        // 和移动端逻辑保持一致 isUseOldVersion 为 true 时，不触发自动计算
        if (!isUseOldVersion || isModify) {
          //明细已保存过再次打开时onlyQuery=true，联查不用重新赋值，仅查询监听字段;
          const onlyQuery = !!dataSource && typeof dataSource.idx !== 'undefined'
          customizeQueryPower ? this.updateAutoCalFields(feeType, onlyQuery) : this.updateAutoCalFields(feeType)
        }
      })
    this.fnFixMinAndMax(details, dataSource)
    let setFieldsExternalTimer = setInterval(() => {
      if (bus.setFieldsExternalsData) {
        setFieldsExternal({ riskWarning: external, bus })
        clearInterval(setFieldsExternalTimer)
        setFieldsExternalTimer = null
      }
    }, 300)
  }

  handleFeeTypeChange = feeTypeId => {
    const {
      visibleFeeTypes,
      bus,
      dataSource,
      setValidateError,
      civilServiceCard,
      isModify,
      dimentionCurrencyInfo,
      billSpecification,
      billData,
      timeField
    } = this.props
    // todo
    const { template: oldTemplate, doc, ordersData } = this.state
    const feeType = getFeeTypeById(visibleFeeTypes, feeTypeId)
    bus.emit('savebtn:state:change', { disabled: true })
    this.getFeeTypeTemplate(feeType)
      .then(async ({ template, specification, isUseOldVersion }) => {
        const hiddenFields = await getSpecificationHiddenFields(specification)
        template = getBillTemplateAddFeeReceivingAmount(template, this.allowSelectionReceivingCurrency)
        // specification.components 没有用 template中的模板，自己替换了下，因为template中有一些bus 属性，在 addFullPathInApportion 中的 cloneDeep会报错
        specification.components = getBillTemplateAddFeeReceivingAmount(
          specification?.components,
          this.allowSelectionReceivingCurrency
        )
        return { template, specification, isUseOldVersion, hiddenFields }
      })
      .then(async ({ template, specification, isUseOldVersion, hiddenFields }) => {
        const invoiceForm = template.filter(v => v.field === "invoiceForm" && v.type === 'invoice')
        if (isModify && dimentionCurrencyInfo?.currency?.id === '156') {
          if (bus.has('fee:detail:feetype:change')) {
            await bus.invoke('fee:detail:feetype:change', feeType, { template })
          }
        }
        if (!isModify && bus.has('fee:detail:feetype:change')) {
          await bus.invoke('fee:detail:feetype:change', feeType, { template })
        }
        if(bus.getValue){
          // 新建费类没有默认费类时，组件能力未和dynamic中的bus融合
          const feeDetailsValue = await bus.getValue()
          // 切换费类事件 bus.invoke('fee:detail:feetype:change“）事件在doc.id 之后执行，导致数据偶尔出现错误
          // 所以提取一个方法执行，初步怀疑是bus.invoke 执行顺序问题，但是没有找到源头，暂时先这样处理
          if(invoiceForm?.length && feeDetailsValue?.invoiceForm?.invoices?.length) {
            await updateInvoiceDeductionAndSetFieldsValue(feeType, {
              bus: bus,
              template: template,
              value: feeDetailsValue.invoiceForm,
              billSpecification: billSpecification,
              billData: billData,
            })
          }
        }
        setValidateError({ detail: [] })
        if (doc.id) {
          // 之前有选中的费用类型
          bus.getValue().then(async (value = {}) => {
            let v = await initDefaultValue(value, template, oldTemplate)
            if (v.amortizes) {
              delete v.amortizes
            }
            // 查询单据模板上所有固定值并重新赋值
            template.forEach(tmp => {
              const tmpValue = constantValue(tmp, true)
              if (tmpValue) {
                v[tmp.field] = tmpValue
              }
            })

            if (civilServiceCard && ordersData?.some(el => el.platform === 'transact')) {
              v = await this.handleCSCFieldsValueAssignment(value, v, oldTemplate, template)
            }

            // 时间口径的获取不应放在发票组件中，挪切换费类触发
            if(timeField && dataSource){
              const timeFieldValue = value[timeField]
              if(dataSource.feeTypeForm[timeField] !== timeFieldValue){
                this.handleChangeTimeField({ date: timeFieldValue });
              }
            }
            this.changeFeeTypeDynamicForm(v, template, specification, feeType, hiddenFields)
            !isUseOldVersion && this.updateAutoCalFields(feeType)
          })
        } else {
          // 之前无选中的费用类型
          const v = dataSource ? dataSource.feeTypeForm : {}
          this.changeFeeTypeDynamicForm(v, template, specification, feeType, hiddenFields)
          !isUseOldVersion && this.updateAutoCalFields(feeType)
        }
        this.initDataLinkPermissionFields(feeType)

        api?.logger?.info?.('web端切换费用类型', {
          feeTypeName: doc?.name,
          feeTypeId: doc?.id,
          oldFeeType: oldTemplate,
          feeType: template,
          formOld: value,
          formNew: v
        })
      })
      .catch(err => {
        console.error('切换费用类型失败', err)
        bus.emit('savebtn:state:change', { disabled: false })
      })
  }

  handleCSCFieldsValueAssignment = (value, newInitValue, oldTemplate, template) => {
    const officialCardMoney = template?.filter(el => el?.defaultValue?.type === 'officialCardMoney')
    const officialCardSettlement = template?.find(el => el?.defaultValue?.type === 'officialCardSettlement')?.field

    if (officialCardMoney?.length) {
      const oldOfficialCardMoney = oldTemplate?.find(el => el?.defaultValue?.type === 'officialCardMoney')?.field
      if (oldOfficialCardMoney && value[oldOfficialCardMoney]) {
        officialCardMoney.forEach(v => {
          newInitValue[v.field] = value[oldOfficialCardMoney]
        })
      }
    } else {
      // 公务卡切换费用类型,如果切换的费用类型没有公务卡金额字段，清除费用金额和订单信息卡片数据
      this.setState({
        orders: [],
        ordersData: [],
        max: NaN,
        min: NaN
      })
    }
    if (officialCardSettlement) {
      const oldOfficialCardSettlement = oldTemplate?.find(el => el?.defaultValue?.type === 'officialCardSettlement')
        ?.field
      if (oldOfficialCardSettlement && value[oldOfficialCardSettlement]) {
        newInitValue[officialCardSettlement] = value[oldOfficialCardSettlement]
      }
    }
    return newInitValue
  }

  changeFeeTypeDynamicForm = async (v, template, specification, feeType, hiddenFields) => {
    let {
      value: { ordersData },
      didiAuth,
      externalData,
      externalToFather
    } = this.state
    const { bus, civilServiceCard } = this.props
    bus.emit('savebtn:state:change', { disabled: true })
    if (!ordersData && this.props.dataSource) {
      let {
        dataSource: { feeTypeForm }
      } = this.props
      if (feeTypeForm && feeTypeForm.ordersData) {
        ordersData = feeTypeForm.ordersData
      }
    }
    if (
      didiAuth &&
      didiAuth.setting &&
      didiAuth.setting.authScope == '1|2|3' &&
      ordersData &&
      ordersData[0] &&
      ordersData[0].form.useCarType != 'TAXI'
    ) {
      let field = template.find(item => item.field == 'invoiceForm')
      if (field) {
        if (field.editable === false && !field.canResetUnify) {
          field.canResetUnify = true
        } else {
          delete field.canResetUnify
        }
      }
    }

    // 查询有赋值规则的业务对象字段
    const dataLinkAssignmentFields = template.filter(el => el.type === 'dataLink' && el.isLinkageAssignment)
    if (dataLinkAssignmentFields.length) {
      for (const field of dataLinkAssignmentFields) {
        const dataLinkValue = v[field.name]
        if (field.isLinkageAssignment && dataLinkValue?.data) {
          const linkAssignmentValue = await linkAssignmentFieldsToValue(field, dataLinkValue.data, template, v, {
            updateDisableField: true, // 更新自动计算为无的字段
            updateValueForNullField: true // 更新值为空字段
          })
          if (linkAssignmentValue) {
            v = { ...v, ...linkAssignmentValue }
          }
        }
      }
    }

    this.setState({ template: [] }, () => {
      const extraRiskWarningList = getExtraRiskWarningList(template, externalData)
      const ValidExternal = getValidRiskWarningData(template, externalData) || {}
      const riskNumInTemplate = getRiskInTemplate(template, ValidExternal)
      const riskFieldNum = getRiskFieldNum(ValidExternal)
      const externalCopy = cloneDeep(externalData) || {}
      Object.keys(ValidExternal).forEach(v => {
        delete externalCopy[v]
      })
      Object.values(externalCopy).forEach(v => externalToFather.push(v[0]))
      this.setState(
        {
          hiddenFields,
          template,
          specification,
          doc: feeType,
          value: v,
          externalData: ValidExternal,
          extraRiskWarningList,
          riskFieldNum,
          riskNumInTemplate,
          externalToFather
        },
        () => {
          setTimeout(() => {
            bus.emit('on:dependence:change', { key: 'flow.FeeType', id: feeType.id, dependenceFeeType: true })
            this.initAutoAssignOfOneResultOfDataLink()
          }, 200)
          this.sefBus.setFieldsExternalsData?.({ ...ValidExternal })
          bus.emit('savebtn:state:change', { disabled: false })
        }
      )
    })
  }

  // 审批中修改自动定位错误字段
  initValidateByModify = debounce(() => {
    const { bus, isModify } = this.props
    const { specification } = this.state
    if (isModify && bus) {
      bus.getValueWithValidate?.(0).catch(e => {
        const components = specification?.components || []
        const errors = getValidateErrorByShow(components, Object.keys(e))
        if (!errors.length) {
          setValidateError({ detail: Object.keys(e) })
        }
        return Promise.reject(e)
      })
    }
  }, 1000)

  // 获取业务对象依赖关系
  initDataLinkPermissionFields = async (feeType = {}) => {
    const { billSpecification = {} } = this.props
    const { type } = billSpecification
    const newFeeType = cloneDeep(feeType)
    const newSpecificationId = newFeeType[this.getSpecTypeId(type)]

    const items = await getDataLinkPermissionFieldComponent(newSpecificationId)
    this.setState({
      dependenceFieldOfDataLink: items
    })
  }

  // 业务对象字段唯一值时，自动赋值逻辑
  initAutoAssignOfOneResultOfDataLink = debounce((fields = [], options = {}) => {
    const { bus, billState, submitterId } = this.props
    const { template = [] } = this.state

    // 唯一值处理逻辑只在新建单据时做改为在编辑态做
    if (billState && !['new', 'draft', 'rejected', 'modify'].includes(billState)) {
      options?.onEnd?.()
      return
    }

    let curTime = new Date().getTime()
    this.timer = curTime
    const dataLinkFields = filterDataLinkFields(template, fields)
    if (!dataLinkFields.length) {
      options?.onEnd?.()
      return
    }

    showLoading()
    bus?.getValue?.().then(async res => {
      const currentValue = {
        form: { ...res, feeTypeId: this.state.doc?.feeType?.id || '' },
        submitterId: submitterId?.id
      }
      try {
        const limit = 10
        const result = await handleAutoAssignOfOneResultOfDataLink({ dataLinkFields, currentValue, limit })
        const isEditBill = ['draft', 'rejected', 'modify'].includes(billState)
        const checkValue = []
        if (this.timer === curTime) {
          const rangeOnlyOneAutomaticAssignment = useNewAutomaticAssignment()
            ? 'rangeOnlyOneAutomaticAssignment'
            : 'autoFillByFilterDataOnlyOne'

          dataLinkFields.forEach((line, idx) => {
            const temp = result[idx]
            const { id } = res[line.field] || {}
            const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中
            if (temp && temp.length === 1 && line[rangeOnlyOneAutomaticAssignment]) {
              bus.emit('on:autoAssignDataLink', { id: temp[0]?.dataLink?.id, field: line })
            }
            if (id && !isPreviousIdPresent && temp.length >= limit) {
              // 因返回数据存在翻页，这里记录id，后面统一找
              checkValue.push({ field: line.field, id: id })
              line.filterBy = `(id.in("${id}")) && (active==true)`
            } else if (
              (temp && temp.length !== 1 && !isPreviousIdPresent) ||
              (!line[rangeOnlyOneAutomaticAssignment] && !isPreviousIdPresent)
            ) {
              // 非编辑单据返回多条直接清空，编辑单据只有在筛选依赖条件变更后更新对应赋值结果（多条清空）
              if (!isEditBill || (isEditBill && this.oneResultDataLinkLock)) {
                bus.emit('clear:autoAssignDataLink', { field: line })
              }
            }
          })

          // 确定数据删除
          if (checkValue.length) {
            let dataLinkFieldSameFields = dataLinkFields
            if (getBoolVariation('cyxq-74667')) {
              dataLinkFieldSameFields = dataLinkFields.filter(line => checkValue.some(v => v.field === line.field))
            }
            const res2 = await handleAutoAssignOfOneResultOfDataLink({
              dataLinkFields: dataLinkFieldSameFields,
              currentValue,
              filterBySearch: true
            })

            dataLinkFieldSameFields.forEach((line, idx) => {
              const temp = res2[idx]
              const { id } = res[line.field] || {}
              const isPreviousIdPresent = (temp || []).map(v => v?.dataLink?.id).includes(id) // 上一次存在的id是否在这次结果中

              if (!isPreviousIdPresent) {
                if (!isEditBill || (isEditBill && this.oneResultDataLinkLock)) {
                  bus.emit('clear:autoAssignDataLink', { field: line })
                }
              }
            })
          }
        }
        options?.onEnd?.()
      } catch (e) {
        options?.onEnd?.()
      }
      hideLoading()
    })
  }, 300)

  lockAutoAssignOfOneResultOfDataLink = fields => {
    this.oneResultDataLinkLock = true
    this.initAutoAssignOfOneResultOfDataLink(fields, {
      onEnd: () => {
        this.oneResultDataLinkLock = false
      }
    })
  }

  fnFixMinAndMax(details, dataSource) {
    const ordersData = get(dataSource, 'feeTypeForm.ordersData', [])
    if (!details || !dataSource || !ordersData || !ordersData.length) {
      return
    }
    if (ordersData.length > 1) {
      // 多条消费记录合并生成一条单据明细
      const itemOrder = dataSource.feeTypeForm.ordersData || []
      const itemAmount = dataSource.feeTypeForm.amount || {}
      const transactRecordIds = []
      const platforms = []
      itemOrder.forEach(item => {
        if (item.transactRecordId) {
          transactRecordIds.push(item.transactRecordId)
        }
        if (item.platform === 'transact') {
          platforms.push(item.platform)
        }
      })
      if (transactRecordIds.length !== itemOrder.length || platforms.length !== itemOrder.length) {
        return
      }
      Fetch.GET(`/api/mall/v1/officialCard/[${transactRecordIds.join(',')}]`).then(res => {
        const { items = [] } = res
        let maxVal = new MoneyMath(0)
        items.forEach(item => {
          maxVal = maxVal.add(new MoneyMath(item.useBalance))
        })
        // 后端记录剩余的都是能用的
        details
          .filter(item => {
            const itemOrders = get(item, 'feeTypeForm.orders')
            const orders = get(dataSource, 'feeTypeForm.orders')
            return (
              !!item.feeTypeForm.ordersData &&
              item.feeTypeForm.ordersData.every(el => el.platform === 'transact') &&
              itemOrders === orders
            )
          })
          .forEach(v => {
            // val 记录变化总量，在循环外统一操作
            let val = new MoneyMath(0)
            const { amount } = v.feeTypeForm
            v.feeTypeForm.ordersData.forEach(vl => {
              const { transactRecordId: trid, id } = vl
              if (transactRecordIds.includes(trid)) {
                const useOrder = items.find(el => el.id === vl.transactRecordId)?.useOrder
                const use = useOrder && useOrder.find(o => o.orderId === id)
                if (use) {
                  val = val.plus(use.money)
                }
              }
            })
            maxVal = maxVal.add(val).minus(amount)
          })
        maxVal = maxVal.add(itemAmount).standMoney
        if (Big(maxVal).gte(0)) {
          this.setState({ max: maxVal, min: '0.01' })
        } else {
          this.setState({ max: '-0.01', min: maxVal })
        }
      })
    } else {
      // 单条消费记录生成单据
      const itemOrder = dataSource.feeTypeForm.ordersData[0]
      const itemAmount = dataSource.feeTypeForm.amount
      const { transactRecordId, platform } = itemOrder
      if (platform !== 'transact' || !transactRecordId) {
        return
      }
      Fetch.GET(`/api/mall/v1/officialCard/detail/$${transactRecordId}`).then(res => {
        const { useBalance, useOrder = [] } = res
        // 后端记录剩余的都是能用的
        let maxVal = new MoneyMath(useBalance)
        details
          .filter(item => {
            return !!item.feeTypeForm.ordersData && item.feeTypeForm.ordersData[0].platform === 'transact'
          })
          .forEach(v => {
            const { transactRecordId: trid, id } = v.feeTypeForm.ordersData[0]
            if (transactRecordId === trid) {
              const use = useOrder && useOrder.find(o => o.orderId === id)
              const { amount } = v.feeTypeForm
              if (use) {
                maxVal = maxVal.add(use.money).minus(amount)
              } else {
                maxVal = maxVal.minus(amount)
              }
            }
          })
        maxVal = maxVal.add(itemAmount).standMoney
        if (Big(maxVal).gte(0)) {
          this.setState({ max: maxVal, min: '0.01' })
        } else {
          this.setState({ max: '-0.01', min: maxVal })
        }
      })
    }
  }

  fnSelectStaff = async data => {
    const isByRule = await this.fnGetBillDate(data.staffRangeRule)
    const types = []
    const allowExternalStaff = data?.field?.allowExternalStaff
    if (data?.field?.allowInteriorStaff) {
      //允许选择内部员工
      types.push('department-member')
    }
    if (allowExternalStaff) {
      // 允许选择外部员工
      types.push('external')
    }
    return billSelectStaff({ ...data, isByRule, allowExternalStaff }, data.multiple, types)
  }

  fnSelectStaffs = async data => {
    const isByRule = await this.fnGetBillDate(data.staffRangeRule)
    const types = []
    const allowExternalStaff = data?.allowExternalStaff
    if (data?.allowInteriorStaff) {
      //允许选择内部员工
      types.push('department-member')
    }
    if (allowExternalStaff) {
      // 允许选择外部员工
      types.push('external')
    }
    return billSelectStaff({ ...data, isByRule, allowExternalStaff }, true, types)
  }

  async fnGetBillDate(ruleId) {
    const result = ruleId && ruleId !== 'false'
    if (!result) return result

    const { bus, billData, flowId, baseDataProperties, billSpecification } = this.props
    const { specification } = this.state
    let formValue = {}
    if (bus && bus.getValue) {
      formValue = await bus.getValue() //  当前表单值
    }
    const fee = {
      feeTypeId: this.state.doc.id,
      specificationId: specification.id,
      feeTypeForm: formValue
    }
    billData.details = [fee]
    if (flowId) {
      billData.flowId = flowId
    }
    const params = getParam('detail_', billData, billData, billSpecification, baseDataProperties, undefined, true)
    return api.dataLoader('@common.staffRangeByRule').reload({ ...params.billData, ruleId })
  }

  // 判断变更字段是否属于业务对象依赖字段,如果是执行业务对象唯一值自动赋值
  handleUpdateDataLink = changeValues => {
    const { dependenceFieldOfDataLink } = this.state
    const currentDependenceField = getDependenceFieldOfDataLink(dependenceFieldOfDataLink, changeValues)
    if (!currentDependenceField) return

    const fields = currentDependenceField?.fields || []
    this.lockAutoAssignOfOneResultOfDataLink(fields)
  }

  handleDynamicValueChange = changeValues => {
    const { bus, timeField } = this.props
    let changeKeys = Object.keys(changeValues)
    if (timeField && changeKeys.includes(timeField)) {
      this.handleChangeTimeField({ date: changeValues[timeField] })
    }
    this.updateCalculateField(changeValues)
    bus?.has('bill:value:changed:forAIAttachment') && bus?.emit('bill:value:changed:forAIAttachment', changeValues)
    setTimeout(() => {
      this.handleAmountChangeEvent(changeValues)
      this.updateCustomizeCalculateField(changeValues)
      this.handleUpdateDataLink(changeValues)
    }, 100)
  }

  handleAmountChangeEvent = async changeValues => {
    if (!this.allowSelectionReceivingCurrency) return
    const { bus, dimentionCurrencyInfo } = this.props
    const value = await bus.getFieldsValue()
    const receivingString = 'receivingAmount'
    const amount = 'amount'
    // 检查 changeValues 是否只包含 'amount' 或 'receivingAmount' 中的一个，但不能同时包含
    const hasAmount = changeValues.hasOwnProperty(amount)
    const hasReceivingAmount = changeValues.hasOwnProperty(receivingString)
    const setMoneyField = value[hasAmount ? receivingString : amount] // 需要设置的值
    const emptyA = isEmpty(setMoneyField?.standard)
    const emptyB = isEmpty(changeValues[hasAmount ? amount : receivingString])
    const emptyValueEqual = () => {
      // 兼容所有空值二次更新
      if (emptyA || emptyB) {
        return emptyA == emptyB
      } else {
        return false
      }
    }

    const equal = !isEqual(changeValues, setMoneyField) && !emptyValueEqual()
    if (
      (hasAmount || hasReceivingAmount) &&
      !(hasAmount && hasReceivingAmount) &&
      equal &&
      !changeValues?.isSetFielAmountTrigger // handleAmountAndReceivingAmountChange 这个事件触发的不再更新
    ) {
      // 处理仅存在一个属性且它们的值不相等的情况
      const existingProperty = hasAmount ? amount : receivingString
      let existingValue = changeValues[existingProperty]
      const standardCurrency = api.getState()['@common']?.standardCurrency
      const currency = dimentionCurrencyInfo?.currency ?? standardCurrency // 当前单据的本位币

      if (existingProperty === amount && !existingValue) {
        existingValue = standardValueMoney('', currency) // 给默认值
      }
      this.handleAmountAndReceivingAmountChange({ [existingProperty]: existingValue })
    }
  }

  handleAmountAndReceivingAmountChange = async changeValues => {
    const { bus, billData, allCurrencyRates, dimentionCurrencyInfo } = this.props
    const receivingString = 'receivingAmount'
    const amount = 'amount'
    const receivingCurrency = getBillReceivingCurrency(billData)
    const value = (await bus.getFieldsValue()) || {}
    const oneValue = Object.keys(changeValues)?.length === 1
    const key = Object.keys(changeValues)[0]
    const newValue = changeValues[key]
    const standardCurrency = api.getState()['@common']?.standardCurrency
    const currency = dimentionCurrencyInfo?.currency ?? standardCurrency // 当前单据的本位币
    const setMoneyField = value[key === amount ? receivingString : amount] // 需要设置的值

    // 相同币种不更新
    if (oneValue && allCurrencyRates) {
      if (key === amount) {
        let moneyValue = null
        if (newValue.standardNumCode === receivingCurrency) {
          moneyValue = newValue
        } else {
          const standard = allCurrencyRates.find(v => v.numCode === receivingCurrency) || currency
          moneyValue = updateReceivingAmountMoney({
            prefix: amount,
            nextValue: newValue,
            preValue: setMoneyField,
            currency: standard
          })
        }
        bus.setFieldsValue({ receivingAmount: moneyValue, isSetFielAmountTrigger: true })
      } else if (key === receivingString) {
        // currency 本位币概念
        const amountValue = updateReceivingAmountMoney({
          prefix: receivingString,
          nextValue: newValue,
          preValue: setMoneyField,
          currency,
          receivingCurrency
        })
        bus.setFieldsValue({ amount: amountValue, isSetFielAmountTrigger: true })
      }
    }
  }

  handleDynamicValueBlur = changeValues => {
    this.handleUpdateDataLink(changeValues)
  }

  updateCalculateField = (changeValues, isTextChange) => {
    let { autoCalFields } = this.state
    Object.keys(changeValues).forEach(key => {
      let needUpdateResultFlag = false
      // 由于发票自动计算，发票变更强制触发自动计算
      const isInvoice = key === 'invoiceForm'

      if (key === 'apportions') {
        needUpdateResultFlag = changeValues[key]?.length
          ? !!changeValues[key]?.find(item =>
            Object.keys(item['apportionForm'])?.find(k => needUpdateResult(k, autoCalFields))
          )
          : true
      }
      if (isInvoice) {
        needUpdateResultFlag = true
      }
      //第一次渲染没有结束就不触发自动计算
      if (needUpdateResult(key, autoCalFields) || needUpdateResultFlag) {
        if (getBoolVariation('cyxq-75092')) {
          callFnByQueueNew({ changeValues, isTextChange }, args =>
            this.fnGetFeetypeStandard(args.changeValues, args.isTextChange)
          )
        } else {
          callFnByQueue({ changeValues, isTextChange }, args =>
            this.fnGetFeetypeStandard(args.changeValues, args.isTextChange)
          )
        }
      }
    })
  }

  updateCustomizeCalculateField = changeValues => {
    let { customizeQueryRule } = this.state
    Object.keys(changeValues).forEach(key => {
      //第一次渲染没有结束就不触发自动计算
      if (needUpdateCustomizeResult(key, customizeQueryRule)) {
        this.fnGetCustomizeFeetypeStandard(changeValues)
      }
    })
  }
  formatValue = (value, baseDataProperties) => {
    let values = cloneDeep(value)
    Object.keys(values).forEach(item => {
      let baseItem = baseDataProperties.find(oo => oo.name === item)
      if (baseItem && baseItem.dataType && baseItem.dataType.type === 'ref') {
        values[item] = values[item] && values[item].id
      }
    })
    return values
  }

  handleSelectDataLink = entityInfo => {
    let { index } = this.props
    if (api.has('get:bills:value')) {
      return api.invoke('get:bills:value').then(result => {
        let dataLinkMap = handleDataLinkUseCount(result, index)
        return this.fnSelectDataLink(entityInfo, dataLinkMap)
      })
    } else {
      return this.fnSelectDataLink(entityInfo, {})
    }
  }

  fnSelectDataLink = (entityInfo, dataLinkMap) => {
    let { bus, flowId, baseDataProperties, submitterId } = this.props
    let { template, doc, specification } = this.state
    return bus.getValue().then(value => {
      let formValue = this.formatValue(value, baseDataProperties)
      let saveValue = {
        form: { ...formValue, feeTypeId: doc && doc.id, specificationId: specification?.id }
      }

      return api
        .open('@bills:SelectDataLinkModal', {
          submitterId,
          entityInfo,
          type: 'detail',
          template,
          dataLinkMap,
          bus,
          flowId,
          fromDetail: true,
          isAdd: this.isAdd,
          isDetail: true,
          formValue: saveValue
        })
        .then(resp => {
          this.isAdd = false
          return resp
        })
    })
  }

  fnDetailsInputImportClick = ({ visibilityFeeTypes, billDetails, invoices }) => {
    let { autoCalFields = {}, specification, details, doc, template } = this.state
    details = [...billDetails, ...details]
    let editDetailObject = { autoCalFields, specification, feeType: doc }
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return inputInvoiceImport.apply(this, [
      { visibilityFeeTypes, details, source: 'addDetails', editDetailObject, invoices, importMode }
    ])
  }

  fnDetailsImportClick = ({ visibilityFeeTypes, invoices }) => {
    return this.fnOpenModal(null, visibilityFeeTypes, invoices)
  }

  fnDetailsImportOCRClick = ({ visibilityFeeTypes, states, invoices, isMedical, isOverseas }) => {
    const { bus, submitterId, notShowModalIfAllInvoiceSuccess = false } = this.props
    let { autoCalFields = {}, specification, doc, currentSpecification, template } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    return bus.getValue().then(feeTypeForm => {
      let editDetailObject = { autoCalFields, specification, feeType: doc, feeTypeForm }
      return api.open('@bills:ImportUploadOCRModal', { isMedical, isOverseas }).then(attachmentList => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          visibilityFeeTypes,
          invoiceList: attachmentList,
          attachmentList,
          editDetailObject,
          submitterId,
          isMedical,
          // isOverseas,
          isOcr: true, // 智能识别入口
          source: 'addDetails', //editDetail
          importType: isOverseas ? 'overseasInvoice' : 'ocr',
          currentSpecification: currentSpecification,
          billData: this.props.billData,
          importMode,
          invoices,
          bus,
          notShowModalIfAllInvoiceSuccess: !isMedical && notShowModalIfAllInvoiceSuccess
        })
      })
    })
  }

  fnDetailsAliPayImportClick = ({ feeTypes, invoiceList }) => {
    const { bus, submitterId, notShowModalIfAllInvoiceSuccess } = this.props
    const {
      currentSpecification,
      specification,
      template,
      doc: { feeType },
      doc
    } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    console.log('[ 支付宝导入 ] >', notShowModalIfAllInvoiceSuccess)
    return bus.getValue().then(value => {
      const invoices = getNodeValueByPath(value, 'invoiceForm.invoices', [])
      const isWholeInvoiceDetail = invoices.length
      return api.open('@bills:AliPayInvoiceListModal', { submitterId }).then(data => {
        return api.open('@bills:ImportInvoiceDetailModal', {
          bus,
          invoiceList: data || [],
          visibilityFeeTypes: feeTypes,
          source: 'addDetails',
          billSpecification: currentSpecification,
          billData: this.props.billData,
          editDetailObject: { specification, feeType: feeType || doc, isWholeInvoiceDetail },
          importMode,
          importType: 'alipay',
          invoices: invoiceList,
          notShowModalIfAllInvoiceSuccess
        })
      })
    })
  }

  fnDetailsAifapiaoImportClick = ({ feeTypes, invoiceList, data }) => {
    const { bus, submitterId, notShowModalIfAllInvoiceSuccess } = this.props
    const {
      currentSpecification,
      specification,
      template,
      doc: { feeType },
      doc
    } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    console.log('[ 爱发票导入 ] >', notShowModalIfAllInvoiceSuccess)
    return bus.getValue().then(value => {
      return api.open('@bills:ImportInvoiceDetailModal', {
        bus,
        invoiceList: data || [],
        visibilityFeeTypes: feeTypes,
        source: 'addDetails',
        billSpecification: currentSpecification,
        billData: this.props.billData,
        editDetailObject: { specification, feeType: feeType || doc },
        importMode,
        importType: 'aifapiao',
        invoices: invoiceList,
        notShowModalIfAllInvoiceSuccess
      })
    })
  }

  fnOpenModal = (ocr, types, invoicesList) => {
    let { bus, dataSource = {}, notShowModalIfAllInvoiceSuccess } = this.props
    let { id } = dataSource
    let {
      currentSpecification,
      autoCalFields = {},
      specification,
      template,
      doc: { feeType },
      doc
    } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    console.log('[ pdf 发票文件导入 ] >', notShowModalIfAllInvoiceSuccess)
    return bus.getValue().then(value => {
      const invoices = getNodeValueByPath(value, 'invoiceForm.invoices', [])
      const isWholeInvoiceDetail = invoices.length
      return api.open('@bills:ImportInvoiceModal', { details: [value], flowId: id, multiple: true, bus }).then(data => {
        if (!isEmpty(ocr)) {
          return api.open('@bills:ImportOCRListModal', { bus, types, state: ocr.state })
        }
        return api.open('@bills:ImportInvoiceDetailModal', {
          bus,
          invoiceList: data.invoiceList || [],
          visibilityFeeTypes: types,
          source: 'addDetails',
          billSpecification: currentSpecification,
          billData: this.props.billData,
          editDetailObject: { specification, feeType: feeType || doc, isWholeInvoiceDetail },
          importMode,
          importType: 'pdf',
          invoices: invoicesList,
          notShowModalIfAllInvoiceSuccess,
          attachmentList: data.invoiceList || []
        })
      })
    })
  }

  fnFirstOpenModal = (types, invoicesList) => {
    let { bus, dataSource = {}, notShowModalIfAllInvoiceSuccess } = this.props
    let { id } = dataSource
    let {
      currentSpecification,
      autoCalFields = {},
      specification,
      template,
      doc: { feeType },
      doc
    } = this.state
    const { importMode } = template.find(item => item.field === 'invoiceForm')
    console.log('[ 爱发票导入 ] >', notShowModalIfAllInvoiceSuccess)
    return bus.getValue().then(value => {
      return api.open('@bills:ImportInvoiceDetailModal', {
        bus,
        invoiceList: data.invoiceList || [],
        visibilityFeeTypes: types,
        source: 'addDetails',
        billSpecification: currentSpecification,
        billData: this.props.billData,
        editDetailObject: { specification, feeType: feeType || doc },
        importMode,
        importType: 'aifapiao',
        invoices: invoicesList,
        notShowModalIfAllInvoiceSuccess
      })
    })
  }

  isFirstAutoCalFinished = false
  isFirstCustomizeCalFinished = false
  fnGetFeetypeStandard = async (changeValues = {}, isTextChange, checkDefaultValue) => {
    const {
      bus,
      billData,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      flowId,
      dataSource,
      submitterId,
      dimentionCurrencyInfo,
      civilServiceCard,
      foreignNumCode,
      selectCurrencyDisable
    } = this.props
    const { specification, template } = this.state
    let formValue = {}
    if (bus && bus.getValue) {
      formValue = await bus.getValue() //  当前表单值
    }
    formValue = { ...formValue, ...changeValues }
    //老补助的自动计算后台要求必须传amount
    const amountField = template.find(t => t.name === 'amount') || {}
    const type = get(amountField, 'defaultValue.type')
    //手动填写的自动计算影响老费标
    if (!(amountField.editable === true && type === 'formula') && !formValue.amount) {
      formValue = { ...formValue, amount: standardValueMoney(0, dimentionCurrencyInfo?.currency) }
    }
    const fee = {
      feeTypeId: this.state.doc.id,
      specificationId: specification.id,
      feeTypeForm: formValue
    }
    billData.details = [fee]
    if (flowId) {
      billData.flowId = flowId
    }
    if (billSpecification?.type === 'permit' && billSpecification?.budgetPermitAllowEdit) {
      billData.submitterId = submitterId
    }

    let needUpdateDefaultValue = checkDefaultValue && !dataSource
    // 公务卡导入单据，生成明细的value只有amount字段，无其他计算字段，故单独处理公务卡自动计算字段是needUpdateDefaultValue置为true
    if (civilServiceCard && dataSource) {
      const ordersData = get(dataSource, 'feeTypeForm.ordersData', [])
      if (ordersData.length && ordersData.every(el => el.platform && el.platform === 'transact')) {
        needUpdateDefaultValue = true
      }
    }
    await updateAutoCalResult(
      'detail_',
      billData,
      fee,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      undefined,
      changeValues,
      isTextChange,
      needUpdateDefaultValue,
      template,
      false,
      { foreignNumCode, selectCurrencyDisable }
    )
    if (checkDefaultValue) {
      this.isFirstAutoCalFinished = true
      this.props.canUseDefaultCurrency &&
        !this.props.dataSource &&
        this.props.billState === 'new' &&
        setDefaultCurrencyMoney.apply(this)
    }
  }

  //分摊表格需要联查时
  handleEditApportion = async param => {
    const { index, apportionSpecification } = param
    let currentEditField = { type: 'apportion_', values: [], operate: 'editApportion' }
    const notfirst = index > -1
    if (notfirst) {
      currentEditField.apportionIdx = index
    }
    const changedFields = [currentEditField]
    const {
      bus,
      billData,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      flowId,
      billState
    } = this.props
    const { specification, template } = this.state
    let formValue = {}
    if (bus && bus.getValue) {
      formValue = await bus.getValue() //  当前表单值
    }
    const curItem = { specificationId: apportionSpecification }
    let apportions = get(formValue, 'apportions')
    if (apportions && !apportions.length) {
      apportions.push(curItem)
      formValue.apportions = apportions
    }
    this.apportionSpecification = apportionSpecification
    const fee = {
      feeTypeId: this.state.doc.id,
      specificationId: specification.id,
      feeTypeForm: formValue
    }
    billData.details = [fee]
    if (flowId) {
      billData.flowId = flowId
    }
    const calculateSpecificationID = specification.id
    const customizeQueryRule = await updateCustomizeCalResult(
      'detail_',
      billData,
      fee,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      undefined,
      {},
      changedFields,
      false,
      template,
      calculateSpecificationID,
      billState,
      false
    )
    !notfirst && customizeQueryRule && this.setState({ customizeQueryRule: customizeQueryRule?.queryFields })
    return customizeQueryRule
  }
  fnGetCustomizeFeetypeStandard = async (changeValues = {}, changedFields, checkDefaultValue, onlyQueryFields) => {
    const {
      bus,
      billData,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      flowId,
      dataSource,
      dimentionCurrencyInfo,
      billState
    } = this.props
    const { specification, template } = this.state
    let formValue = {}
    if (bus && bus.getValue) {
      formValue = await bus.getValue() //  当前表单值
    }
    formValue = { ...formValue, ...changeValues }

    //老补助的自动计算后台要求必须传amount
    const amountField = template.find(t => t.name === 'amount') || {}
    const type = get(amountField, 'defaultValue.type')
    //手动填写的自动计算影响老费标
    if (!(amountField.editable === true && type === 'formula') && !formValue.amount) {
      formValue = { ...formValue, amount: standardValueMoney(0, dimentionCurrencyInfo?.currency) }
    }
    //开启分摊开关 但是还未添加时需要传分摊模板id，否则后端无法查询联查规则
    if (this.apportionSpecification) {
      const curItem = { specificationId: this.apportionSpecification }
      let apportions = get(formValue, 'apportions')
      if (apportions && !apportions.length) {
        apportions.push(curItem)
        formValue.apportions = apportions
      }
    }
    const fee = {
      feeTypeId: this.state.doc.id,
      specificationId: specification.id,
      feeTypeForm: formValue
    }
    billData.details = [fee]
    if (flowId) {
      billData.flowId = flowId
    }

    const needUpdateDefaultValue = checkDefaultValue && !dataSource
    const calculateSpecificationID = specification.id
    const customizeQueryRule = await updateCustomizeCalResult(
      'detail_',
      billData,
      fee,
      billSpecification,
      baseDataProperties,
      baseDataPropertiesMap,
      bus,
      undefined,
      changeValues,
      changedFields,
      needUpdateDefaultValue,
      template,
      calculateSpecificationID,
      billState,
      onlyQueryFields
    )
    customizeQueryRule && this.setState({ customizeQueryRule: customizeQueryRule?.queryFields })
    if (checkDefaultValue) {
      this.isFirstCustomizeCalFinished = true
    }
  }
  fnSelectPayee = (selectedPayee, dependenceList, isFeeDetailPayeeId, options) => {
    const { isModify, billSpecification, flowId, multiplePayeesMode } = this.props
    const { specification } = this.state
    const templateid = multiplePayeesMode ? billSpecification.id : specification?.id
    let modalWays = '@bills:SelectPayeeModal'
    if (newVersionOPaymenAccount()) {
      modalWays = '@bills:SelectPayeePopup'
    }
    return api.open(modalWays, {
      templateid,
      isModify,
      flowId,
      data: selectedPayee,
      dependenceList,
      isFeeDetailPayeeId,
      billSpecification,
      filterRules: options?.filterRules
    })
  }

  getSpecTypeId = type => {
    // TODO: 报账单用的是费用的报销字段
    const supplierBills = ['reconciliation', 'settlement', 'corpPayment', 'reimbursement']
    return supplierBills.includes(type)
      ? `expenseSpecificationId`
      : `${type === 'permit' ? 'requisition' : type}SpecificationId`
  }

  getFeeTypeTemplate = (feeType = {}) => {
    const { billState, onlyDetails = [], getFeeTypeTemplateById, billSpecification = {} } = this.props
    const { type, configs = [] } = billSpecification
    const isLockFeeTypeVersion = getV(
      configs.find(config => config.ability === type),
      'lockFeeTypeVersion',
      false
    )
    const newFeeType = cloneDeep(feeType)
    const newSpecificationId = newFeeType[this.getSpecTypeId(type)]
    const targetOldDetail = onlyDetails.find(detail => detail.feeTypeId && detail.feeTypeId.id === feeType.id) || {}
    const oldSpecification = getV(targetOldDetail, 'specificationId', {})
    const feeTypeForm = getV(targetOldDetail, 'feeTypeForm', null)
    const oldId = getV(targetOldDetail, 'specificationId.id')
    //比对保存过的费用类型和新选的是否一个,同一个的话用旧版本
    const isUseOldVersion = feeTypeForm && isLockFeeTypeVersion && oldId !== newSpecificationId && billState !== 'draft'
    if (isUseOldVersion) {
      return Promise.resolve(this.getFeeTypeCostomTemplateAndSpecification(oldSpecification, isUseOldVersion))
    }
    return getFeeTypeTemplateById(newSpecificationId).then(action => {
      const specification = action.payload.value
      return this.getFeeTypeCostomTemplateAndSpecification(specification, isUseOldVersion)
    })
  }

  getFeeTypeCostomTemplateAndSpecification = async (specification, isUseOldVersion) => {
    const {
      dataSource,
      mappingRelation = [],
      multiplePayeesMode = false,
      payPlanMode = false,
      baseDataProperties,
      isRecordExpends,
      isQuickExpends,
      isNeedPayInfo = false,
      billSpecification,
      canEditFieldWhiteList,
      autoExpenseWithBillStriction
    } = this.props
    const { components, type } = billSpecification
    const payee_temp = cloneDeep(PAYEE_INFO)
    // 随手记费用，除金额外全部是强制成选填字段
    if (isRecordExpends) {
      // 过滤掉分摊和数据互联写入和修改
      const blackTypes = ['apportions', 'dataLinkEdits']
      const component = specification?.components.filter(v => blackTypes.indexOf(v.type) < 0)
      isNeedPayInfo && component.push(payee_temp)
      specification.components = component.map(oo => {
        if (!isQuickExpends) {
          if (oo.field === 'invoiceForm' && oo.invoiceType) {
            oo.optional = true
            oo.invoiceType.isRequired = false
          } else if (oo.field !== 'amount') {
            oo.optional = true
          }
        }
        if (canEditFieldWhiteList?.length && !canEditFieldWhiteList.includes(oo.field)) {
          return { ...oo, editable: false }
        }
        return oo
      })
    }
    if (dataSource && dataSource.feeTypeForm) {
      const fieldWhiteList =
        autoExpenseWithBillStriction && type === 'expense' && !isRecordExpends ? ['invoiceForm'] : []
      specification?.components.forEach(v => {
        //对账单时费用模版的结算单数据互联字段需要隐藏
        if (type === 'reconciliation' && v.field === 'supplierSettlement') {
          v.hide = true
        }
        //结算单时费用模版的对账单数据互联字段需要隐藏
        if (type === 'settlement' && v.field === 'supplierReconciliation') {
          v.hide = true
        }
        let mapping = mappingRelation.find(o => o.to === v.field)
        if (dataSource.feeTypeForm.thirdPartyOrders && dataSource.feeTypeForm.thirdPartyOrders.length && mapping) {
          v.isFromThirdParty = true
          // v.editable = mapping.editable
        }
        if (fieldWhiteList?.length && !fieldWhiteList.includes(v.field)) {
          v.editable = false
        }
      })
    }
    //针对多收款人做的一系列操作
    let feeDetailPayeeCP = specification?.components?.find(item => item.field === 'feeDetailPayeeId')
    let feeDetailPayee
    if (!!feeDetailPayeeCP) {
      feeDetailPayee = cloneDeep(feeDetailPayeeCP)
    }
    if (multiplePayeesMode && !payPlanMode && !isRecordExpends) {
      const payeeId = !!feeDetailPayee ? feeDetailPayee : cloneDeep(components?.find(item => item.field === 'payeeId'))
      payeeId.isFeeDetail = true
      payeeId.field = 'feeDetailPayeeId'
      if (!feeDetailPayeeCP) {
        delete payeeId.defaultValue
        specification.components.push(payeeId)
      }
    }

    let template = await this.filterFeeTypeTemplate({ specification, baseDataProperties })
    template = await this.addRelatedComponet(template)

    const feeDetailPayeeField = template?.find(item => item.field === 'feeDetailPayeeId')
    if (!!feeDetailPayeeField) {
      feeDetailPayeeField.isFeeDetail = true
    }
    return { template, specification, isUseOldVersion }
  }

  filterFeeTypeTemplate = async ({ specification, baseDataProperties }) => {
    await fetchAttachmentConfig()
    let template = parseAsMeta(specification, baseDataProperties)
    template = fnParseTemplateFields(template)
    const { isPermitForm, dataFromOrder } = this.props
    if (isPermitForm) {
      const detailsShowFields = dataFromOrder?.detailsShowFields || []
      template = template.filter(v => {
        if (v.field === 'amount') return true
        return (
          detailsShowFields.includes(v.type) ||
          v.type.startsWith('ref:basedata.Dimension') ||
          v.type === 'select_search'
        )
      })
      template = template.map(v => {
        if (v.field === 'amount') {
          v.optional = true
          v.hide = true
        }
        return v
      })
      specification?.components?.forEach(c => {
        if (!template.find(v => v.field === c.field)) {
          c.optional = true
        }
      })
    }
    return template
  }

  addRelatedComponet = template => {
    const { allowAdd } = related.specificationConfig
    if (allowAdd) {
      let obj = template && template.find(line => line.name === 'linkDetailEntities')
      if (!obj) {
        const { value = {} } = this.state
        const detailId = this.fnDetailId(value)
        obj = { name: 'linkDetailEntities', type: 'linkDetailEntities', showLabel: false, _tempConsumId: detailId }
      }
      template.push(obj)
    }
    return template
  }

  fnDetailId = value => {
    const detailId1 = get(value, 'detailId')
    const arrLinkDetails = get(value, 'linkDetailEntities', []) || []
    const dataList = arrLinkDetails.length > 0 ? arrLinkDetails[0].dataList : []
    const detailId2 = dataList && dataList.length > 0 && dataList[0]._tempConsumId
    return detailId1 || detailId2 || uuid()
  }

  updateAutoCalFields(feetype, onlyQueryFields) {
    let {
      billSpecification,
      bus,
      submitterId,
      getCalculateField,
      dimentionCurrencyInfo,
      customizeQueryPower
    } = this.props
    bus.emit('savebtn:state:change', { disabled: true })
    let { type } = billSpecification
    let specificationId = feetype[this.getSpecTypeId(type)] || feetype['expenseSpecificationId']
    const legalEntityId = dimentionCurrencyInfo?.dimention?.id
    if (customizeQueryPower) {
      this.isFirstCustomizeCalFinished = false
      setTimeout(() => {
        this.fnGetCustomizeFeetypeStandard(undefined, undefined, true, onlyQueryFields)
      }, 500)
    }
    const { presetTemplateId, legalEntityMultiCurrency } = this.state
    return getCalculateField(specificationId, submitterId.id, billSpecification.id, {
      legalEntityId,
      presetTemplateId
    }).then(action => {
      if (action.error) return
      let autoRules = action.payload.items
      let autoCalFields = autoRules[0]
      this.isFirstAutoCalFinished = false
      if (getBoolVariation('cyxq-75092')) {
        callFnByQueueNew({}, () => this.fnGetFeetypeStandard(undefined, undefined, true))
      } else {
        callFnByQueue({}, () => this.fnGetFeetypeStandard(undefined, undefined, true))
      }

      this.setState({ autoCalFields })
      bus.emit('savebtn:state:change', { disabled: false })
      return autoCalFields
    })
  }

  checkValueForDIDI = fee => {
    let r = true
    let { didiAuth } = this.state
    if (didiAuth && didiAuth.setting && didiAuth.setting.authScope === '1|2|3') {
      let { dataSource } = this.props
      if (dataSource) {
        let { feeTypeForm } = dataSource
        let { ordersData, thirdPartyOrders } = feeTypeForm
        if (!ordersData && thirdPartyOrders && thirdPartyOrders.length) {
          ordersData = thirdPartyOrders
        }
        if (ordersData && ordersData[0]) {
          ordersData = ordersData[0]
          if (ordersData && ordersData.platform === 'DIDI' && ordersData.form.useCarType !== 'TAXI') {
            if (
              !fee.feeTypeForm.invoiceForm ||
              fee.feeTypeForm.invoiceForm.type !== 'unify' ||
              fee.feeTypeForm.invoiceForm.invoiceCorporation.channel !== 'DIDI'
            ) {
              showMessage.error(i18n.get('为确保订单顺利开票，请选择正确的发票形式或开票方'), 2)
              r = false
            }
          } else if (
            ordersData &&
            ordersData.platform === 'DIDI' &&
            ordersData.form.useCarType === 'TAXI' &&
            fee.feeTypeForm.invoiceForm &&
            fee.feeTypeForm.invoiceForm.type === 'unify' &&
            fee.feeTypeForm.invoiceForm.invoiceCorporation.channel === 'DIDI'
          ) {
            showMessage.error(i18n.get('仅支持滴滴快车、专车、豪华车、代驾订单的统一开票'), 2)
            r = false
          }
        } else if (
          !ordersData &&
          fee.feeTypeForm.invoiceForm &&
          fee.feeTypeForm.invoiceForm.type === 'unify' &&
          fee.feeTypeForm.invoiceForm.invoiceCorporation.channel === 'DIDI'
        ) {
          showMessage.error(i18n.get('仅支持滴滴快车、专车、豪华车、代驾订单的统一开票'), 2)
          r = false
        }
      } else {
        if (
          fee.feeTypeForm.invoiceForm &&
          fee.feeTypeForm.invoiceForm.type === 'unify' &&
          fee.feeTypeForm.invoiceForm.invoiceCorporation.channel === 'DIDI'
        ) {
          //找orders直接添加的明细都没有orders订单相关数据
          if (!fee.feeTypeForm.orders || fee.feeTypeForm.orders.length === 0) {
            showMessage.error(i18n.get('仅支持滴滴快车、专车、豪华车、代驾订单的统一开票'), 2)
            r = false
          }
        }
      }
    }
    return r
  }

  fnSaveFee = (res, isContinue) => {
    let {
      bus,
      dataSource,
      visibleFeeTypes,
      onDetailsChange,
      field,
      isRecordExpends,
      multiplePayeesMode,
      payPlanMode,
      billSpecification,
      billData,
      customizeQueryPower,
      KA_DISABLE_INVOICE
    } = this.props
    let { specification, doc, template, disableInvoiceList, orders, ordersData } = this.state
    const apportionsTemplate = template.find(t => t.field === 'apportions')
    // 由于分摊计算的 open 会在提交单据的时候进行检查，所以要将自动计算的 open 值重新赋值回去
    const specificationResult = cloneDeep(specification)
    const apportionsComponent = specificationResult?.components?.find?.(v => v.field === 'apportions')
    if (apportionsComponent) {
      apportionsComponent.open = apportionsTemplate?.open ?? apportionsComponent.open
    }

    const feeDetailPayeeId = template.find(el => el.name === 'feeDetailPayeeId')
    if (feeDetailPayeeId) {
      const pay = billSpecification.configs.find(v => v.ability === 'pay')
      const payAmount = get(res, 'amount.standard', 0)
      if (Number(payAmount) && !res?.feeDetailPayeeId?.id && pay && pay?.optionalPayeeByZero) {
        showMessage.warning(i18n.get('费用明细金额不为0，收款信息必填'))
        return
      }
    }

    let changedFields = [] //用来记录明细中变化的字段 单据业务对象联查查询
    let details = this.state.details.slice(0)
    let feeTypeFormData = dataSource && dataSource.feeTypeForm
    if (dataSource && !dataSource.add && feeTypeFormData && feeTypeFormData.detailId) {
      res.detailId = feeTypeFormData.detailId
      changedFields = this.checkChangedFields(res, feeTypeFormData)
    } else {
      changedFields = this.checkChangedFields(res, feeTypeFormData)
    }
    //多收款人 且 按明细 且 没有 detailId 时添加 给支付计划用 方便计划同步明细
    if (!res.detailId && multiplePayeesMode && !payPlanMode) {
      res.detailId = uuid(14)
    }
    if (feeTypeFormData?.systemGenerationDetail) {
      res.systemGenerationDetail = feeTypeFormData.systemGenerationDetail
    }
    specificationResult.components.forEach(cmp => {
      const { type, field } = cmp
      if (type === 'switcher' && res[field] === undefined) {
        res[field] = false
      }
    })
    const feeTypeForm = { ...res }
    if (orders && orders?.length && ordersData && ordersData?.length) {
      feeTypeForm.orders = orders
      feeTypeForm.ordersData = ordersData
    }
    let fee = {
      feeTypeId: cloneDeep(this.state.doc),
      feeTypeForm,
      specificationId: specificationResult,
      idx: dataSource && !dataSource.add ? dataSource.idx : details.length
    }
    if (dataSource?.feeTypeForm?.isAutoDetail) {
      fee.feeTypeForm.isAutoDetail = dataSource?.feeTypeForm?.isAutoDetail
    }
    //说明发票形式字段可编辑
    let r = this.checkValueForDIDI(fee)
    if (!r) return
    //不是添加明细产生的业务数据，而是导入明细之后被点击。然后保存
    if (dataSource && !dataSource.add) {
      let { feeTypeForm } = dataSource
      let { thirdPartyOrders } = feeTypeForm

      if (thirdPartyOrders && thirdPartyOrders.length) {
        fee.feeTypeForm = { ...fee.feeTypeForm, orders, ordersData, thirdPartyOrders }
      }
      fee.idx = dataSource.idx
      let idx = details.findIndex(v => v.idx === fee.idx)
      details.splice(idx, 1, fee)
    } else {
      details.push(fee)
    }
    this.setState({ details })
    let detailsData = isRecordExpends ? [fee] : details
    customizeQueryPower &&
      changedFields.length &&
      (detailsData.currentEditField = [{ type: 'detail_', values: changedFields, operate: 'saveFeeType' }])

    //如果有发票禁用操作传到BillInfo待保存
    if (KA_DISABLE_INVOICE && disableInvoiceList && Object.keys(disableInvoiceList).length) {
      const { onDisableInvoice } = this.props
      onDisableInvoice && onDisableInvoice(disableInvoiceList)
    }
    // 保存 修改 草稿 费用明细
    api?.logger?.info?.('费用明细变更埋点', {
      type: `${dataSource?.specificationId ? '修改' : !isContinue ? '保存' : '再记一笔保存'}-${this.saveType}`,
      specificationId: billSpecification?.id,
      specificationName: billSpecification?.name,
      flowId: billData?.flowId,
      code: billData?.code,
      feeTypeId: specificationResult?.id,
      feeTypeName: this?.state?.doc?.name,
      form: fee?.feeTypeForm
    })
    if (!isContinue) {
      this.props.onOk(detailsData)
    } else {
      this.isAdd = true
      const isFeeTypeEmpty = this.fnIsFeeTypeEmpty()
      if (!isFeeTypeEmpty) {
        this.setState({ doc: { id: '' } })
      } else {
        let feeType = getFeeTypeById(visibleFeeTypes, fee.feeTypeId.id)
        this.getFeeTypeTemplate(feeType).then(({ template, specification, isUseOldVersion }) => {
          template = getBillTemplateAddFeeReceivingAmount(template, this.allowSelectionReceivingCurrency)
          this.setState({ template, specification, doc: feeType }, () => {
            setTimeout(
              () => bus.emit('on:dependence:change', { key: 'flow.FeeType', id: feeType.id, dependenceFeeType: true }),
              200
            )
          })
          !isUseOldVersion && this.updateAutoCalFields(feeType)
        })
      }
      bus.emit('continue:add:detail')
      if (customizeQueryPower) {
        onDetailsChange && onDetailsChange(detailsData)
      } else {
        onDetailsChange && onDetailsChange(isRecordExpends ? [fee] : details)
      }
      const selectCurrencyDisable = isSelectCurrencyDisable(field, details)
      const foreignNumCode = getForeignNumCode(details)
      this.setState({ selectCurrencyDisable, foreignNumCode })
      if (isFeeTypeEmpty) {
        bus.resetFields().then(value => {
          this.getDefaultValue4Template(specificationResult).then(defaultValue => {
            this.setState({ value: { ...value, ...defaultValue } })
            bus.emit('element:money:changed', value)
            bus.emit('continue:add:detail:invoiceForm', value)
          })
        })
      }
    }
  }

  checkChangedFields = (newV, oldV) => {
    if (!newV) return
    const newValue = cloneDeep(newV)
    const oldValue = cloneDeep(oldV)
    newValue.feeTypeId = get(this.state, 'doc.id')
    if (oldValue) {
      oldValue.feeTypeId = get(this.props, 'dataSource.feeTypeId.id')
      let changedFields = []
      Object.keys({ ...newValue, ...oldValue }).forEach(field => {
        JSON.stringify(newValue[field]) != JSON.stringify(oldValue[field]) && changedFields.push(field)
      })
      return changedFields
    } else {
      return Object.keys(newValue)
    }
  }

  fnAddTaxInvoiceMaster = item => {
    let taxAmount = this.invoiceTaxInfo.getTaxAmount(item)
    item['taxAmount'] = typeof taxAmount === 'object' ? taxAmount : standardValueMoney(taxAmount)
    return item
  }

  fnAddTaxTicket = item => {
    let { master, taxAmount, taxRate } = item
    const { form } = master
    if (taxAmount == void 0) {
      taxAmount = form[i18n.get('E_税额')] != void 0 ? form[i18n.get('E_税额')] : standardValueMoney(0)
    }
    if (taxRate == void 0) {
      taxRate =
        form[i18n.get('E_税率')] && form[i18n.get('E_税率')] !== i18n.get('免税') ? form[i18n.get('E_税率')] : '0.00'
    }
    item['taxAmount'] = typeof taxAmount === 'object' ? taxAmount : standardValueMoney(taxAmount)
    item['taxRate'] = taxRate
    return item
  }

  fnAddTaxInfo = res => {
    const { invoiceForm } = res
    if (invoiceForm != void 0) {
      const { invoices } = invoiceForm
      if (invoices != void 0 && !!invoices.length) {
        const data = invoices.map(item => {
          const {
            master: { entityId }
          } = item
          return entityId === i18n.get('system_发票主体') ? this.fnAddTaxInvoiceMaster(item) : this.fnAddTaxTicket(item)
        })
        res.invoiceForm.invoices = data
      }
    }
    return res
  }

  checkNotEditablePresetApportion = specification => {
    const { configs = [] } = specification
    const isApi = configs[0]?.isApi
    const presetApportionConfig = (configs?.length > 0 && !isApi && configs[1]?.apportionConfig) || {}
    return isApi || (presetApportionConfig.type === 'preinstall' && presetApportionConfig.editable === false)
  }

  // 1轻松校验模式，0严格模式
  handleSave = async (e, isContinue, level = 0) => {
    this.saveType = level === 1 ? '明细草稿' : '明细保存'
    const {
      bus,
      setValidateError,
      civilServiceCard,
      billState,
      dataSource,
      modifyApproveMoney,
      originalValue,
      dimentionCurrencyInfo
    } = this.props
    const { externalToFather, template, value, specification, ordersData } = this.state

    if (isContinue && civilServiceCard && ordersData.some(el => el.platform === 'transact')) {
      showMessage.warning(i18n.get('公务卡数据不支持再记一笔'))
      return
    }

    const autoFillDataLink = filterAutoAssignDataLinkField(template)
    // 新建单据唯一值开启自动赋值联查接口处理完保存费类
    if (billState === 'new' && autoFillDataLink?.length && this.oneResultDataLinkLock) {
      return
    }

    bus
      .getValueWithValidate?.(level)
      .catch(e => {
        const components = specification?.components || []
        const errors = getValidateErrorByShow(components, Object.keys(e))
        if (!errors.length) {
          setValidateError({ detail: Object.keys(e) })
        }
        return Promise.reject(e)
      })
      .then(formValue => {
        if (!getBoolVariation('ao-84-optimize-datalinkedittable', false)) {
          return Promise.resolve(formValue)
        }
        const components = specification?.components || []
        const dataLinkEdits = components.filter(v => v.type === 'dataLinkEdits' && v.showType === 'TABLE' && ['INSERT', 'UPDATE', 'MORE'].includes(v.behaviour))
        return Promise.all(dataLinkEdits.map(field => {
          if (bus.has(`submit:bill:click:validate:${field?.field}`)) {
            return bus.invoke(`submit:bill:click:validate:${field?.field}`)
          }
          return Promise.resolve()
        })).then(res => {
          if (res.some(v => v === 'failed')) {
            return Promise.reject(new Error('提交失败'))
          }
          return Promise.resolve(formValue)
        })
      })
      .then(async res => {
        if (
          !dataSource?.tripEdit &&
          dataSource?.feeTypeForm?.travelPlanning?.[0]?.travelId &&
          (res?.travelPlanning[0]?.startDate != dataSource?.feeTypeForm?.travelPlanning[0]?.startDate ||
            res?.travelPlanning[0]?.endDate != dataSource?.feeTypeForm?.travelPlanning[0]?.endDate)
        ) {
          try {
            await api.invokeService('@bills:check:purchase:travel', {
              travelId: res?.travelPlanning?.[0].travelId,
              startTime: res?.travelPlanning?.[0].startDate,
              endTime: res?.travelPlanning?.[0].endDate
            })
          } catch (e) {
            Modal.error({
              title: e?.errorMessage
            })
            return
          }
        }

        if (res['u_行程规划']) {
          const error = await bus.invoke('tripdataLink:checkDateValiable')
          if (error === 'ERROR') {
            return
          }
          const datalinkTrip = res['u_行程规划']?.[0]
          if (datalinkTrip?.incomplete) {
            res['u_行程规划'] = []
          } else if (datalinkTrip?.hasOwnProperty('incomplete')) {
            delete datalinkTrip.incomplete
          }
        }
        saveFeeDetailLog()
        if (level && level === 1) {
          res.detailStatus = 'draft'
        }
        const detailNo = get(dataSource, 'feeTypeForm.detailNo', '')
        if (detailNo) {
          res.detailNo = detailNo
        }
        if (civilServiceCard) {
          const status = await this.fnCheckCSCFields(res)
          if (status === 'break') return
        }
        const { flag, lessZero } = this.isShowRelatedTips(res)
        if (flag) {
          this.handleRelatedTips(lessZero)
          return
        }
        if (!validateFeeTypeOnlyCurrency(res)) {
          showMessage.error(i18n.get('一个费用明细中只能存在一种消费币种（原币或本位币），请检查发票信息'))
          return
        }

        // 明细保存时 费用金额<=发票总金额
        if (level !== 1 && !validateInvoicesMoneyLessThanAmount(res)) {
          showMessage.error(i18n.get('费用金额大于发票总金额'))
          return
        }

        //金额字段选填的时候如果没有填值就设置为0，否则后台会报错
        setValidateError({ detail: [] })
        res = this.fnAddTaxInfo(res)
        if (!res.amount || !res.amount.standard) {
          //现在费用明细可以保存草稿，保存草稿的时候必填的也可以不填，所以这个地方如果没有值的话都给一个0的默认值，要不然单据保存草稿的话会报错
          res.amount = standardValueMoney('0.00', dimentionCurrencyInfo?.currency)
        }
        //发票超额指定值，配置了超额的文本字段就必填
        if (res?.invoiceForm?.invoices?.length > 0) {
          //明细模板配置了超额项的文字字段集合
          let textTemplate = []
          template.forEach(t => {
            const type = get(t, 'dataType.elemType.type') || get(t, 'dataType.type')
            const amountChecked = get(t, 'exceedConfigs.amountChecked', false)
            const invoiceAmount = get(t, 'exceedConfigs.invoiceAmount', 0)
            if (type === 'text' && amountChecked) {
              let obj = {
                field: t.field,
                label: t.label,
                invoiceAmount: invoiceAmount
              }
              textTemplate.push(obj)
            }
          })
          if (textTemplate.length > 0) {
            const {
              invoiceForm: { invoices }
            } = res
            let hasExceedComErr = []
            //明细发票金额面值集合
            let amountArr = []
            invoices.forEach(val => {
              const {
                master: { form }
              } = val
              let money = form?.E_system_发票主体_价税合计?.standard
              amountArr.push(money)
            })
            textTemplate.forEach(item => {
              let invoiceAmount = item.invoiceAmount
              let label = item.label
              let hasExceed = false
              amountArr.forEach(num => {
                if (Number(num) > Number(invoiceAmount)) {
                  hasExceed = true
                }
              })
              //某个发票面额值大于相应文本超额值
              if (hasExceed && (res[item.field] === '' || res[item.field] === null || res[item.field] === undefined)) {
                hasExceedComErr.push(`有发票金额大于${invoiceAmount}，${label}不能为空`)
              }
            })
            if (hasExceedComErr.length > 0) {
              hasExceedComErr.forEach(tip => {
                showMessage.error(i18n.get(tip))
              })
              return
            }
          }
        }

        if (
          modifyApproveMoney &&
          res.invoiceForm?.invoices?.length > 0 &&
          !getBoolVariation('close-approveAmount-validate') //  默认值是false
        ) {
          const {
            amount,
            invoiceForm: { invoices }
          } = res
          const hasApproveAmountErr = invoices.filter(item => {
            const { approveAmount } = item
            return approveAmount && Number(approveAmount.standard) > Number(this.getTicketAmount4approve(item))
          })
          if (hasApproveAmountErr.length > 0) {
            showMessage.error(i18n.get('发票的核发金额大于发票金额'))
            return
          }
          if (modifyApproveMoney.isApproveEqualExpense || modifyApproveMoney.isApproveGreatExpense) {
            const invoiceTotal = invoices.reduce((cur, dataSource) => {
              const { approveAmount } = dataSource
              if (approveAmount) {
                return new Big(Number(cur))
                  .plus(new Big(Number(approveAmount.standard)))
                  .toFixed(Number(amount.standardScale))
              } else {
                const ticketAmount = this.getTicketAmount4approve(dataSource)
                return new Big(Number(cur)).plus(new Big(Number(ticketAmount))).toFixed(Number(amount.standardScale))
              }
            }, 0)
            if (modifyApproveMoney.isApproveEqualExpense && modifyApproveMoney.isApproveGreatExpense) {
              if (Number(invoiceTotal) !== Number(amount.standard)) {
                showMessage.error(i18n.get('费用金额需等于核发金额合计'))
                return
              }
            } else if (modifyApproveMoney.isApproveGreatExpense) {
              if (Number(invoiceTotal) < Number(amount.standard)) {
                showMessage.error(i18n.get('费用金额大于核发金额合计'))
                return
              }
            } else if (modifyApproveMoney.isApproveEqualExpense) {
              if (Number(invoiceTotal) > Number(amount.standard)) {
                showMessage.error(i18n.get('费用金额小于核发金额合计'))
                return
              }
            }
          }
        }
        let { apportions, amortizes } = res
        let newValue = res
        let externalFlag = ''
        if (apportions) {
          await api.invoke('get:apportions:values', true)
          newValue = await bus.invoke('update:feeType:amount:save', newValue)
          externalFlag = newValue?.externalFlag
          delete newValue.externalFlag
          newValue.apportions?.forEach(el => {
            if (!el.apportionForm.apportionId) {
              el.apportionForm['apportionId'] = 'ID_' + uuid(11)
            }
          })
        }
        if (amortizes) {
          await api.invoke('get:amortization:values', true)
          newValue = await bus.invoke('validate:amortization:value:feetype:save', newValue)
          newValue.amortizes?.forEach(el => {
            if (!el.amortizeForm.amortizeId) {
              el.amortizeForm['amortizeId'] = 'ID_' + uuid(11)
            }
          })
        }
        this.fnSaveFee(newValue, isContinue)
        if (!externalFlag) {
          api.emit('external:FORM:edit', externalToFather)
        } else {
          api.emit('external:FORM:edit', { externalToFather, externalFlag })
        }
        if (value?.detailId) {
          api.emit('update:editedDetailList', value?.detailId)
        }
      })
      .catch(e => console.log(e))
  }

  handleSaveContinue = e => {
    this.handleSave(e, true)
  }

  handleSaveDraft = e => {
    this.handleSave(e, false, 1)
  }

  getTicketAmount4approve = item => {
    const {
      master: { form, entityId }
    } = item
    const invoiceMoney =
      entityId === 'system_发票主体'
        ? form[i18n.get(`E_{__k0}_价税合计`, { __k0: entityId })]
        : form[i18n.get(`E_{__k0}_金额`, { __k0: entityId })] || form[i18n.get(`E_{__k0}_金额合计`, { __k0: entityId })]
    const isCheckerInvoice = getIsCheckerInvoice(item?.master?.form)
    let ticketAmount = isCheckerInvoice ? this.invoiceTaxInfo.getAllMoney(item.details) : invoiceMoney?.standard
    if (entityId === 'system_非税收入类票据') {
      ticketAmount = invoiceMoney?.standard
    }
    if (isElectronicAirAndTrain(form)) {
      ticketAmount = invoiceMoney?.standard
    }
    return ticketAmount
  }

  getDefaultValue4Template = specification => {
    const { lastChoice, submitterId, baseDataPropertiesMap } = this.props
    const defaultValueMap = {}
    const defaultValueList = specification.components.map(c => {
      const field = baseDataPropertiesMap[c.field]
      return getDefaultValue(c, field, lastChoice, submitterId).then(v => {
        if (v) {
          defaultValueMap[c.field] = v
        }

        return
      })
    })
    return Promise.all(defaultValueList).then(_ => {
      return defaultValueMap
    })
  }

  handleRelatedTips = lessZero => {
    const { notOverIsChecked } = related.specificationConfig
    const message = notOverIsChecked
      ? lessZero
        ? i18n.get('「费用金额」不可小于关联明细的「本次报销金额」')
        : i18n.get('「费用金额」不可大于关联明细的「本次报销金额」')
      : i18n.get('「费用金额」不可小于关联明细的「本次报销金额」')
    Modal.warning({
      title: i18n.get('提示'),
      content: message
    })
  }

  isShowRelatedTips = data => {
    const { linkDetailEntities } = data
    const { dataFromOrder } = this.props
    const amountField = this.state.template.find(el => el.name === 'amount')
    const defType = get(amountField, 'defaultValue.type')
    const isFormulaAmount = defType === 'formula' || defType === 'costStandard'
    // 成本归属单传入订单的企业已付金额时，不校验金额
    if ((isFormulaAmount && !amountField.editable) || dataFromOrder?.companyRealPayFromOrder) {
      return { flag: false }
    }
    if (!linkDetailEntities || (linkDetailEntities && !linkDetailEntities.length)) {
      return { flag: false }
    }
    const { allowAdd, notOverIsChecked } = related.specificationConfig
    const notWrittenOffAmount = this.saveTipsFlag(data)
    const equalZero = new MoneyMath(notWrittenOffAmount).equal(0)
    const lessZero = !new MoneyMath(notWrittenOffAmount).gte(0)
    if (notOverIsChecked) {
      return { flag: allowAdd && !equalZero, lessZero }
    } else {
      return { flag: allowAdd && lessZero, lessZero }
    }
  }

  saveTipsFlag = data => {
    const flag = getBoolVariation('expenseLink_detail_checked_amount', false)
    const { amount, linkDetailEntities } = data
    const dataList = linkDetailEntities.length > 0 ? linkDetailEntities[0].dataList : []
    const _tempConsumId = dataList && dataList.length > 0 && dataList[0]._tempConsumId
    const totalMoney = flag
      ? linkDetailEntities?.reduce((prev, next) => {
        return new MoneyMath(prev).add(next?.useTotalMoney || 0).value
      }, 0)
      : related.getUseMoneyByConsumeId(_tempConsumId)
    return new MoneyMath(amount).minus(totalMoney).value
  }

  handleCancel = () => {
    const { bus, removeLinkDetailEntityId } = this.props
    bus.getFieldsValue().then(res => {
      const { linkDetailEntities } = res
      const detailId = this.fnGetDetailId(linkDetailEntities)
      related.removeByConsumeId(detailId)
      if (!removeLinkDetailEntityId) {
        const value = related.relatedItemMap[detailId]
        const list = this.getList(value)
        const itemList = list.map(v => {
          return { consumeAmount: v.modifyValue, relateId: v.id }
        })
        related.setRelatedMap({ id: detailId, value: itemList })
      }
    })
    let { details } = this.state
    const { isRecordExpends, isRejectCancel } = this.props
    if (isRecordExpends || isRejectCancel) {
      this.props.layer.emitCancel()
    } else {
      this.props.onOk(details)
    }
  }

  fnGetDetailId = linkDetailEntities => {
    const dataList = linkDetailEntities && linkDetailEntities.length > 0 ? linkDetailEntities[0].dataList : []
    const detailId = (dataList && dataList.length > 0 && dataList[0]._tempConsumId) || ''
    return detailId
  }

  getList = value => {
    let list = []
    value &&
      value.forEach(v => {
        list = list.concat(v.dataList)
      })
    return list
  }

  resetDetailsExternalsData = data => {
    if (!data) return
    const { path, controlField, pathValueId } = data
    const externalData = cloneDeep(this.state.externalData)
    const { externalToFather } = this.state
    externalToFather.push(data)
    if (path !== 'APPORTION') {
      externalData[controlField] && delete externalData[controlField]
    } else {
      // const [, idx] = pathValueId.split('$')
      let id = pathValueId.split('$ID_')[0]
      let idx = pathValueId.substring(id.length + 1)
      const deleteItem = get(externalData, `apportions[${idx}][${controlField}]`)
      deleteItem && delete externalData.apportions[idx][controlField]
      if (
        externalData?.apportions &&
        externalData.apportions[idx] &&
        !Object.keys(externalData.apportions[idx]).length
      ) {
        delete externalData.apportions[idx]
        if (!Object.keys(externalData.apportions).length) {
          delete externalData.apportions
        }
      }
    }
    const { bus } = this.props
    const { template } = this.state
    const extraRiskWarningList = getExtraRiskWarningList(template, externalData)
    const riskInTemplateList = getRiskInTemplate(template, externalData)
    const riskFieldNum = getRiskFieldNum(externalData)
    this.setState({ externalData, extraRiskWarningList, riskFieldNum, externalToFather, riskInTemplateList }, () => {
      setTimeout(() => {
        setFieldsExternal({ riskWarning: externalData, bus })
      }, 0)
    })
  }

  createList = (list, external) => {
    const values = Array.isArray(external) ? external : Object.values(external)
    values.forEach(v => {
      if (Array.isArray(v)) {
        !~list.indexOf(v[0]) && list.push(v[0])
      } else {
        this.createList(list, v)
      }
    })
    return list
  }

  emptyApportionsExternalsData = () => {
    const { bus } = this.props
    const { template, externalData, externalToFather } = this.state
    if (!externalData || Object.keys(externalData).length === 0) return
    const temporaryExternalData = cloneDeep(externalData)
    const list = cloneDeep(externalToFather)
    const temporaryExternalToFather = this.createList(list, temporaryExternalData)
    delete temporaryExternalData.apportions
    const extraRiskWarningList = getExtraRiskWarningList(template, temporaryExternalData)
    const riskInTemplateList = getRiskInTemplate(template, temporaryExternalData)
    const riskFieldNum = getRiskFieldNum(temporaryExternalData)
    this.setState(
      {
        externalData: temporaryExternalData,
        extraRiskWarningList,
        riskFieldNum,
        externalToFather: temporaryExternalToFather,
        riskInTemplateList
      },
      () => {
        setTimeout(() => {
          setFieldsExternal({ riskWarning: temporaryExternalData, bus })
        }, 0)
      }
    )
  }

  updateTaxs() {
    let { template, value } = this.state
    const { dataSource } = this.props
    const invoices = getV(dataSource, 'feeTypeForm.invoiceForm.invoices', [])

    if (Array.isArray(template) && invoices.length > 0) {
      const invoicesAmount = getInvoicesAmount(invoices)
      template
        .filter(line => get(line, 'defaultValue.type', '') === 'invoiceSum')
        .forEach(({ defaultValue, field }) => {
          const taxValue = get(defaultValue, 'value')
          const v = getAcountKey(taxValue)
          // 价税合计
          if (taxValue === 'taxTotal') {
            value[field] = new MoneyMath(invoicesAmount.taxAmount).add(invoicesAmount.noTaxAmount).value
          } else {
            value[field] = value[field] || value[v]
          }
        })
    }
  }

  _handlerCurrencyMoneySelectChange = data => {
    const { dataSource, canUseDefaultCurrency, billState, bus } = this.props
    // 测试代码注释
    canUseDefaultCurrency &&
      billState === 'new' &&
      !dataSource &&
      handlerCurrencyMoneySelectChange.call(this, data, 'feeDetail')

    if (this.allowSelectionReceivingCurrency) {
      const { name } = data?.field || {}
      if (name === 'amount') {
        this.handleAmountChangeEvent({ amount: data?.data })
      } else if (name === 'receivingAmount') {
        this.handleAmountChangeEvent({ receivingAmount: data?.data })
      }
    }
  }

  handleOldDiDiCardClick = thirdPartyOrder => {
    if (thirdPartyOrder.platform === 'DIDI') {
      api
        .invokeService('@bills:get:datalink:template:byId', { entityId: thirdPartyOrder?.id, type: 'CARD' })
        .then(resp => {
          const dataLink = get(resp, 'value.data.dataLink')
          const tripType = get(dataLink, 'entity.type', '')
          const data = get(resp, 'value.data')
          api.open('@bills:TripOrderPopup', {
            title: i18n.get('订单详情'),
            entityInfo: { ...data },
            tripType: tripType
          })
        })
    }
  }

  fnGetCheckFields = template => {
    const checkFields = template.filter(item => {
      const dataType = get(item, 'dataType.type')
      const defaultValueType = get(item, 'defaultValue.type')
      const settlementEntity = get(item, 'dataType.entity')
      const officialCardMoney = dataType === 'money' && defaultValueType === 'officialCardMoney'
      const officialCardSettlement =
        dataType === 'ref' &&
        settlementEntity === 'basedata.Settlement' &&
        defaultValueType === 'officialCardSettlement'
      if (officialCardMoney || officialCardSettlement || item.field === 'amount') {
        return item
      }
    })
    return checkFields
  }

  fnCheckCSCFields = async values => {
    const { template, ordersData, settlementId } = this.state
    const transactRecordIds = ordersData.map(v => v.transactRecordId)
    if (!transactRecordIds?.length) return 'continue'
    const res = await Fetch.GET(`/api/mall/v1/officialCard/[${transactRecordIds.join(',')}]`)
    const CSCData = res?.items || []
    let sumUseBalance = 0
    CSCData.forEach(v => {
      sumUseBalance = new Big(Number(sumUseBalance)).plus(new Big(Number(v.useBalance))).toString()
    })
    const CSCorders = ordersData.filter(o => o && o.platform === 'transact')
    const checkFields = filterCSCFields(template)
    const errorMsg = []
    if (checkFields?.length && CSCorders?.length) {
      const officialCardSettlementField = checkFields?.find(
        el => get(el, 'defaultValue.type') === 'officialCardSettlement'
      )
      if (officialCardSettlementField && !values[officialCardSettlementField.field]) {
        if (settlementId) {
          values[officialCardSettlementField.field] = settlementId
        } else {
          showMessage.error('未设置结算方式，请联系管理员')
          return 'break'
        }
      }
      checkFields.forEach(el => {
        const type = get(el, 'defaultValue.type')
        if (type === 'officialCardMoney' || el.field === 'amount') {
          const result = checkCSCMoney(values, el, sumUseBalance)
          if (result) {
            errorMsg.push(`${el.cnLabel}字段金额缺失或错误`)
          }
        }
      })
    }
    if (errorMsg?.length) {
      errorMsg.forEach(v => showMessage.error(v))
      return 'break'
    }
    return 'continue'
  }

  fnDetailsImportCSCClick = async () => {
    const { flowId, visibleFeeTypes, submitterId, valuation, bus } = this.props
    const { doc, specification, template, ordersData, propsOrdersData } = this.state
    let CSCids = null // 该费用明细所引用公务卡数据的id
    let propsCSCids = null // 该费用明细从单据detail获取的公务卡数据ID
    let transactRecordIds = null // 该费用明细所引用公务卡数据的transactRecordId
    CSCids = ordersData.filter(o => o && o.platform === 'transact').map(item => item.id)
    propsCSCids = propsOrdersData.filter(o => o && o.platform === 'transact').map(item => item.id)
    transactRecordIds = ordersData.filter(o => o && o.platform === 'transact').map(item => item.transactRecordId)
    const data = await api.open('@bills:ImportCivilServiceCardModal', {
      flowId,
      visibilityFeeTypes: visibleFeeTypes,
      valuation,
      submitterId,
      openByDetail: true,
      CSCids,
      propsCSCids,
      transactRecordIds,
      feeTypeId: doc,
      specificationId: specification
    })
    const checkFields = filterCSCFields(template)
    const feeTypeForm = get(data, '[0].feeTypeForm')
    const amount = get(data, '[0].feeTypeForm.amount')
    if (amount) {
      let max, min
      if (Big(amount.standard).gte(0)) {
        max = amount.standard
        min = '0.01'
      } else {
        max = '-0.01'
        min = amount.standard
      }
      // 重置金额字段的最大值和最小值范围
      const setDetailValue = async () => {
        const formValue = await bus.getValue()
        const orders = get(feeTypeForm, 'orders')
        const ordersData = get(feeTypeForm, 'ordersData')
        const keys = Object.keys(feeTypeForm)
        const invoices = get(formValue, 'invoiceForm.invoices')
        keys?.forEach(el => {
          if (checkFields?.length && !checkFields?.find(item => item.field === el)) {
            delete feeTypeForm[el]
          }
          if (el === 'amount' && invoices?.length) {
            delete feeTypeForm[el]
            delete feeTypeForm['invoiceForm']
          }
        })
        const newValue = { ...formValue, ...feeTypeForm, orders, ordersData }
        this.setState({ ordersData, orders, value: newValue })
      }
      const moneyField = template.find(t => t.field === 'amount')
      if (max && moneyField) moneyField.max = max
      if (min && moneyField) moneyField.min = min
      this.setState({ max, min, template }, setDetailValue)
    }
  }

  render() {
    let {
      dataSource = {},
      bus,
      visibleFeeTypes,
      temFeeType,
      submitterId,
      lastChoice,
      currentNode,
      isModify,
      flowId,
      keel,
      stackerManager,
      billSpecification,
      isForbid,
      validateError,
      billTemplate,
      external,
      civilServiceCard,
      billState,
      viewKey,
      treeDefaultExpandAll,
      isQuickExpends,
      isPermitForm,
      inECard,
      canEditFieldWhiteList,
      autoExpenseWithBillStriction,
      timeField,
      ...others
    } = this.props
    let {
      hiddenFields,
      doc,
      template,
      value,
      max,
      min,
      autoCalFields,
      customizeQueryRule,
      calculateErrorMsg,
      riskFieldNum,
      riskInTemplateList,
      extraRiskWarningList,
      recommendFeeTypes,
      selectCurrencyDisable,
      foreignNumCode,
      referenceDataType,
      ordersData,
      detalsAllCurrencyRates
    } = this.state
    // 税额相关计算
    // this.updateTaxs()
    let { feeTypeForm = {}, showAllFeeType, apportionVisibleList, cannotEditAmountField } = dataSource
    let { thirdPartyOrders = [], detailId } = feeTypeForm
    let invoiceCard = ordersData.find(o => o && o.platform === 'fp')
    let thirdPartyOrder = thirdPartyOrders[0]
    let thirdOrder = ordersData.filter(o => o && o.platform !== 'fp' && o.platform != 'DIDI')
    if ((max || min) && template && template.length) {
      let moneyTemplate = template.find(t => t.field === 'amount')
      if (moneyTemplate) {
        moneyTemplate.max = max && Big(max).lt(moneyTemplate.max) ? max : moneyTemplate.max
        moneyTemplate.min = min && Big(min).gt(moneyTemplate.min) ? min : moneyTemplate.min
      }
    }
    let disabled = isModify && !isAllowModifyFiled(currentNode, 'feeTypeId')
    const billType = get(billSpecification, 'type')
    if (
      billType === 'reconciliation' ||
      billType === 'settlement' ||
      value?.systemGenerationDetail ||
      (billType === 'expense' && !this.props.isRecordExpends && autoExpenseWithBillStriction) ||
      inECard
    ) {
      disabled = true
    }
    if (template && template.length) {
      const pay = billSpecification.configs.find(v => v.ability === 'pay')
      template.forEach(item => {
        if (item.name === 'feeDetailPayeeId' && pay && pay?.optionalPayeeByZero) {
          item.optionalPayeeByZero = true
          item.optional = true
          item.allowClear = true
        }
      })
    }
    const { groupTemplate, isGroup } = splitTemplateToGroups(template)
    // 费用明细中的导入公务卡数据, 开通公务卡charge，明细可导入公务卡数据，若限制费用类型，则只有限制类型可导入
    let CSCImportEnable = false
    // CSCOpinion = 公务卡charge && 单据不是修改状态 && 屏蔽随手记中记费用弹窗 && 单据类型是报销单
    const CSCOpinion =
      civilServiceCard && billState !== 'modify' && viewKey !== 'FeeDetailView' && billSpecification?.type === 'expense'
    if (CSCOpinion) {
      const referenceByDetailsEnable = get(referenceDataType, 'referenceByDetailsEnable')
      const enable = get(referenceDataType, 'referenceByDetails.enable')
      const feeTypeIds = get(referenceDataType, 'referenceByDetails.feeTypeIds')
      if (referenceByDetailsEnable) {
        CSCImportEnable = true
        if (enable && feeTypeIds?.length) {
          const feeTypeId = get(doc, 'id')
          CSCImportEnable = !!feeTypeId && feeTypeIds.includes(feeTypeId)
        }
      }
    }
    const specificationId = doc[this.getSpecTypeId(billType)] || doc['expenseSpecificationId']
    return (
      <>
        <div
          className={classNames(styles['fee-detail-edit-wrapper'], {
            'highlight-modifiable-field': currentNode?.config?.highlightModifiableField || false
          })}
        >
          <div className={styles['fee-detail-edit']}>
            <div className="group-item">
              {riskFieldNum > 0 && (
                <WarningOrErrorTips
                  riskInTemplateList={riskInTemplateList}
                  extraRiskWarningList={extraRiskWarningList}
                  isForbid={isForbid}
                  isFeeDetail={true}
                />
              )}
              <FeeTypeItem
                useTreeSelectRC
                label={i18n.get('费用类型')}
                className="fee-type-select"
                size="large"
                showFullPath
                disabledCheckedFather
                multiple={false}
                treeCheckable={false}
                feeTypes={visibleFeeTypes}
                checkedKeys={doc?.id}
                recommends={recommendFeeTypes}
                errorMsg={calculateErrorMsg}
                onChange={this.handleFeeTypeChange}
                disabled={disabled}
                checkedValue={doc}
                filterOnSearch={true}
                treeDefaultExpandAll={treeDefaultExpandAll}
                useEUI={true}
              />
            </div>
            {!!doc?.id ? (
              <div className={!isGroup ? 'group-item' : ''}>
                {CSCImportEnable && (
                  <SelectCivilServiceCard
                    label={i18n.get('公务卡')}
                    fnDetailsImportCSCClick={this.fnDetailsImportCSCClick}
                  />
                )}
                <DynamicEditable
                  {...others}
                  hiddenFields={hiddenFields}
                  billTemplate={billTemplate}
                  selectCurrencyDisable={selectCurrencyDisable}
                  foreignNumCode={foreignNumCode}
                  keel={keel}
                  feeDetailId={doc?.id}
                  feeType={doc}
                  stackerManager={stackerManager}
                  autoCalFields={autoCalFields}
                  customizeQueryRule={customizeQueryRule}
                  bus={bus}
                  layout={layout}
                  template={template}
                  groupTemplate={groupTemplate}
                  submitterId={submitterId}
                  value={value}
                  lastChoice={lastChoice}
                  currentNode={currentNode}
                  isModify={isModify}
                  flowId={flowId}
                  dleType={detailId ? null : 'detail'}
                  isDetail={true}
                  detailId={detailId}
                  billSpecification={billSpecification}
                  isForbid={isForbid}
                  validateError={validateError.detail}
                  cannotEditAmountField={cannotEditAmountField}
                  showAllFeeType={showAllFeeType}
                  apportionVisibleList={apportionVisibleList}
                  feeAmount={value ? value['amount'] : undefined}
                  riskWarning={external}
                  classNameGroup="group-item"
                  billState={billState}
                  isQuickExpends={isQuickExpends}
                  isPermitForm={isPermitForm}
                  onComponentLoadFinished={this.handleComponentLoadFinish}
                  shouldSaveFeetype={dataSource?.shouldSaveFeetype}
                  timeField={timeField}
                  detalsAllCurrencyRates={detalsAllCurrencyRates}
                  specificationId={specificationId}
                  useEUI
                  businessType={'DETAILS'}
                />
              </div>
            ) : (
              <FeeTypeEmpty />
            )}
            <div
              className={invoiceCard || (thirdOrder && thirdOrder.length > 0) || thirdPartyOrder ? 'group-item' : ''}
            >
              {invoiceCard && (
                <Row className="card_wrapper">
                  <Col span={24} className="label">
                    {i18n.get('发票信息')}
                    {i18n.get('：')}
                  </Col>
                  <Col span={16}>
                    <InvoiceCard invoice={invoiceCard} />
                  </Col>
                </Row>
              )}
              {thirdOrder && thirdOrder.length > 0 && (
                <Row className="card_wrapper">
                  <Col span={24} className="label">
                    {i18n.get('订单信息')}
                    {i18n.get('：')}
                  </Col>
                  {thirdOrder.map(item => (
                    <Col span={16} className="mb-8">
                      <ThirdCard orders={item} />
                    </Col>
                  ))}
                </Row>
              )}
              {thirdPartyOrder && (
                <Row className="card_wrapper">
                  <Col span={24} className="label">
                    {i18n.get('订单信息')}
                    {i18n.get('：')}
                  </Col>
                  <Col span={16}>
                    <div onClick={() => this.handleOldDiDiCardClick(thirdPartyOrder)}>
                      <ThirdPartyCard
                        dataSource={thirdPartyOrder}
                        iconFont={thirdPartyOrder?.platform === 'DIDI' ? svg : null}
                      />
                    </div>
                  </Col>
                </Row>
              )}
            </div>
          </div>
        </div>
        {!!doc.id && (
          <FeeDetailBottom
            isRecordExpends={this.props.isRecordExpends}
            bus={bus}
            editabel={true}
            addable={!dataSource.specificationId && !isQuickExpends && !isPermitForm}
          />
        )}
      </>
    )
  }
}
