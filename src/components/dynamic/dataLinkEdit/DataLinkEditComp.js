import React, { PureComponent } from 'react'
import { message } from 'antd'
import styles from './DataLinkEdit.module.less'
import { Button } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import DataLinkEditSingle from './DataLinkEditSingle'
import MessageCenter from '@ekuaibao/messagecenter'
import { parseAsMeta } from '@ekuaibao/lib/lib/parse'
import { cloneDeep, get } from 'lodash'
import DataLinkCard from './DataLinkCard'
import DataLinkEditTable from './DataLinkEditTable'
const BEHAVIOUR = 'UPDATE'
import DataLinkEditHeader from './DataLinkEditHeader'
import { getBudgetAdjustColumns, getBudgetAdjustDetails } from '../../utils/BudgetAdjust'
import CarBusinessCard from '../../../elements/dataLink-card/MyCarBusinessCard'
const { billSelectStaff, isHabBinding } = api.require('@bills/util/billUtils')
import { checkPriority } from '../../utils/fnFormartDatalinkData'
import { MoneyMath } from '@ekuaibao/money-math'
import { getBoolVariation } from '../../../lib/featbit'

@EnhanceConnect(state => ({
  dimensionValue: state['@common'].dimensionValue.items,
  baseDataProperties: state['@common'].globalFields.data,
  specificationGroupsList: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
  requisitionInfo: state['@bills'].requisitionInfo,
  fieldMap: state['@common'].fieldMap,
  lastChoice: state['@common'].lastChoice.choiceValue,
  multiplePayeesMode: state['@bills'].multiplePayeesMode
}))
export default class DataLinkEditComp extends PureComponent {
  constructor(props) {
    super(props)
    this.bus = props.fieldBus ?? new MessageCenter()
    let { field, value } = props
    field.fieldBus = this.bus
    field.fieldBus.onChange = props.onChange
    this.state = {
      field: field,
      templates: [],
      templatesold: [],
      items: value && value.length > 0 ? value : [],
      dataLinkTemplateId: '',
      dataLinkId: null,
      editingKey: ''
    }
  }
  componentWillReceiveProps(nextProps) {
    if (nextProps.value !== this.props.value &&
      nextProps.value !== this.state.items) {
      const { field, billSpecification } = nextProps
      const isHabSetValue = isHabBinding(billSpecification, field?.field);
      const isAIToList = true; // TODO: 临时处理
      (isAIToList || isHabSetValue) && this.setState({
        items: nextProps.value && nextProps.value.length > 0 ? nextProps.value : [],
      })
    }
  }
  componentDidMount() {
    this.getTemplate()
    const { bus } = this.props
    bus?.on('dimention:currency:change', this.handleRefChange)
    this.bus.watch('element:ref:select:staff', this.fnSelectStaff)
    this.bus.watch('element:ref:select:staffs', this.fnSelectStaffs)
    this.bus.watch('element:ref:select:payee', this.fnSelectPayee)
    this.bus.watch('element:select:dataLink', this.handleSelectDataLink)
    this.bus.watch('element:ref:dataLink:staffs', this.fnDataLinkStaffs)
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus?.un('dimention:currency:change', this.handleRefChange)
    this.bus.un('element:ref:select:staff', this.fnSelectStaff)
    this.bus.un('element:ref:select:staffs', this.fnSelectStaffs)
    this.bus.un('element:ref:select:payee', this.fnSelectPayee)
    this.bus.un('element:select:dataLink', this.handleSelectDataLink)
    this.bus.un('element:ref:dataLink:staffs', this.fnDataLinkStaffs)
  }

  handleRefChange = data => {
    this.bus?.emit('dimention:currency:change', data)
  }

  fnSelectStaff = data => {
    return billSelectStaff(data, data.multiple)
  }

  fnSelectStaffs = data => {
    return billSelectStaff(data, true)
  }
  fnDataLinkStaffs = data => {
    const { checkedList } = data

    return api.open('@layout:SelectStaffsModal', { checkedList: checkedList, multiple: true }).then(result => {
      const { checkedList } = result
      const staff = checkedList?.find(item => item.type === 'department-member').checkedData || []
      let params = { staff }
      const role = checkedList?.find(item => item.type === 'role').checkedData || []
      const department = checkedList?.find(item => item.type === 'department').checkedData || []
      params = { role, department, ...params }

      return params
    })
  }
  fnSelectPayee = selectedPayee => {
    return api.open('@bills:SelectPayeeModal', { data: selectedPayee })
  }
  handleSelectDataLink = entityInfo => {
    let { flowId } = this.props
    let eninfo = cloneDeep(entityInfo)
    if (eninfo.dataLink && !eninfo.dataLink.id) {
      eninfo.dataLink.id = eninfo.referenceData
    }
    return api.open('@bills:SelectDataLinkModal', { entityInfo: eninfo, flowId: flowId || '' })
  }
  handleType = (dataLinkTemplateId, dataLinkId, formobj) => {
    let items = [{ dataLinkForm: { ...formobj }, dataLinkTemplateId, dataLinkId }]
    this.handleChange(items)
  }
  resetTemp = items => {
    const { isRecordExpends, field: { showType, referenceData } } = this.props
    return items.map(entityType => {
      let nt = cloneDeep(entityType)
      let fields = entityType?.selectedFields
      let components = parseAsMeta(entityType, cloneDeep(fields), true)
      // 表格布局使用tableTemplate
      if (showType === 'TABLE') {
        fields = nt.tableTemplate.selectedFields
        components = parseAsMeta(nt?.tableTemplate, cloneDeep(fields), true)
      }
      if (isRecordExpends) {
        components.forEach(item => {
          item.optional = true
        })
      }
      let temp = components.filter(i => {
        // @i18n-ignore
        return i.name != 'visibility' && i.name != 'active' && i.name != 'ownerId'
      })
      nt.components = temp
      nt.selectedFields = nt.selectedFields
      return nt
    })

  }

  loadBudgetAdjustDetails = () => {
    const { value } = this.props
    if (value?.length) {
      getBudgetAdjustDetails({ ids: value }).then(res => {
        if (res?.items?.length) {
          const entityId = res.items[0].entityId
          this.columns = getBudgetAdjustColumns(entityId)
          this.setState({ templates: this.columns, items: res.items })
        }
      })
    }
  }
  getTemplate = async () => {
    const {
      field: { behaviour = 'INSERT', subTypeId, name, showTemplateId: tableTemplateId, showType },
      value,
      billSpecification,
    } = this.props
    if (name === 'budgetAdjustDetails') {
      this.loadBudgetAdjustDetails()
      return
    }

    const params = {
      id: this.state.field.referenceData.id,
      type: behaviour === 'INSERT' || behaviour === 'MORE',
      tableTemplateId,
      needFormula: getBoolVariation('ao-2-datalink-show-calculate') ? true : false,
    }
    // 只支持表格的菜有这个分摊列
    if(getBoolVariation('aprd-5665-datalink') && showType=== 'TABLE'){
      params.specificationId = billSpecification?.id
      params.field = name
    }
    const res = await api.invokeService('@bills:get:getDataLinkEditTemplate', params)

    const items = subTypeId ? res.items.filter(i => i?.entity?.id === subTypeId) : res.items
    let ntes = this.resetTemp(items)
    let its = ntes.filter(i => i.entity.active === true)
    // 业务对象写入name最大长度 补丁,设计之初的问题 强行修改为name字段最大长度100
    its = its?.map(v => {
      const { components } = v
      v.components = components?.map(subItem => {
        if (subItem?.name?.endsWith('_name')) {
          subItem.maxLength = 300
          return subItem
        }
        if (subItem?.name?.endsWith('_code')) {
          subItem.maxLength = 100
          return subItem
        }
        return subItem
      })
      return v
    })
    if (behaviour === 'REF' && value?.length) {
      try {
        const dataLinkIds = value.map(v => v.dataLinkId)
        const resp = await api.invokeService('@bills:get:datalink:template:byIds', { dataLinkIds, type: showType })
        let data = resp.items || []
        const newValue = value.map(it => {
          const cur = data?.find(v => v?.data?.dataLink?.id === it?.dataLinkId)
          const dataLinkForm = {}
          Object.keys(it.dataLinkForm).forEach(key => {
            dataLinkForm[key] = cur?.data?.dataLink[key]
          })

          return { ...it, dataLinkForm }
        })
        this.setState({ items: newValue, templates: its, templatesold: ntes })
      } catch (error) {
        this.setState({ templates: its, templatesold: ntes })
      }
    } else {
      this.getDataLinkEditItems(its, ntes)
    }
  }
  getDataLinkEditItems = (its, ntes) => {
    const {
      field,
      flowId,
      detailId,
      tripId,
      isDetail = false,
      isTrip = false,
      shouldUpdate = true,
      dleType,
      value,
      field: { behaviour = 'INSERT', name },
      currentNode,
      billState
    } = this.props
    let params = undefined
    if (flowId && dleType != 'detail') {
      if (isDetail && detailId && shouldUpdate && value) {
        //type 类别:form(表头), details(明细),trips(行程的明细)
        params = { flowId, detailId, type: 'details', fieldName: field.name }
      } else if (isTrip && tripId && shouldUpdate && value) {
        params = { flowId, detailId: tripId, type: 'trips', fieldName: field.name }
      } else if (!isDetail && !isTrip) {
        params = { flowId, type: 'form', fieldName: field.name }
      }
    }
    if (params) {
      api.invokeService('@bills:get:getDataLinkEditItems', { ...params }).then(res => {
        let object = {
          templates: its,
          templatesold: ntes
        }
        if (res.items && Array.isArray(res.items) && res.items.length > 0) {
          if (
            (behaviour === 'INSERT' && res.items.filter(i => i.dataLinkId != null && i.dataLinkId != 'null').length > 0) ||
            (behaviour === BEHAVIOUR && res.items.filter(i => i.dataLinkId == null || i.dataLinkId == 'null').length > 0)
          ) {
            if (!currentNode) {
              object.items = []
            }
          } else {
            res.items.forEach(el => {
              const dataLinkForm = get(el, 'dataLinkForm')
              if (dataLinkForm && dataLinkForm.owner) {
                delete dataLinkForm.owner
              }
              // owner 为 null 时，删除 owner, owner不是全局字段会影响业务对象数据更新
              if (dataLinkForm.owner === null) {
                delete dataLinkForm.owner
              }
            })
            object.items = res.items
          }
        } else {
          object.items = []
        }
        this.setState(object)
      })
    } else {
      this.setState({
        templates: its,
        templatesold: ntes
      })
    }
  }
  handleRenewSelect = () => {
    const { external, field, bus } = this.props
    let { templates } = this.state

    api
      .open('@bills:SelectDataLinkEditModal', {
        entityInfo: {
          value: {},
          field,
          templates,
          external,
          bus: this.bus,
          addCallback: this.addCallback,
          flowBus: bus
        }
      })
      .then(result => {
        var items = [...this.state.items, ...result.data]
        this.handleChange(items)
      })
  }

  addCallback = result => {
    const items = [...this.state.items, JSON.parse(result.data)]
    this.handleChange(items)
  }

  fnUpdateData = (result) => {
    if (!getBoolVariation('ao-17-datalink-multiple-assignment-rule')) return
    const { bus, template, form, field } = this.props
    const { assignmentRule, isLinkageAssignment } = field
    if (!assignmentRule?.fields?.length || !isLinkageAssignment) return
    const { fields = [] } = assignmentRule
    const templateMap = template.reduce((item, next) => {
      item[next.name] = next
      return item
    }, {})
    let valueMap = {}
    fields.forEach(item => {
      const dataLinkFormValue = result.map(res => res.dataLinkForm?.[item?.sourceField] || res?.[item?.sourceField]).filter(v => v)
      const billField = templateMap[item.targetField]
      // 业务对象多选字段联动赋值仅支持金额/数字字段的求和赋值
      if (billField?.type === 'money' && checkPriority(billField)) {
        valueMap[item.targetField] = dataLinkFormValue.reduce((prev, acc) => {
          return new MoneyMath(prev).add(acc).value
        }, 0)
      } else if (billField?.type === 'number' && checkPriority(billField)) {
        valueMap[item.targetField] = dataLinkFormValue.reduce((prev, acc) => {
          return new Big(prev).plus(acc).valueOf()
        }, 0)
      }
    })
    if (Object.keys(valueMap).length) {
      !!form && form.setFieldsValue(valueMap)
    }
    if (valueMap.loanMoney) {
      bus.emit('loanMoney:changed', valueMap.loanMoney)
    }
  }

  handleChange = result => {
    result.forEach(item => {
      delete item.index
    })
    this.setState({
      items: result
    })
    let { onChange } = this.props
    this.props.bus.setValidateLevel(-1)
    if (result && result.length == 1) {
      const val = Object.values(result[0].dataLinkForm).find(i => !!i)
      if (!val) {
        result[0].dataLinkForm = {}
        onChange && onChange(result)
        this.bus.resetFields && this.bus.resetFields()
        return
      }
    }
    this.fnUpdateData(result)
    onChange && onChange(result)
  }

  handleRemove = index => {
    let items = this.state.items.filter((i, num) => {
      return num !== index
    })
    this.handleChange(items)
  }

  handleRowClick = (record) => {
    const { field } = this.props
    if (field?.behaviour === 'REF') {
      this.handleShowDetail(record)
    }
  }

  handleShowDetail = (obj) => {
    const { field } = this.props
    if (field?.referenceData?.platformId?.type === 'PRIVATE_CAR') {
      api.open('@bills:BillStackerModal', {
        viewKey: 'MyCarBusinessInfo',
        referenceData: field?.referenceData,
        data: { form: obj },
        isMulti: true
      })
    } else {
      api.open('@bills:DataLinkDetailModal', {
        entityInfo: { dataLink: { id: obj.dataLinkId }, entityId: field.referenceData },
        field,
        disabledStaff: true,
        showClose: true
      })
    }
  }

  handleDataLinkLineClick = (obj, idx, items) => {
    const { field } = this.props
    if (field.behaviour === 'REF') {
      this.handleShowDetail(obj, idx)
    } else {
      this.handleUpdate(obj, idx, items)
    }
  }

  handleUpdate = (obj, index, dataList) => {
    const {
      external,
      field,
      field: { behaviour },
      bus
    } = this.props
    let { templates, templatesold } = this.state
    this.setState({ editingKey: index })
    api
      .open('@bills:SelectDataLinkEditModal', {
        entityInfo: {
          value: { ...obj },
          status: 'update',
          action: behaviour === BEHAVIOUR ? 'dataLinkUpdate' : '',
          field,
          templates: behaviour === BEHAVIOUR ? templatesold : templates,
          templatesold,
          external,
          bus: this.bus,
          flowBus: bus,
          updateNextSource: {
            dataList: cloneDeep(dataList),
            currentIndex: index,
            updateCallback: this.updateCallback,
          }
        }
      })
      .then((result) => {
        const { currentIndex, ...other } = result
        this.updateCallback(other, currentIndex > -1 ? currentIndex : index)
      })
  }

  updateCallback = (result, index) => {
    const { items } = this.state
    const { data } = result
    const newList = items.map((i, num) => {
      if (num === index) {
        return { ...data }
      } else {
        return i
      }
    })
    this.setState({ editingKey: '' })
    this.handleChange(newList, '')
  }

  getIsShowError = (it, showType) => {
    const dataLinkEntity = api.getState()['@bills'].dataLinkEntity
    const { dataLinkTemplateId, dataLinkForm } = it
    const currDataLinkEntity = dataLinkEntity?.find(it => it.templateId === dataLinkTemplateId)
    // const currEmptyData = []
    if (currDataLinkEntity) {
      let components = currDataLinkEntity?.components
      if (showType === 'TABLE') {
        components = currDataLinkEntity?.tableTemplate?.components
      }
      components = components?.filter(i => i.type !== 'switcher')
      return components.some(oo => {
        const { field, optional } = oo
        const val = dataLinkForm?.[field]
        if (!optional && (!val || val?.length === 0)) {
          return true
        }
      })
    }
    return false
  }

  renderList = () => {
    let { items, templatesold } = this.state
    const { field } = this.props
    const type = field?.referenceData?.platformId?.type
    if (items.length === 0) {
      return ''
    }
    return items.map((i, index) => {
      if (type === 'PRIVATE_CAR') {
        return (
          <div className={styles['dataLink-input-wrapper']}>
            <CarBusinessCard field={this.props.field} value={i} isMulti={true} onDelete={() => this.handleRemove(index)} isEdit />
          </div>
        )
      }
      const isShowError = this.getIsShowError(i, field?.showType)
      return <div key={index} className="link-card-box">
        <DataLinkCard
          value={i}
          templates={templatesold}
          isShowError={isShowError}
          onDelete={() => this.handleRemove(index)}
          onlineClick={() => this.handleDataLinkLineClick(i, index, items)}
        />
      </div>
    })
  }

  fetchDataLink = async () => {
    const { flowId, bus, billSpecification, submitterId } = this.props
    const {
      field: { referenceData, filterId, importMode },
      field,
      islock,
      items
    } = this.state
    const form = await bus.getFieldsValue()
    if (form && !form?.specificationId) {
      form.specificationId = billSpecification?.id || billSpecification
    }
    const type = get(referenceData, 'platformId.type')

    return api.open('@bills:SelectDataLinkUpdateModal', {
      entityInfo: {
        values: items.map(i => i.dataLinkId),
        dataLink: { type, id: referenceData.id },
        field,
        referenceData,
        flowId,
        selectedEntity: items,
        islock,
        filterId,
        multiple: importMode === 'MULTIPLE'
      },
      flowId,
      formValue: {
        form
      },
      submitterId: form?.submitterId?.id || submitterId?.id
    })
  }
  handleUpdateSelect = async () => {
    const { external, field, bus } = this.props
    let { templatesold, templates = [], items = [] } = this.state
    if (templates.length == 0) {
      message.error(i18n.get('暂无权限'))
      return
    }

    const { data } = await this.fetchDataLink()
    const isPrivateCar = field?.referenceData?.platformId?.type === 'PRIVATE_CAR'
    if (Array.isArray(data) || field.behaviour === 'REF') {
      const list = field.behaviour === 'REF' && field.importMode === 'SINGLE' ? [data] : data
      const selectData = []
      console.log(list)
      list.forEach(it => {
        if (!it) return
        if (!items.find(oo => oo.dataLinkId === it.id)) {
          const tem = templatesold.find(oo => oo?.entity?.id === it.dataLink.entityId)

          if (!!tem) {
            const { components, templateId } = tem
            const dataLinkId = it.id
            const dataLink = it.dataLink
            const dataLinkForm = {}
            console.log('loooooook', components)
            components.forEach(oo => {
              const name = oo.name
              if (!isPrivateCar || (isPrivateCar && name !== 'useCount' && !name?.includes('用车员工'))) {
                dataLinkForm[name] = dataLink[name]
              }
              // 业务对象的话，取值id 就可以了，解决数据中有id,有对象的情况，@王伟东@杨志硕
              if (oo.source === 'dataLink' && oo.type === 'dataLink') {
                dataLinkForm[name] = dataLink[name]?.data?.dataLink?.id
              }
            })
            const v = { dataLinkId, dataLinkTemplateId: templateId, dataLinkForm }
            selectData.push(v)
          }
        }
      })
      console.log('loooooook')
      console.log(selectData)
      this.handleChange([...items, ...selectData])
      return
    }

    let prodata = { dataLinkForm: data.dataLink, dataLinkId: data.id }
    templatesold.forEach(i => {
      if (i.entity.id === data.dataLink.entityId) {
        prodata.dataLinkTemplateId = i.templateId
      }
    })

    api
      .open('@bills:SelectDataLinkEditModal', {
        entityInfo: {
          value: prodata,
          field,
          templates: templatesold,
          templatesold: templatesold,
          external,
          bus: this.bus,
          status: 'update',
          action: 'dataLinkUpdate',
          addCallback: this.addCallback,
          flowBus: bus
        }
      })
      .then(result => {
        const items = [...this.state.items, result.data]
        this.handleChange(items)
      })
  }

  handleClickClearValue = () => {
    this.setState({ items: [] })
    const { field } = this.props
    if (field?.assignmentRule?.clearAssignWhenManualDelete) {
      this.fnUpdateData([])
    }
  }

  renderUpdate = () => {
    const {
      field: { importMode, behaviour, showType, optional, label },
      field,
      flowId,
      external,
      isForbid
    } = this.props
    const { items, templates, templatesold } = this.state
    return (
      <div className={styles['data-link-input-wrapper']}>
        <DataLinkEditHeader
          isEdit
          field={field}
          clearButton={
            showType === 'FIELD' && items.length !== 0 ? (
              <Button type="primary" size="small" style={{ marginLeft: 8 }} ghost onClick={this.handleClickClearValue}>
                解除关联
              </Button>
            ) : null
          }
          flowId={flowId}
          external={external}
          isForbid={isForbid}
        />
        <div className="datalink-select">
          {(importMode === 'MULTIPLE' || (importMode == 'SINGLE' && items.length < 1)) && behaviour === 'INSERT' && (
            <Button onClick={this.handleRenewSelect} theme="highlight" category="secondary">
              {i18n.get('点击添加')}
            </Button>
          )}
          {(importMode === 'MULTIPLE' || (importMode == 'SINGLE' && items.length < 1)) && behaviour === 'UPDATE' && (
            <Button onClick={this.handleUpdateSelect} theme="highlight" category="secondary">
              {i18n.get('选择')}
            </Button>
          )}
          <>
            {behaviour === 'MORE' && (
              <Button onClick={this.handleRenewSelect} className="mr-8" theme="highlight" category="secondary">
                {i18n.get('点击添加')}
              </Button>
            )}
            {['MORE', 'REF'].includes(behaviour) && (
              <Button onClick={this.handleUpdateSelect} theme="highlight" category="secondary">
                {i18n.get('选择')}
              </Button>
            )}
          </>
        </div>
        {showType === 'FIELD' && items.length !== 0 ? (
          <div className={styles['data-link-single']}>
            <div className={styles['data-link-single-clear']} onClick={this.handleClickClearValue}>一键清空</div>
            <DataLinkEditSingle
              elements={this.props.elements}
              bus={this.bus}
              value={items.length > 0 ? items[0] : {}}
              templates={templates}
              label={label}
              templatesold={templatesold}
              handleType={this.handleType}
              optional={optional}
            />
          </div>
        ) : (
          this.renderList()
        )}
      </div>
    )
  }

  renderEdit = () => {
    let {
      field: { label, importMode, optional },
      flowId,
      showHeader = true,
      field,
      external,
      isForbid
    } = this.props

    let { templates, items, templatesold } = this.state
    if (
      (templates.length === 0 && !flowId) ||
      (flowId && templatesold.length === 0) ||
      (flowId && items.length === 0 && templates.length === 0)
    ) {
      let tipClass = optional === false ? styles['data-link-red'] : styles['data-link-err']
      return <div className={tipClass}>{i18n.get('暂无权限')}</div>
    }
    if (importMode === 'SINGLE') {
      return (
        <>
          {showHeader && (
            <DataLinkEditHeader isEdit field={field} flowId={flowId} isForbid={isForbid} external={external} />
          )}
          <div className={styles['data-link-single']}>
            <div className={styles['data-link-single-clear']} onClick={this.handleClickClearValue}>一键清空</div>
            <DataLinkEditSingle
              elements={this.props.elements}
              bus={this.bus}
              value={items.length > 0 ? items[0] : {}}
              templates={templates}
              label={label}
              templatesold={templatesold}
              handleType={this.handleType}
              optional={optional}
            />
          </div>
        </>
      )
    } else {
      return (
        <div className={styles['data-link-input-wrapper']}>
          <DataLinkEditHeader isEdit field={field} />
          <div className="datalink-select">
            <Button onClick={this.handleRenewSelect} theme="highlight" category="secondary">
              {i18n.get('点击添加')}
            </Button>
          </div>
          {this.renderList()}
        </div>
      )
    }
  }
  render() {
    const {
      field: { behaviour = 'INSERT', showType, name },
      field,
      bus,
      external,
      flowId,
      isForbid,
      billSpecification,
      form,
    } = this.props
    const { templates, items, templatesold, editingKey } = this.state
    const isBudgetAdjust = name === 'budgetAdjustDetails'
    const isHabSetValue = isHabBinding(billSpecification, field?.field)

    const flowFormFieldsValue = form.getFieldsValue()
    if (showType === 'TABLE' || isBudgetAdjust) {
      return (
        <DataLinkEditTable
          isEdit={!isBudgetAdjust}
          field={field}
          dataSource={items}
          templates={templates}
          templatesold={templatesold}
          bus={this.bus}
          onSelect={behaviour !== 'INSERT' ? this.handleUpdateSelect : null}
          flowBus={bus}
          flowFormFieldsValue={flowFormFieldsValue}
          onChange={this.handleChange}
          onFieldEdit={this.handleUpdate}
          onRowClick={this.handleRowClick}
          onGetActiveDataIndex={editingKey => this.setState({ editingKey })}
          editingKey={editingKey}
          flowId={flowId}
          isForbid={isForbid}
          external={external}
          isHabSetValue={isHabSetValue}
        />
      )
    }
    if (behaviour === 'INSERT') {
      return this.renderEdit()
    } else {
      return this.renderUpdate()
    }
  }
}
