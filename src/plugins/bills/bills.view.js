import React, { PureComponent } from 'react'
import styles from './bills.view.module.less'
import MessageCenter from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { app, app as api } from '@ekuaibao/whispered'
const { Box } = app.require('@elements/ekbc-basic/layout/Box')
import ListTab from './parts/left-part/ListTab'
import RightPart from './parts/right-part/RightPart'
import PNG_TMC_BG from '../../images/tmc_bg.png'
import { showModal } from '@ekuaibao/show-util'
import { getV } from '@ekuaibao/lib/lib/help'
import { setValidateError, getFlowInfoById } from './bills.action'
import { related } from '../../elements/feeDetailViewList/Related'
import { loginTrack } from './util/trackBill'
import BillTableView from './BillTableView'
import { getChangeTemplateModalKey } from './util/billUtils'
import BillViewHeader from './views/BillViewHeader'
import clx from 'classnames'
import AI<PERSON>hatBox from '../../components/AIChatBox'
import { getBoolVariation } from '../../lib/featbit'


const DefaultBreadcrumbItems = [
  {
    key: 'newHomePage',
    label: i18n.get('首页')
  },
  {
    key: 'myBill',
    label: i18n.get('我的单据')
  }
]

@EnhanceConnect(
  state => ({
    ONLYTMC: state['@common'].powers.ONLYTMC,
    recentlyUsedSpecifications: state['@custom-specification'].recentlyUsedSpecifications,
    specificationGroupsList: state['@custom-specification'].specificationGroupsList,
    userinfo: state['@common'].userinfo.data
  }),
  { setValidateError, getFlowInfoById }
)
export default class BillView extends PureComponent {
  bus = new MessageCenter()
  state = {
    viewType: localStorage.getItem(`BillViewType-${this.props.userinfo?.staff?.id}`) || 'list',
    chatOpen: true
  }

  componentDidMount() {
    api.on('create:new:bill', this.createNewBill)
    this.setNewBill()
    this.doActions()
    loginTrack()
  }

  componentWillUnmount() {
    api.un('create:new:bill', this.createNewBill)
    api.invokeService('@bills:update:dimention:currency', null)
  }

  createNewBill = (opts = {}) => {
    const { isModal = false } = opts
    const modalKey = getChangeTemplateModalKey()
    if (modalKey === '@bills:NewChangeTemplateModal') {
      // 新建
      return api
        .open(modalKey, {
          reloadSpecification: true,
          isForm: 'bills'
        })
        .then(data => {
          if (isModal) {
            return api.open('@bills:BillInfoCreatePopup', {
              bus: this.bus,
              data,
              isAlwaysOk: true
            })
          }
          this.renderBillContent(data)
        })
    }
    api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned').then(_ => {
      let { specificationGroupsList } = this.props
      let hasTemplates = false
      if (specificationGroupsList.length) {
        for (let item of specificationGroupsList) {
          if (item.specifications.length > 0) {
            hasTemplates = true
            break
          }
        }
      }
      if (!hasTemplates) {
        showModal.warning({
          title: i18n.get('无法创建单据'),
          content: i18n.get('没有可用的单据模板，请联系系统管理员')
        })
        return
      }
      const modalKey = getChangeTemplateModalKey()
      api
        .open(modalKey, {
          specificationGroups: specificationGroupsList,
          isForm: 'bills'
        })
        .then(data => {
          if (isModal) {
            return api.open('@bills:BillInfoCreatePopup', {
              bus: this.bus,
              data,
              isAlwaysOk: true
            })
          }
          this.renderBillContent(data)
        })
    })
  }

  renderBillContent = data => {
    const { setValidateError } = this.props
    related.clearRelatedData()
    setValidateError({ bill: [], detail: [], trip: [] })
    let callback = () => {
      this.bus.emit('list:select:change', {
        formType: data.type,
        state: 'new',
        currentSpecification: data,
        requisitionInfo: undefined
      })
    }
    this.bus.invoke('check:value:changed').then(
      res => {
        callback()
      },
      error => {
        if (error === 'cancel') return
        callback()
      }
    )
  }

  doActions = () => {
    const { params, getFlowInfoById } = this.props
    if (params?.action === 'New') {
      api.emit('create:new:bill')
    } else if (params?.action === 'Check' && params?.id) {
      getFlowInfoById({ id: params?.id }).then(action => {
        if (action.error) {
          return
        }
        const dataSource = getV(action, 'payload.value', {})
        api.open('@bills:BillStackerModal', {
          viewKey: 'BillInfoView',
          dataSource,
          fromSource: 'myBill',
          showButton: true
        })
      })
    } else if (params?.action === 'Edit' && params?.id) {
      this.bus.emit('list:select:change', {
        id: params?.id
      })
    }
  }

  setNewBill = () => {
    let { specificationGroupsList = [] } = this.props
    const specificationId = api.invokeService('@new-homepage:get:location:search', {
      props: this.props,
      name: 'specificationId'
    })
    if (!specificationId) {
      return
    }
    let obj1
    specificationGroupsList.forEach(a => {
      a.specifications.forEach(item => {
        if (item.id === specificationId) obj1 = item
      })
    })
    if (!obj1) return
    const billTemplate = obj1
    if (this.state.viewType === 'table') {
      return api.open('@bills:BillInfoCreatePopup', {
        bus: this.bus,
        data: billTemplate
      })
    }
    this.renderBillContent(billTemplate)
  }

  changeViewType = type => {
    const { viewType } = this.state
    if (type === viewType) return null

    this.setState({ viewType: type }, () => {
      localStorage.setItem(`BillViewType-${this.props.userinfo?.staff?.id}`, type)
    })
  }

  isCanCreate = () => {
    const supplierPortalConfig = api.getState('@common').supplierPortalConfig
    if (supplierPortalConfig?.flowRule?.allowNewBuildFlow === false) {
      // 供应商门户配置的此外部员工没有新建单据的权限
      return false
    }
    return true
  }

  render() {
    const { ONLYTMC } = this.props
    const { viewType, chatOpen } = this.state

    if (ONLYTMC) {
      return (
        <div className="layout-content-tmcWrapper">
          <img src={PNG_TMC_BG} alt="" />
          {i18n.get('您正在使用易快报差旅预订专版，如需差旅预订，请移步手机端')}
        </div>
      )
    }
    const ifCanCreate = this.isCanCreate()

    return (
      <>
        <div className={clx(styles['bills-view-warp'], styles[`bills-view-warp-${viewType}`])}>
          {viewType === 'table' ? (
            <>
              <div className={clx(styles['bills-view-warp-table-inner'])}>
                <BillViewHeader ifCanCreate={ifCanCreate} isList={viewType !== 'table'} bus={this.bus} changeViewType={this.changeViewType} />
                <BillTableView bus={this.bus} changeViewType={this.changeViewType}  showAIIcon={getBoolVariation('aprd-5394-ai-chat')}/>
              </div>
              {
                viewType === 'table' && chatOpen && <AIChatBox
                  chatId="BillView:"
                  className={clx(styles['ai-chat-box'], { [styles['chatHidden']]: !chatOpen })}
                  onClose={() => {
                    this.setState({
                      chatOpen: false
                    })
                  }}
                  bus={this.bus}
                />
              }
            </>
          ) : (
            <Box id="bills" className={styles['bills-view']} bus={this.bus}>
              <ListTab ifCanCreate={ifCanCreate} currentViewType={viewType} changeViewType={this.changeViewType} />
              <RightPart className="ovr-h" scene={'OWNER'} />
            </Box>
          )}
        </div>
      </>
    )
  }
}
