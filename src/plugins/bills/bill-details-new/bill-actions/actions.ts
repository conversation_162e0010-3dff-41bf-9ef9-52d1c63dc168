import { FlowAction } from "../../layers/bill-info-popup/FlowAction";
import { ApproveAction } from "./approve";
import { RejectAction } from "./reject";
import { PrintAction, PrintInvoiceAction, PrintedAction, PrintRemindAction, PrintDocAction } from "./print";
import { StartSignatureAction, ViewSignatureAction } from "./signature";
import {
  AddExpressAction,
  SkipSendAction,
  ReceiveAction,
  ReceiveExceptionAction,
  CancelReceiveExceptionAction,
} from "./send-receive";
import { ModifyAction } from "./modify";
import {
  ShiftApproveAction,
  AddSignNodeAction,
  AdminShiftNodeAction,
  SuspendAction,
  BackAction,
} from "./node-management";
import {
  NullifyAction,
  ShareAction,
} from "./other";
import { PayAction, WithdrawPayAction } from "./pay";
import { AdminAddNodeAction, AdminSkipNodeAction } from "./admin";
import { ActivateAction } from "./activate";
import { SaveAction } from "./save";
import { SubmitAction } from "./submit";
import { ExportAction } from "./export";
import { AlterAction } from "./alter";
import { CopyAction } from "./copy";
import { RetractAction } from "./retract";
import { CancelAction } from "./cancel";
import { DeleteAction } from "./delete";
import { UrgeAction } from "./urge";
import { MarkedReadAction } from "./cc";
import { CommentAction } from "./comment";
import { InvoiceReminderAction }  from './invoice'
import { WithdrawAction } from "./withdraw";

/**
 * 对应 src/plugins/bills/layers/bill-info-popup/FlowAction.ts 中的 FlowAction 枚举
*/
export const actionsMap = {
  [FlowAction.Save]: SaveAction,
  [FlowAction.Submit]: SubmitAction,
  [FlowAction.Comment]: CommentAction,
  // 审批
  [FlowAction.Agree]: ApproveAction,
  [FlowAction.Reject]: RejectAction,
  // 支付
  [FlowAction.Pay]: PayAction,
  [FlowAction.Withdraw]: WithdrawAction,
  // 打印
  [FlowAction.Print]: PrintAction,
  [FlowAction.PrintInvoice]: PrintInvoiceAction,
  [FlowAction.PrintDoc]: PrintDocAction,
  [FlowAction.Printed]: PrintedAction,
  [FlowAction.PrintRemind]: PrintRemindAction,
  // 签署
  [FlowAction.StartSignature]: StartSignatureAction,
  [FlowAction.ViewSignature]: ViewSignatureAction,
  [FlowAction.Activate]: ActivateAction,
  // 寄送
  [FlowAction.Send]: AddExpressAction,
  [FlowAction.AddExpress]: AddExpressAction,
  [FlowAction.SkipSend]: SkipSendAction,
  [FlowAction.JumpExpress]: SkipSendAction,
  // 收单
  [FlowAction.Receive]: ReceiveAction,
  [FlowAction.ReceiveException]: ReceiveExceptionAction,
  [FlowAction.CancelReceiveException]: CancelReceiveExceptionAction,

  [FlowAction.Nullify]: NullifyAction,
  [FlowAction.Modify]: ModifyAction,

  [FlowAction.ShiftNode]: ShiftApproveAction,
  [FlowAction.ShiftApprove]: ShiftApproveAction,
  [FlowAction.AddNode]: AddSignNodeAction,
  [FlowAction.AddSignNode]: AddSignNodeAction,
  [FlowAction.AdminShiftNode]: AdminShiftNodeAction,
  [FlowAction.Suspend]: SuspendAction,
  [FlowAction.Back]: WithdrawPayAction,
  [FlowAction.Urge]: UrgeAction,
  [FlowAction.MarkedRead]: MarkedReadAction,
  [FlowAction.AdminSkipNode]: AdminSkipNodeAction,

  [FlowAction.Alter]: AlterAction,
  [FlowAction.Copy]: CopyAction,
  [FlowAction.Retract]: RetractAction,
  [FlowAction.Cancel]: CancelAction,
  [FlowAction.Delete]: DeleteAction,

  [FlowAction.ExportAttachment]: ExportAction,
  [FlowAction.Share]: ShareAction,
  [FlowAction.AdminBack]: BackAction,
  [FlowAction.AdminAddNode]: AdminAddNodeAction,

  [FlowAction.RemindAddingInvoice]: InvoiceReminderAction,
} as const;