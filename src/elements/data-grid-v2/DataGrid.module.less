@import '~@eku<PERSON>bao/eui-styles/less/token';

.container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 210px);
  .header {
    padding: 16px 16px 4px;
  }
  .body {
    flex: auto;
    overflow: hidden;
    height: 100%;
    position: relative;
    max-height: 960px;
    padding: 0 16px;
    .ant-search-input-wrapper {
      right: 69px !important;
    }
  }
  .footer {
    padding: 12px 24px;
    display: flex;
    align-items: center;
    height: 56px;
    flex-shrink: 0;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2), 0px 1px 0px 0px rgba(29, 43, 61, 0.15);
  }
  .tableWrapper {
    height: calc(100% - 16px);
    :global {
      .dx-datagrid-rowsview.dx-scrollable {
        overflow: auto;
      }
    }
  }

  .tableWrapperError {
    :global {
      .dx-datagrid-nodata {
        display: none !important;
      }
    }
  }

  .table_actions {
    position: absolute;
    right: 16px;
    top: 0;
    height: 52px;
    display: flex;
    align-items: center;

    .global_search {
      position: relative;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;

      &_checkbox {
        line-height: 52px;
        margin-left: @space-4;

        span:last-child {
          padding: 0 0 0 @space-2;
        }
      }
    }

    .columnChooser {
      margin-right: 12px;
    }

    .divider {
      width: 1px;
      height: 16px;
      margin: 0 @space-6;
      background: rgba(29, 43, 61, 0.15);
    }
  }

  .sceneRow {
    display: flex;
    flex-direction: row;
    .sceneWrapper {
      flex: 1;
    }
    .menuBar {
      flex-shrink: 0;
      line-height: 15px;
      height: 20px;
      margin: 0 0 0 auto;
      padding-left: @space-3;
      white-space: nowrap;
    }
  }

  :global {
    .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-header-panel .dx-toolbar .dx-toolbar-items-container {
      padding-right: 400px;
    }

    .dx-widget.ekb-table-inner .dx-datagrid .dx-datagrid-rowsview tr td div.ai-agent-label-tag .ai-agent-label-tag-text{
      font: var(--eui-font-note-b1) !important;
    }
  }
}

.containerHeight {
  height: calc(100vh - 158px) !important;
  flex: unset !important;
}

.simpleContainer {
  display: flex;
  width: 100%;
  flex-direction: column;
  height: 100%;
  padding: 0 16px;
  .body {
    flex: auto;
    overflow: hidden;
    height: 100%;
    max-height: 1000px;
    position: relative;
  }
  .tableWrapper {
    height: calc(100% - 74px);
  }
  .tableWrapper-noGroup {
    &:first-child {
      //content: ' ';
      //position: absolute;
      border-top: 1px solid #ddd;
      //width: 100%;
      //z-index: 100;
    }
  }
  .footer {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    margin-top: 12px;
    padding: 12px 24px;
    width: 100%;
    height: 56px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 24px 0px rgba(29, 43, 61, 0.2), 0px 1px 0px 0px rgba(29, 43, 61, 0.15);
  }
  .wb-bw {
    word-break: break-all;
  }
  .search {
    right: 10px;
  }
}
