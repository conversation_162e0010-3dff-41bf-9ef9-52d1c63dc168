import { BillActionBase } from './base-action'
import { action } from './constant'

export class AddExpressAction extends BillActionBase {
  action = action.addExpress
  label = i18n.get('添加寄送信息')
}

export class SkipSendAction extends BillActionBase {
  action = action.jumpExpress
  label = i18n.get('跳过寄送')
}

export class ReceiveAction extends BillActionBase {
  action = action.receiveExpress
  label = i18n.get('确认收单')
}

export class ReceiveExceptionAction extends BillActionBase {
  action = action.receiveExceptionExpress
  label = i18n.get('收单异常')
}

export class CancelReceiveExceptionAction extends BillActionBase {
  action = action.cancelReceiveExceptionExpress
  label = i18n.get('取消收单异常')
}