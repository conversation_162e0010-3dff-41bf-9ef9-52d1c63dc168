/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017/7/13.
 */
@import '~@ekuaibao/eui-styles/less/token.less';
@import '~@ekuaibao/web-theme-variables/styles/colors';

@view-panel-width: 64px;

.header-wrapper {
  background-color: #ffffff;
  padding: 0 16px;
  margin: 0 -16px;

  .adaptive-actions {
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;

    &.is-line-feed {
      flex-grow: 1;
      padding-top: 12px;
    }
  }

  :global {
    .header {
      min-height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      padding-bottom: 8px;
      border-bottom: 1px dashed var(--eui-line-divider-default);
      position: relative;
      padding-right: @view-panel-width;
      &.edit{
        border-bottom:none;
      }
      &.header-two-line {
        padding-right: 0;

        .view-panel {
          position: static;
        }
      }
      .title {
        flex: 1;
        display: flex;
        align-items: center;
        line-height: 30px;
        font-weight: 500;
        .font-size-2;
        color: var(--eui-text-title);
        word-break: keep-all;
        white-space: nowrap;
        .count {
          margin-left: 5px;
          font-size: 12px;
        }
      }
      .show-type {
        padding: 1px 8px;
        color: var(--eui-text-title);
        font: var(--eui-font-body-r1);
        cursor: pointer;
        display: flex;
        align-items: center;
        .icon {
          margin-left: 4px;
        }
        &.show-type--active {
          color: var(--eui-primary-pri-500);
        }
      }
    }
    .total-money {
      // padding: 12px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      // border-top: 1px dashed #e9e9e9;
      // border-bottom: 1px dashed #e9e9e9;
      // margin: 12px 0;
      height: 42px;
      > span {
        color: var(--eui-text-caption);
        font: var(--eui-font-body-r1);
      }
      .amount-wrapper {
        display: flex;
        .amount {
          font-family: DIN Alternate;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          line-height: 24px;
          font-weight: 600;
          .text {
            margin-right: 5px;
            .convert-into{
              margin-left: 8px;
              margin-right: 4px;
              font: var(--eui-font-body-r1);
              color: var(--eui-text-caption);
            }
          }
        }
      }
    }
    .fold-detail {
      margin-bottom: -@space-6;
      border-bottom: 0;
    }
    .action {
      margin-top: 12px;
      // padding-left: 8px;
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 14px;
      width: 100%;
      flex-direction: row;
      position: relative;
      border-top: 1px dashed var(--eui-line-divider-default);
      &.action5 {
        .detail_action_left {
          flex: 1;
          display: flex;
          flex-direction: row;
          align-items: center;
          height: 100%;
          overflow-x: auto;
        }
        .disabled {
          background-color: @gray-3 !important;
          border: solid 1px #ebebeb !important;
          color: @gray-8 !important;
          cursor: not-allowed !important;
        }
        .add-button {
          .add-detail {
            padding: 2px 12px;
          }
          .import-detail {
            padding: 2px 8px;
          }
        }
        .import {
          padding: 2px 8px;
        }
        .other-action {
          margin-right: 8px;
          flex-shrink: 0;
        }
      }
      .checkall {
        color: var(--eui-text-title);
      }
      .disabled {
        background-color: @gray-3 !important;
        border: solid 1px #ebebeb !important;
        color: @gray-8 !important;
        cursor: not-allowed !important;
      }
      .add-button {
        display: flex;
        align-items: center;
        border: 1px solid #b9e6eb;
        border-radius: 2px;
        cursor: pointer;
        .add-detail {
          padding: 2px 8px;
          flex: 1;
          border-right: 1px solid #b9e6eb;
        }
        .batch-import {
          width: 24px;
          text-align: center;
          display: inline-block;
        }
        .import-detail {
          padding: 2px 8px;
        }
      }
      .import {
        border: 1px solid #b9e6eb;
        border-radius: 2px;
        padding: 2px 8px;
      }
      .line {
        flex-shrink: 0;
        margin: 0 1px;
      }
      .other {
        border: 1px solid #b9e6eb;
        padding: 2px 12px;
        .divider {
          width: 0;
          height: 12px;
          border-right: 1px solid #b9e6eb;
          margin-left: 12px;
          margin-right: 12px;
        }
      }
      span {
        white-space: nowrap;
        display: flex;
        align-items: center;
      }
      img {
        margin-right: 4px;
      }
      .checkbox {
        margin-right: 4px;
      }
    }
  }
  .select {
    color: #2b9aab;
    border: 1px solid #2b9aab;
  }

  .import-menu {
    width: 100px;
  }
}

.view-panel {
  position: absolute;
  right: 0;
  top: 0;
}

.group-menu {
  border-radius: 6px;
  background: var(--eui-bg-float);
  box-shadow: var(--eui-shadow-down-3);
  padding: 8px 12px;
  .group-menu-inner {
    max-height: 376px;
    overflow-y: auto;
  }
  .group-menu-title {
    color: var(--eui-text-caption);
    font: var(--eui-font-body-r1);
    margin-bottom: 8px;
  }
  .group-menu-list {
    display: flex;
    flex-direction: column;
    margin: 0;
    margin-top: 8px;
  }
  .group-menu-item {
    color: var(--eui-text-title);
    font: var(--eui-font-body-r1);
    border-radius: 4px;
    padding: 4px 8px;
    margin-bottom: 4px;
    position: relative;
    cursor: pointer;
    &:last-child {
      margin-bottom: 0;
    }
    &:hover {
      background: var(--eui-fill-hover);
    }
    &.active {
      color: var(--eui-primary-pri-500);
      background: var(--eui-fill-active);
    }
    .highlight {
      color: var(--eui-primary-pri-500);
      background: var(--eui-base-bg);
      padding: 0;
    }
    .tick {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0 28px;
    .desc {
      margin: 8px 0;
      color: var(--eui-text-placeholder);
      text-align: center;
      font: var(--eui-font-body-r1);
    }
  }
}

.excel-drop {
  width: 122px;
  text-align: center;
}

.state-icon {
  margin-left: 10px;
}

.third-party-sub-menu {
  background: #ffffff;
  opacity: 0.5;
  color: #333333;
}

.normal {
  color: #1e96fa;
  width: 16px;
  height: 16px;
}

:global {
  .affix-in-modal {
    .ant-affix {
      left: unset !important;
      top: 80px !important;
    }
  }
}
