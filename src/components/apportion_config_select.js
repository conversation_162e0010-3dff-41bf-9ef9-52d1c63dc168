/*
 * @Author: joy<PERSON>oy
 * @Date: 2024-03-27 14:14:11
 * @LastEditors: wangweidong <EMAIL>
 * @LastEditTime: 2024-04-10 11:18:39
 * @FilePath: /web/src/components/apportion_config_select.js
 */
/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/16 下午7:32.
 */
import React, { PureComponent, useEffect, useState } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from './layout/FormWrapper'
import styles from './apportion-config-select.module.less'
import { getBoolVariation } from '../lib/featbit'
import { Image } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
const exampleImg = app.require('@images/apportion_example.png');

import { OutlinedTipsInfo, OutlinedTipsAdd, OutlinedEditDeleteTrash } from '@hose/eui-icons'
import { Radio, Form, Button, Space, Select, TreeSelect, Spin, Tooltip, InputNumber, Input, Alert } from '@hose/eui'
import {operators} from './utils/utilFunctionsParams'


export const ApportionType = {
  AVERAGE: 'AVERAGE', //平均分摊
  BY_RATIO: 'BY_RATIO', //固定数值比例
  BY_FIELD: 'BY_FIELD', //根据业务对象字段
  MANUAL: 'MANUAL', //手动分摊
}

@EnhanceField({
  descriptor: {
    type: 'apportion-config-select'
  },
  validator: (field, props) => (rule, value, callback) => {
    return callback()
  },
  wrapper: wrapper()
})
export default class ApportionConfigSelect extends PureComponent {
  render() {
    return <ApportionConfigSelectFunc {...this.props} />
  }
}

const ApportionConfigSelectFunc = props => {
  const [form] = Form.useForm()
  const { describe } = props.field
  const [type, setType] = useState()
  const [loading, setLoading] = useState(false)

  const [specificationGroups, setSpecificationGroups] = useState([])
  const [gIds, setGIds] = useState([])
  const [ids, setIds] = useState([])

  const onRadioChange = e => {
    setType(e?.target?.value)

    if (e?.target?.value === '2' && !(props.value?.apportionConfigs?.length > 0)) {
      form.setFieldsValue({
        apportionConfigs: [
          {
            groupIds: []
          }
        ]
      })
    }

    props.onChange &&
      props.onChange({
        ...props.value,
        hasApportion: e?.target?.value === '2'
      })
  }

  const onValuesChange = () => {
    const { apportionConfigs } = form.getFieldsValue()

    const res = apportionConfigs
      .filter(item => item === undefined || item?.groupIds) // 新增的为undefined，删除的没有groupIds
      .map(item => {
        const { groupIds, ...others } = item || { groupIds: [] }
        const _groupIds = groupIds?.map(id => (typeof id === 'string' ? id : id.value)) || []
        const filterSpecificationGroupIds = _groupIds?.filter(id => gIds.includes(id))
        const filterSpecificationIds = _groupIds?.filter(id => ids.includes(id))
        return {
          ...others,
          filterSpecificationGroupIds,
          filterSpecificationIds
        }
      })
    props.onChange &&
      props.onChange({
        hasApportion: type === '2',
        apportionConfigs: res
      })
  }

  useEffect(() => {
    setLoading(true)
    const groups = props.field?.tags?.specifications?.filter(group => group.active) || []
    setGIds(groups.map(item => item.id))
    let _ids = []
    const res = groups.map(line => {
      const { specifications, ...others } = line
      const newSpec = specifications?.filter(item => item.active) || []
      _ids = [..._ids, ...newSpec.map(item => item.id)]
      return {
        ...others,
        children: newSpec
      }
    })
    setIds(_ids)
    setSpecificationGroups(res)
    setLoading(false)
  }, [props.field])

  useEffect(() => {
    if (props.value?.hasApportion) {
      setType('2')
    } else {
      setType('1')
    }
    form.setFieldsValue({
      apportionConfigs: props.value?.apportionConfigs?.map(item => ({
        ...item,
        groupIds: [...item?.filterSpecificationIds, ...item?.filterSpecificationGroupIds]
      })) || [
          {
            groupIds: []
          }
        ]
    })
  }, [props?.value])

  useEffect(() => {
    props.bus.getApportionConfigValidateFields = form.validateFields
    return () => {
      props.bus.getApportionConfigValidateFields = () => { }
    }
  }, [])

  return (
    <div className={styles['apportion-config-select']}>
      <Spin spinning={loading} text="加载中...">
        <Radio.Group onChange={onRadioChange} value={type}>
          <Space size={24}>
            <Radio value="1">{i18n.get('累加统计（不执行分摊规则）')}</Radio>
            <Radio value="2">
              <Space size={4}>
                <span>{i18n.get('按分摊规则累加统计')}</span>
                <Tooltip title={describe}>
                  <OutlinedTipsInfo fontSize={14} color="var(--eui-icon-n2)" />
                </Tooltip>
              </Space>
            </Radio>
          </Space>
        </Radio.Group>
        {type === '2' && (
          <ApportionConfigComp
            {...props}
            form={form}
            type={type}
            onValuesChange={onValuesChange}
            specificationGroups={specificationGroups}
          />
        )}
      </Spin>
    </div>
  )
}

const ApportionConfigComp = props => {
  const { field, form, specificationGroups, value } = props
  const [visible, setVisible] = useState(false)
  const hasManualType = getBoolVariation('aprd-5665-datalink') && field?.config?.apportionType.find(item => item.value === ApportionType.MANUAL)
  const hasAlert = getBoolVariation('aprd-5665-datalink') && value?.apportionConfigs?.some(item => item.apportionType === ApportionType.MANUAL)  
  return (
    <section className={styles['apportion-config-comp-wrapper']}>
      {hasAlert && <>
        <Alert
          type="info"
          message={
            <div>
              <span>注意：启用“手动分摊”后，即可在填单时手动输入分摊额</span>
              <Button
                category='text'
                theme='highlight'
                style={{ marginLeft: '8px' }}
                onClick={() => setVisible(true)}
              >查看效果</Button>
            </div>
          }
          className='overflow-description'
        />
        <Image
          src={exampleImg}
          preview={{
            visible,
            src: exampleImg,
            width: 704,
            className:styles['image-preview'],
            onVisibleChange: value => {
              setVisible(value);
            },
          }}
        />
      </>
      }
      <Space className="form-list-label" size={16}>
        <div className="flow-template-width">单据模板<span className='required'>*</span></div>
        <div className="apportion-rule-width">台账分摊规则<span className='required'>*</span></div>
        {hasAlert && <div className="apportion-limit-width">分摊总额限制</div>}
        {hasAlert && <div className="apportion-label-width">分摊列展示名称<span className='required'>*</span></div>}
      </Space>
      <Form form={form} autoComplete="off" onValuesChange={props.onValuesChange}>
        <Form.List name="apportionConfigs">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => {
                return (
                  <Space key={key} style={{ display: 'flex', marginBottom: 8 }} size={16} align="baseline">
                    <Form.Item
                      {...restField}
                      name={[name, 'groupIds']}
                      rules={[{ required: true, message: '单据模板为必选' }]}
                    >
                      <TreeSelect
                        showArrow
                        className="flow-template-width"
                        showSearch
                        multiple={true}
                        placeholder="选择模板"
                        treeNodeFilterProp="name"
                        treeNodeLabelProp="name"
                        treeCheckable={true}
                        showCheckedStrategy={TreeSelect.SHOW_PARENT}
                        maxTagCount="responsive"
                        fieldNames={{ label: 'name', value: 'id' }}
                        allowClear={true}
                        treeData={specificationGroups}
                      />
                    </Form.Item>
                    <div className="apportion-rule-width">
                      <Form.Item
                        {...restField}
                        name={[name, 'apportionType']}
                        rules={[{ required: true, message: '台账规则为必选' }]}
                        style={{ flex: 1 }}
                      >
                        <Select
                          placeholder="选择规则"
                          allowClear={true}
                          options={field?.config?.apportionType || []}
                        />
                      </Form.Item>

                      <Form.Item
                        shouldUpdate={(prev, cur) => {
                          const isChange =
                            prev?.apportionConfigs?.[name]?.apportionType !== cur?.apportionConfigs?.[name]?.apportionType
                          if (isChange) {
                            form.setFieldValue(['apportionConfigs', name, 'apportionValue'], null)
                          }
                          return isChange
                        }}
                        noStyle={true}
                      >
                        {() => {
                          const apportionType = form.getFieldValue(['apportionConfigs', name, 'apportionType'])
                          return (
                            <Form.Item
                              {...restField}
                              name={[name, 'apportionValue']}
                              rules={[
                                {
                                  required: apportionType && [ApportionType.BY_FIELD, ApportionType.BY_RATIO].includes(apportionType),
                                  message: '数值字段为必选'
                                }
                              ]}
                              noStyle={true}
                            >
                              <ApportionValue
                                apportionType={apportionType}
                                tags={props.field.tags?.dataLinkEntity || []}
                                fields={fields}
                                name={name}
                              />
                            </Form.Item>
                          )
                        }}
                      </Form.Item>
                    </div>

                    {hasManualType && <>
                      <Form.Item
                        noStyle={true}
                        shouldUpdate={(prev, cur) => {
                          const isChange =
                            prev?.apportionConfigs?.[name]?.apportionType !== cur?.apportionConfigs?.[name]?.apportionType
                          if (isChange) {
                            form.setFieldValue(['apportionConfigs', name, 'limitConfig'], null)
                          }
                          return isChange
                        }}
                      >
                        {() => {
                          const apportionType = form.getFieldValue(['apportionConfigs', name, 'apportionType'])
                          return (
                            <Form.Item
                              {...restField}
                              noStyle={true}
                              name={[name, 'limitConfig']}
                              // eui 中的错误信息不清楚为何展示不出来，先注释把，靠后的报错了
                              // rules={[
                              //   () => ({
                              //     validator(_, value) {
                              //       if (value && (!value?.operator  || !value?.field)) {
                              //         return Promise.reject(new Error(`请选择${value?.field ? '比较符' : '数值字段'}`));
                              //       }
                              //       return Promise.resolve();
                              //     },
                              //   }),
                              // ]}
                            >
                              <LimitConfig
                                apportionType={apportionType}
                                fields={fields}
                                name={name}
                              />
                            </Form.Item>
                          )
                        }}
                      </Form.Item>
                      <Form.Item
                        noStyle={true}
                        shouldUpdate={(prev, cur) => {
                          const isChange =
                            prev?.apportionConfigs?.[name]?.apportionType !== cur?.apportionConfigs?.[name]?.apportionType
                          if (isChange) {
                            form.setFieldValue(['apportionConfigs', name, 'label'], null)
                          }
                          return isChange
                        }}
                      >
                        {() => {
                          const apportionType = form.getFieldValue(['apportionConfigs', name, 'apportionType'])
                          if (apportionType !== ApportionType.MANUAL) {
                            return null
                          }
                          return (
                            <Form.Item
                              {...restField}
                              name={[name, 'label']}
                              rules={[
                                {
                                  required: apportionType && apportionType === ApportionType.MANUAL,
                                  message: '分摊列展示名称为必选'
                                }
                              ]}
                            >
                              <Input placeholder="请输入分摊列显示名称" className="apportion-label-width" />
                            </Form.Item>
                          )
                        }}
                      </Form.Item>
                    </>}
                    {fields.length > 1 && (
                      <OutlinedEditDeleteTrash
                        onClick={() => {
                          if (fields.length > 1) {
                            remove(name)
                          }
                        }}
                        color="var(--eui-icon-n2)"
                        size={16}
                        style={{ cursor: 'pointer', height: '32px', display: 'inline-flex', alignItems: 'center' }}
                      />
                    )}
                  </Space>
                )
              })}
              <Button category="text" theme="highlight" onClick={() => add()} icon={<OutlinedTipsAdd />}>
                添加规则
              </Button>
            </>
          )}
        </Form.List>
      </Form>
    </section>
  )
}

const ApportionValue = props => {
  const { apportionType, tags, fields, name, ...others } = props
  let Comp = null
  if (apportionType === ApportionType.BY_FIELD) {
    Comp = (
      <Select
        placeholder="请选择数值字段"
        allowClear={true}
        className="apportion-value-width"
        options={tags}
        fieldNames={{ label: 'label', value: 'name' }}
        {...others}
      />
    )
  }
  if (apportionType === ApportionType.BY_RATIO) {
    Comp = <InputNumber addonAfter="%" placeholder="输入百分比" style={{ width: '140px' }} {...others} />
  }

  return Comp
}

const LimitConfig = props => {
  const { apportionType, fields, name, value, onChange } = props
  let Comp = null
  const baseDataProperties = app.getState()['@common'].globalFields.data
  const numericFields = baseDataProperties.filter(item => item.dataType.type === 'money' || item.dataType.type === 'number')

  if (apportionType === ApportionType.MANUAL) {
    const handleOperatorChange = (operatorValue) => {
      const newValue = {
        ...(value || {}),
        operator: operatorValue
      }
      onChange && onChange(newValue)
    }

    const handleFieldChange = (fieldValue) => {
      const newValue = {
        ...(value || {}),
        field: fieldValue
      }
      onChange && onChange(newValue)
    }

    Comp = <div className="apportion-limit-width">
      <Select
        placeholder="比较符"
        allowClear={true}
        options={operators}
        value={value?.operator}
        onChange={handleOperatorChange}
      />
      <Select
        placeholder="请选择数值字段"
        allowClear={true}
        options={numericFields}
        value={value?.field}
        fieldNames={{ label: 'label', value: 'name' }}
        onChange={handleFieldChange}
        optionFilterProp="label"
        showSearch
      />
    </div>
  }

  return Comp
}
