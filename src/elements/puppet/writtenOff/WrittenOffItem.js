/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/7/20.
 */
import styles from './WrittenOffItem.module.less'
import React from 'react'
import { Checkbox } from 'antd'
import Money from '../Money'
import { get } from 'lodash'
import { MoneyMath } from '@ekuaibao/money-math'

import WRITTENOFF_ICON from './writtenoff-icon.svg'
const noop = () => { }

import { repaymentDateTip } from '../../../lib/lib-util'

function WrittenOffItem(props) {
  let {
    item,
    onClick,
    onChange = noop,
    isShowBorder,
    isEdit,
    isChecked = false,
    hasExpenseLink,
    isReceiptTemplate,
    showErrorWhenLink,
    writtenOffByDetail,
    isCross //开通了跨币种核销
  } = props

  const fnOnLineClick = e => {
    if (e.target.type === 'checkbox') {
      e.stopPropagation()
      return
    }
    if (isEdit) {
      return
    }
    onClick(item, e)
  }
  let amount
  if (isReceiptTemplate) {
    if (!item.manualAmount) {
      if (!item.amount) {
        amount = item.maxWTAmount
      } else {
        amount = item.amount
      }
    } else {
      amount = item.manualAmount
    }
  } else {
    amount = item.amount
  }
  const foreignCurrencyLoan = get(item, 'loanInfoId.foreignCurrencyLoan')
  if (foreignCurrencyLoan && !isCross) {
    const isEdit = typeof item.foreignAmount === 'object'
    amount = isEdit
      ? item.foreignAmount
      : {
        ...foreignCurrencyLoan,
        foreign: item.foreignAmount,
        standard: Number(new MoneyMath(item.foreignAmount).times(foreignCurrencyLoan.rate).value).toFixed(
          foreignCurrencyLoan.standardScale
        )
      }
  }

  if (foreignCurrencyLoan && isCross) {
    amount = item.foreignAmount
  }
  let currencyStrCode = !!item.foreignAmount
    ? get(item, 'loanInfoId.totalMoneyNode.foreignStrCode')
    : get(item, 'loanInfoId.totalMoneyNode.standardStrCode')
  if (!currencyStrCode) {
    currencyStrCode = get(writtenOffByDetail, 'amount.standardStrCode')
  }

  return (
    <div className={styles['writtenoff-item']} onClick={fnOnLineClick}>
      {isEdit && <Checkbox className="checkbox" checked={isChecked} onChange={onChange.bind(this, item)} />}
      <img className="item-icon" width={32} height={32} src={WRITTENOFF_ICON} alt="" />
      <div className="item-main">
        <div className="content-writtenoff">
          <div className="name translate-ignore-class">
            <span onClick={isEdit ? fnOnLineClick : undefined}>{item.title}</span>
          </div>
          <div className="item-content">
            {item.loanInfoId?.code ? (
              <>
                <div className="code">{item.loanInfoId?.code}</div>
                <div className="line"></div>
              </>
            ) : null}
            <div className="date">{repaymentDateTip(item.repaymentDate, item.loanInfoId?.state)}</div>
            {item.loanInfoId?.infoType === 'EBUSSCARD' && (
              <>
                <div className="line"></div>
                <span className="code">E商卡</span>
              </>
            )}
          </div>
          {item.fromApply && !item.hasImported && !showErrorWhenLink && (
            <div className={'color-red'}>
              {hasExpenseLink
                ? i18n.get('该借款来自申请事项，与当前报销单关联的申请不一致')
                : i18n.get('该借款相关的申请事项未被当前单据关联')}
            </div>
          )}
        </div>
        <div className="money">
          <Money
            currencySize={12}
            valueSize={16}
            color="rgba(0,0,0,0.85)"
            isShowForeign={!!item.foreignAmount}
            showStrCode={true}
            showSymbol={false}
            currencyStrCode={currencyStrCode}
            value={amount}
          />
        </div>
        {isShowBorder && <div className="border" />}
      </div>
    </div>
  )
}

WrittenOffItem.defaultProps = {
  item: {} //每一条消费记录的数据
}

export default WrittenOffItem
