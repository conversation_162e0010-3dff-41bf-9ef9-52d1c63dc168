/**
 * Created by <PERSON><PERSON> on 2017/11/20.
 */
import React, { PureComponent } from 'react'
import moment from 'moment'
import styles from './DetailItemExpandFields.module.less'
import Money from '../../../elements/puppet/Money'
import { isArray } from '@ekuaibao/helpers'
import fnGetFieldLabel from '../../../components/utils/fnGetFieldLabel'
import { formatDateTime } from '../../../components/utils/fnPredefine4Date'
import buildDataLinkEditsStr from '../../../components/utils/fnFormatDataLinkEdits'
import { app } from "@ekuaibao/whispered";
import { getDisplayName } from '../../utilFn'

const funcTypeMap = {
  text: renderText,
  number: renderText,
  money: renderMoney,
  date: renderDate,
  dateRange: renderDate,
  switcher: renderSwitcher,
  select: renderRef,
  city: renderCity,
  payeeInfo: renderPayeeInfo,
  select_search: renderRef,
  Staff: renderStaff
}

export default class DetailItemExpandFields extends PureComponent {
  render() {
    let { feeTypeForm, components = [], isTripForm, isRecordExpends, allTemplate } = this.props
    let cps = isTripForm
      ? components
      : isRecordExpends
        ? components.filter(cp => cp.showInDetails && cp.field === 'consumptionReasons') //随手记只展示消费事由
        : components.filter(cp => cp.showInDetails)
    if (!cps.length) return null
    return <div className={`${styles['detail-item-expand-fields']}`}>{renderField(cps, feeTypeForm, allTemplate)}</div>
  }
}

function renderField(fields, values = {}, allTemplate) {
  return fields.map((f, index) => {
    let { type, field } = f
    let func = funcTypeMap[type]
    if (!func && type.startsWith('ref')) {
      func = renderRef
    } else if (type.startsWith('list:ref:basedata.Dimension')) {
      return renderMulRef(f, values[field], index)
    } else if (!func && type.endsWith('Staff')) {
      func = renderStaff
    } else if (type === 'dataLinkEdits') {
      func = renderDataLink
    }
    func = func ? func : renderText
    return func(f, values[field], index, allTemplate)
  })
}

function renderMoney(field, value, index) {
  let { label } = field
  if (!value) {
    return (
      <div key={index} className="text">
        {i18n.get(`{__k0}：`, { __k0: fnGetFieldLabel(field) })}
      </div>
    )
  }
  return (
    <div key={index} className="text translate-ignore-class">
      {i18n.get(`{__k0}：`, { __k0: fnGetFieldLabel(field) })}
      <Money style={{ display: 'inline-block' }} value={value} />
    </div>
  )
}

function getNewValue(value) {
  if (typeof value === 'number') return value;
  if (typeof value !== 'string') return
  let reg = /\n/
  const values = value.split(reg)
  return values.map((item, index) => {
    return (
      <p style={{ margin: 0 }} key={index} className="translate-ignore-class">
        {item ? item : values.length === 1 ? i18n.get('无') : ''}
      </p>
    )
  })
}

function renderText(field, value = '', index) {
  let { label, dataType = {} } = field
  let { unit = '' } = dataType
  let str = getNewValue(value)
  return (
    <div key={index} className="text">
      <div className="label">{fnGetFieldLabel(field)}：</div>
      <div className="value translate-ignore-class">{str}</div>
      {unit && <div className="unit">{unit}</div>}
    </div>
  )
}

function renderDate(field, value, index) {
  let { label, withTime, dateTimeType } = field
  let dateFormat = formatDateTime(withTime, dateTimeType)
  let nn
  if (!value) {
    value = ''
  }
  if (isObject(value)) {
    let { start = 0, end = 0 } = value
    start = typeof start === 'string' ? Number(start) : start
    start = typeof start === 'number' ? moment(start) : start
    end = typeof end === 'string' ? Number(end) : end
    end = typeof end === 'number' ? moment(end) : end

    let showValueStart = start ? moment(start).format(dateFormat) : ''
    let showValueEnd = end ? moment(end).format(dateFormat) : ''
    nn = `${showValueStart} – ${showValueEnd}`
  } else {
    nn = value ? moment(parseInt(value)).format(dateFormat) : ''
  }

  return (
    <div key={index} className="text translate-ignore-class">
      {i18n.get(`{__k0}：{__k1}`, { __k0: fnGetFieldLabel(field), __k1: nn })}
    </div>
  )
}

function renderSwitcher(field, value, index) {
  let { label } = field
  let v
  if (value === undefined) {
    v = ''
  } else if (value) {
    v = i18n.get('是')
  } else {
    v = i18n.get('否')
  }
  return (
    <div key={index} className="text translate-ignore-class">
      {i18n.get(`{__k0}：{__k1}`, { __k0: fnGetFieldLabel(field), __k1: i18n.get(v) })}
    </div>
  )
}

function renderRef(field, value, index) {
  let { label, type } = field
  if (!value) {
    value = ''
  }
  let title = ''
  if (isObject(value)) {
    let { code } = value
    title = getDisplayName(value)
    let fieldValue = field && field.field
    if (code && fieldValue !== 'submitterId' && !type.startsWith('ref:basedata.Enum') && title)
      title = title + i18n.get('（') + code + i18n.get('）')
    if (type.startsWith('ref:basedata') && title)
      title = value.active === false ? i18n.get(`{__k0}（已停用）`, { __k0: title }) : title
  }
  return (
    <div key={index} className="text">
      <div className="label">{fnGetFieldLabel(field)}：</div>
      <div className="value translate-ignore-class">{title ? title : i18n.get('无')}</div>
    </div>
  )
}

function renderMulRef(field, value, index) {
  let { label, type } = field
  if (!value) {
    value = ''
  }
  let title = ''
  let names = []
  if (isArray(value) && value.length) {
    value.forEach(v => {
      let name = getDisplayName(v)
      let { code } = v
      if (code && name) {
        name = name + i18n.get('（') + code + i18n.get('）')
      }
      if (name) {
        name = value.active === false ? i18n.get(`{__k0}（已停用）`, { __k0: name }) : name
      }
      if (name) names.push(name)
    })
  }
  if (names.length) {
    title = names.join(i18n.get('comma'))
  }
  return (
    <div key={index} className="text translate-ignore-class">
      {i18n.get(`{__k0}：{__k1}`, { __k0: fnGetFieldLabel(field), __k1: title ? title : i18n.get('无') })}
    </div>
  )
}

function renderCity(field, value, index) {
  let { label } = field
  let val = value ? JSON.parse(value) : ''
  val = val.length > 0 ? val.map(el => el.label).join(i18n.get('，')) : ''
  return (
    <div key={index} className="text translate-ignore-class">
      {i18n.get(`{__k0}：{__k1}`, { __k0: fnGetFieldLabel(field), __k1: val })}
    </div>
  )
}

function renderPayeeInfo(field, value = {}, index) {
  let { label } = field
  let { bank, branch, accountNo, staffId = {} } = value
  return (
    <div key={index} className="text dis-f">
      {i18n.get(`{__k0}：`, { __k0: fnGetFieldLabel(field) })}
      <div className="dis-f fd-c translate-ignore-class">
        <span className="text">{i18n.get(`{__k0}（{__k1}）`, { __k0: staffId.name, __k1: bank })}</span>
        <span className="text">{accountNo}</span>
        <span className="text">{branch}</span>
      </div>
    </div>
  )
}

function renderStaff(field, value, index) {
  const { label } = field
  let val = ''
  const staffMap = app.getState()['@common'].authStaffStaffMap
  if (isArray(value)) {
    val = value
      .map(item => {
        if (isObject(item)) {
          return item
        }
        return staffMap[item]
      })
      .map(line => getDisplayName(line))
      .filter(line => !!line)
      .join(',')
  } else if (isObject(value)) {
    val = getDisplayName(value)
  }
  return (
    <div key={index} className="text">
      <div className="label">{fnGetFieldLabel(field)}：</div>
      <div className="value translate-ignore-class">{val ? val : i18n.get('无')}</div>
    </div>
  )
}

function renderDataLink(field, value, index, allTemplate) {
  return (
    <div key={index} className="text translate-ignore-class">
      {buildDataLinkEditsStr(field, value, allTemplate)}
    </div>
  )
}

function isObject(obj) {
  return obj && typeof obj === 'object'
}
