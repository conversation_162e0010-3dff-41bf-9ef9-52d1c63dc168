import './flow-allow-modal.less'
import { Form, Input } from 'antd'
import { Button } from '@hose/eui'
import { OutlinedTipsClose, FilledTipsWarning } from '@hose/eui-icons'
import { findIndex, get } from 'lodash'
import React, { Fragment } from 'react'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnCompareProps } from '@ekuaibao/lib/lib/lib-util'
import EnhanceFormCreate from './enhance/enhance-form-create'
import InputSelector from './input-selector'
import { app as api } from '@ekuaibao/whispered'
import { CountersignSelector } from './countersign/countersign-selector'
import AddCreditNote from '../elements/addCreditNote'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import MoneyView from './MoneyView/MoneyView' // src/plugins/bills/layers/comment/BillCommentModal.js
const ApprovalComments = api.require('@audit/ApprovalComments')
const WarningAlert = api.require('@audit/WarningAlert')
const RiskStatus = api.invokeServiceAsLazyValue('@audit:import:RiskStatus')
const ApproveSignature = api.require('@elements/signature/ApproveSignature')
const ApprovalAmountConfig = api.require('@elements/ApprovalAmountConfig')
import { getStaffShowByConfig, isPayNode } from './utilFn'
import { parseAttachment } from '@ekuaibao/lib/lib/attachment/parseAttachment'
import qs from 'qs'
const FormItem = Form.Item
const selectReviewer = i18n.get('请选择审批人')
import MessageCenter from '@ekuaibao/messagecenter'
import { showMessage } from '@ekuaibao/show-util'
import { checkStaffHasExternalStaff } from '../plugins/bills/layers/flow/flowHelper'

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
  invoiceReview: state['@common'].powers.invoiceReview,
  budgetPower: state['@common'].powers.Budget
}))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer'
})
@EnhanceFormCreate()
export default class FlowAllowModal extends React.PureComponent {
  //单一审批
  constructor(props) {
    super(props)
    let curNode = this._getNextPlanNode(props.data.flowPlan) || {}
    let powersList = api.getState()['@common'].powers.powersList
    const hasSubmitPer = powersList.filter(v => v.powerCode == '160031' && v.state != 'expire').length ? true : false
    this.state = {
      flowPlan: props.data.flowPlan || [],
      staffs: props.data.staffs,
      curNode,
      hasSubmitPer,
      uploaderFileList: [],
      value: undefined,
      isUploading: false,
      isRequired: false, // 详情中的同意批注是否必填-判断风险超标
      riskWarningData: {},
      approvalOpinions: false, // 详情中的同意批注是否必填-判断审批流配置 同意时必填
      mustBeUsedSignature: false,
      creditNoteRequired: false,
      noteRequest: undefined,
      selectedData: [],
      approveDetail: {} // 审批布局设置
    }
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
    setTimeout(() => {
      if (curNode.type !== 'ebot' && curNode.type !== 'invoicingApplication') {
        this.fnGetApproverId(curNode).then(approverId => {
          props.form.setFieldsValue({ approverId })
        })
      }
    })
  }

  bus = new MessageCenter()

  componentDidMount() {
    api.dataLoader('@common.authStaffStaffMap').load()
    const id = get(this.props, 'data.flowId.id')
    this.props.data.isAgreeFromDetail &&
      api.invokeService('@audit:get:batch:flow:Config', { flowIds: [id] }).then(res => {
        const isRequired = get(res, 'value.commentWhenFlowHasRisk')
        const mustBeUsedSignature = get(res, 'value.mustBeUsedSignature')
        const approvalOpinions = get(res, 'value.approvalOpinions')
        this.setState({ isRequired, mustBeUsedSignature, approvalOpinions })
      })
    this.getAmountConfig()
    api.invokeService('@bills:get:flow:risk:warning', { id: id }).then(riskData => {
      this.setState({ riskWarningData: riskData })
    })
  }

  componentWillReceiveProps(nextProps) {
    let fn = fnCompareProps(this.props, nextProps)
    fn('visible', visible => {
      if (!visible) return
      let newFlowPlan = nextProps.data.flowPlan
      let curNode = this._getNextPlanNode(newFlowPlan)
      this.setState({ curNode }, () => {
        this.fnGetApproverId(curNode).then(approverId => {
          nextProps.form.setFieldsValue({ approverId })
        })
      })
    })
  }
  getAmountConfig = () => {
    const { flowId } = this.props.data
    if (flowId?.id) {
      api.invokeService('@bills:get:approve:detail', { flowIds: [flowId.id] }).then(res => {
        this.setState({ approveDetail: res?.value })
      })
    }
  }
  fnGetApproverId = curNode => {
    if (curNode.counterSigners) {
      return Promise.resolve(curNode.counterSigners || [])
    } else if (curNode.approverId) {
      const staffs = get(this.props.data, 'staffs', [])
      const approverId = get(curNode, 'approverId.id', curNode.approverId)
      let staff = staffs.find(o => o.id === approverId)
      if (curNode?.config?.crossCorpApprove) {
        staff = curNode?.config?.crossCorpApprovers.find(o => o.id === approverId)
      }
      if (staff) {
        return Promise.resolve({ key: staff.id, value: getStaffShowByConfig(staff), tag: staff })
      } else {
        return api.invokeService('@common:get:staff:by:ids', { ids: [approverId] }).then(rep => {
          if (rep.items && rep.items.length) {
            const staff = rep.items[0]
            return { key: staff.id, value: getStaffShowByConfig(staff), tag: staff }
          }
          return { key: '', value: '', tag: null }
        })
      }
    }
    return Promise.resolve({ key: '', value: '', tag: null })
  }

  _getNextPlanNode(flowPlan = {}) {
    const { resubmitMethod, nextNodeId, nodes, addNodeIds = [] } = flowPlan
    if (resubmitMethod === 'TO_REJECTOR' && nextNodeId) {
      return nodes.find(o => o.id === addNodeIds[1] || o.id === nextNodeId)
    }
    // ide:匹配当前节点在流程中的索引号
    let idx = findIndex(flowPlan.nodes, o => o.id === flowPlan.taskId)
    // 判断idx是否有效：
    if (idx + 1 >= flowPlan.nodes.length || idx < 0) {
      return null
    }
    idx++
    return this.getUnSkippedIdx(flowPlan.nodes, idx)
  }

  // 递归查询并返回节点中不是跳过状态的节点：
  getUnSkippedIdx(nodes, idx) {
    const isEbotAndHiddenInFlowApprove =
      nodes[idx] &&
      (nodes[idx].type === 'ebot' || nodes[idx].type === 'invoicingApplication') &&
      nodes[idx].ebotConfig?.hiddenNode &&
      (nodes[idx].ebotConfig?.hiddenModule || [])?.includes('flowApprove')
    if (
      nodes[idx] &&
      ((nodes[idx].skippedType !== 'NO_SKIPPED' &&
        nodes[idx].skippedType !== 'NO_ABILITY' &&
        nodes[idx].skippedType !== 'PAY_AMOUNT_IS_0') ||
        isEbotAndHiddenInFlowApprove)
    ) {
      idx++
      return this.getUnSkippedIdx(nodes, idx)
    }
    return nodes[idx] || {}
  }

  getResult() {
    const flowId = this.props.data.flowPlan.id
    const { id: nodeId, type, skippedType } = this.state.curNode
    const { validateFieldsAndScroll, resetFields } = this.props.form
    return new Promise((resolve, reject) => {
      validateFieldsAndScroll((errors, values) => {
        if (errors) {
          reject()
          return
        }
        this.bus.invoke('get:approve:show:signature').then(showAutograph => {
          this.bus.invoke('get:mention:content').then(mentionContent => {
            let data = {
              node: {
                flowId,
                id: nodeId,
                name: 'freeflow.agree'
              },
              reminder: values.reminder,
              keepItSecret: values.keepItSecret,
              ...mentionContent
            }
            data.comment = !!data?.comment ? data?.comment : i18n.get('同意')
            const { noteRequest } = this.state
            if (noteRequest) {
              data.params = {
                noteRequest: noteRequest
              }
            }

            if (skippedType !== 'NO_ABILITY' && skippedType !== 'PAY_AMOUNT_IS_0') {
              data.node.name = 'freeflow.select.approver'
              if (!['ebot', 'recalculate', 'invoicingApplication'].includes(type)) {
                if (type === 'countersign' && values.approverId.length) {
                  data.node.approverIds = values.approverId.map(v => get(v, 'signerId.id', v))
                } else {
                  data.node.approverId = values?.approverId?.key
                }
              }
            }

            if (showAutograph) {
              data.autographImageId = this.props.userInfo.staff.autograph.key
            }

            if (values.attachments) {
              data.attachments = parseAttachment(values.attachments)
            }
            resolve(data)
          })
        })
      })
    }).then(data => {
      resetFields()
      return data
    })
  }

  handleModalClose = () => {
    this.props.layer.emitCancel()
    this.props.form.resetFields()
  }

  handleModalSave = () => {
    const { validateFieldsAndScroll } = this.props.form
    validateFieldsAndScroll(errors => {
      if (errors) return
      this.bus.invoke('get:approve:signature:result').then(result => {
        if (!result) return
        const { creditNoteRequired, noteRequest = [], selectedData = [] } = this.state
        if (noteRequest?.length !== selectedData?.length) {
          showMessage.info(i18n.get('信用批注有未选择'))
          return
        }
        if (creditNoteRequired) {
          if (!noteRequest?.length) {
            showMessage.error(i18n.get('信用批注必须填写'))
            return
          }
        }
        this.props.layer.emitOk()
        setTimeout(() => {
          this.handleGoApprovalUrl()
        }, 1000)
      })
    })
  }

  // 临时授权接口增加【审批完成后跳转的地址】参数，仅pageType=form/backlogDetail时有效。
  handleGoApprovalUrl = () => {
    const urlState = qs.parse(window.location.search.slice(1))
    if (urlState && ['form', 'backlogDetail'].includes(urlState.pageType) && urlState.approvalUrl) {
      window.location.href = urlState.approvalUrl
    }
  }

  async handleSelectStaff(node, done) {
    if (node?.config?.crossCorpApprove) {
      api.open('@bills:CollaborationApproveStaff', { node, ids: [node.approverId] }).then(staff => {
        done({
          key: get(staff, 'id'),
          value: getStaffShowByConfig(staff),
          tag: staff
        })
      })
    } else {
      // const checkedKeys = node?.staff?.id ? [node.staff.id] : []
      const value = this.props.form.getFieldValue('approverId')
      let checkedKeys = value?.key ? [value.key] : []
      let limitData = node.staffIds
      if (node.type === 'carbonCopy') {
        const ccStaffIds = get(node, 'carbonCopy[0].staffIds', [])
        checkedKeys = ccStaffIds
        limitData = ccStaffIds
      }
      const data = [
        {
          type: 'department-member',
          checkIds: checkedKeys
        }
      ]
      const hasExternalStaff = await checkStaffHasExternalStaff(limitData)
      if (hasExternalStaff) {
        data.push({
          type: 'external',
          checkIds: checkedKeys
        })
      }
      api
        .open('@organizationManagement:SelectStaff', {
          title: i18n.get('选择人员'),
          multiple: false,
          staffLimitData: node.isAllStaffs ? undefined : limitData,
          data,
          fetchDataSourceAction: {
            staff: api.invokeService('@organizationManagement:get:visibility:staffs'),
            department: api.invokeService('@organizationManagement:get:visibility:departments')
          },
          notFollowExternalChargeRules: hasExternalStaff
        })
        .then(checkedList => {
          const staffData = checkedList.find(v => v.type === 'department-member')
          const externalStaffData = checkedList.find(v => v.type === 'external')
          let staffs = []
          if (staffData?.checkList?.length) {
            staffs = staffData.checkList
          }
          if (externalStaffData?.checkList?.length) {
            staffs = externalStaffData.checkList
          }
          const staff = staffs[0]
          if (staff) {
            done({
              key: staff.id,
              value: getStaffShowByConfig(staff),
              tag: staff
            })
          }
        })
    }
  }

  renderRiskTip = () => {
    const { invoiceReview, budgetPower, userInfo } = this.props
    const flowId = get(this.props.data, 'flowId') || {}
    const data = get(this.props.data, 'riskData') || {}
    const { riskWarningData, hasSubmitPer, approveDetail = {} } = this.state
    const { budgetRisk = true, expenseStandardsRisk = true, invoiceRisk = true } = approveDetail?.riskWarningCfg ?? {}
    const riskData = data.value ? data : riskWarningData
    if (!riskData.value) return null
    const riskNum = get(riskData, 'value.moneyCount')
    const riskWarning = get(riskData, 'value.riskWarning', [])
    // TODO 判断待优化
    // 兼容复核风险类型，太坑了，接口没有正确的类型，只能通过文案判断，简直醉了
    const ReviewTexts = [
      '未收到以下发票的纸质发票',
      'Paper invoices for the following invoices have not been received',
      '未收到发票的纸质发票'
    ]
    const existBudgetRisk = riskWarning.filter(v => v.type === 'budget' && v.messages.length > 0) // todo zhb  看结构，确定层级和字段名
    const existInvoiceRisk = riskWarning.filter(
      v => (v.type === 'invoiceReview' || v.isReview || ReviewTexts.includes(v.controlName)) && v.messages.length > 0
    )
    const existSubmitRisk = riskWarning.filter(v => v.type === 'submit' && v.messages.length > 0)
    const budgetNum = existBudgetRisk.length
    const InvoiceNum = existInvoiceRisk.length
    const submitNum = existSubmitRisk.length
    // 限制发票复核风险显示范围，只对报销单模板校验，显示风险提示。
    const showInvoiceReview = flowId?.formType === 'expense'
    const ownerOrsubmiter =
      userInfo?.staff?.id &&
      (userInfo.staff.id == flowId?.ownerId?.id || userInfo.staff.id == flowId?.form?.submitterId?.id)
    const invoiceReviewPermission = ownerOrsubmiter || userInfo?.permissions?.find(item => item === 'INVOICE_REVIEW')
    const budgetWaring = !!budgetNum && budgetPower && !!budgetRisk
    const expenseWaring = !!riskNum && !!expenseStandardsRisk
    const submitWaring = submitNum && hasSubmitPer
    const invoiceWaring = !!InvoiceNum && invoiceReviewPermission && invoiceReview && showInvoiceReview && !!invoiceRisk
    // 单条详情里的审批同意
    if (!budgetNum && !riskNum && !InvoiceNum && !submitNum) {
      return (
        <Fragment>
          {budgetPower && !!budgetRisk && (
            <WarningAlert status={RiskStatus.value.Success} text={i18n.get('无预算超标')} />
          )}
          {!!expenseStandardsRisk && (
            <WarningAlert status={RiskStatus.value.Success} text={i18n.get('无费用标准超标')} />
          )}
          {invoiceReviewPermission && invoiceReview && showInvoiceReview && !!invoiceRisk && (
            <>
              {flowId?.form?.reviewStatus === 'ALL' ? (
                <WarningAlert status={RiskStatus.value.Success} text={i18n.get('发票均已收到')} />
              ) : (
                <WarningAlert
                  status={RiskStatus.value.Warning}
                  text={i18n.get('未收到全部发票，请注意', { InvoiceNum })}
                />
              )}
            </>
          )}
        </Fragment>
      )
    }
    return (
      <Fragment>
        {budgetWaring || expenseWaring || submitWaring || invoiceWaring ? (
          <FilledTipsWarning className="bill-icon" />
        ) : null}
        {budgetWaring ? (
          <WarningAlert status={RiskStatus.value.Warning} text={i18n.get(`${budgetNum} 处预算风险`)} />
        ) : null}
        {expenseWaring ? (
          <WarningAlert status={RiskStatus.value.Warning} text={i18n.get(`${riskNum} 处费用标准超标`)} />
        ) : null}
        {submitWaring ? (
          <WarningAlert status={RiskStatus.value.Warning} text={i18n.get('非首次提交', { submitNum })} />
        ) : null}
        {invoiceWaring ? (
          <WarningAlert status={RiskStatus.value.Warning} text={i18n.get('未收到全部发票', { InvoiceNum })} />
        ) : null}
      </Fragment>
    )
  }

  initOtherName = () => {
    return { ebot: 'Ebot', recalculate: i18n.get('重算节点'), invoicingApplication: i18n.get('开票申请') }
  }

  handleValidator = (rule, obj, callback) => {
    const {
      curNode: { type, skippedType, counterSigners }
    } = this.state
    if (skippedType === 'NO_ABILITY' || skippedType === 'PAY_AMOUNT_IS_0') {
      callback()
    } else if (type === 'countersign') {
      if (!counterSigners.length && !get(obj, '0')) {
        callback(selectReviewer)
      } else {
        callback()
      }
    } else if (!get(obj, 'key') || !get(obj, 'value')) {
      callback(selectReviewer)
    } else {
      callback()
    }
  }
  renderApprover = () => {
    const { getFieldDecorator } = this.props.form
    const node = this.state.curNode
    const nodeName = isPayNode(node) ? node.label || node.name : node.name
    const langName = i18n.currentLocale === 'en-US' && node.enName ? node.enName : nodeName
    const label = langName || selectReviewer
    const iValue = this.initOtherName()[node.type]
    if (['ebot', 'recalculate', 'invoicingApplication'].includes(node.type)) {
      return (
        <FormItem label={label} className="trabster-group">
          <Input value={iValue} disabled={true} />
        </FormItem>
      )
    } else if (node.type === 'aiApproval') {
      return null
    } else if (node.type === 'carbonCopy') {
      const ccStaffIds = get(node, 'carbonCopy[0].staffIds', [])
      const staffMap = api.getState()['@common']?.authStaffStaffMap || {}
      const ccStaffs = ccStaffIds.map(id => staffMap[id])
      const customStaffs = ccStaffs.map(staff => { return { signer: staff } })
      const customDesc = i18n.get('抄送至{__k0}名人员', { __k0: customStaffs.length })
      const placeholder = i18n.get('匹配不到抄送人，将自动跳过本节点')
      const value = ccStaffs[0] ? {
        key: ccStaffs[0].id,
        value: getStaffShowByConfig(ccStaffs[0]),
        tag: ccStaffs[0]
      } : {}
      return (
        <FormItem label={label} className="trabster-group">
          {customStaffs.length > 1 ? (
            <CountersignSelector node={node} action={'agree'} isEditConfig={true} customStaffs={customStaffs} customDesc={customDesc} />
          ) : (
            <InputSelector
              placeholder={placeholder}
              disabled={customStaffs.length === 0}
              onSelect={this.handleSelectStaff.bind(this, node)}
              tagShow={true}
              value={value}
              closable={false}
            />
          )}
        </FormItem>
      )
    } else if (node.type === 'countersign') {
      return (
        <FormItem label={label} className="trabster-group">
          {getFieldDecorator('approverId', {
            validateFirst: true,
            rules: [{ required: true, message: selectReviewer, type: 'array' }, { validator: this.handleValidator }]
          })(<CountersignSelector node={node} action={'agree'} isEditConfig={true} />)
          }
        </FormItem>
      )
    } else {
      return (
        <FormItem label={label} className="trabster-group">
          {getFieldDecorator('approverId', {
            validateFirst: true,
            rules: [{ required: true, message: selectReviewer, type: 'object' }, { validator: this.handleValidator }]
          })(
            <InputSelector
              placeholder={selectReviewer}
              disabled={get(node.config, 'isSubmitterChoice')}
              onSelect={this.handleSelectStaff.bind(this, node)}
              tagShow={true}
            />
          )}
        </FormItem>
      )
    }
  }
  addCreditNote = data => {
    console.log('[ addCreditNote data ] >', data)
    const { creditNoteRequired, noteRequest, selectedData = [] } = data
    this.setState({ creditNoteRequired, noteRequest, selectedData })
  }
  render() {
    const {
      data: { state, flowId, flowPlan }
    } = this.props
    const {
      isUploading,
      isRequired,
      mustBeUsedSignature,
      approvalOpinions,
      curNode: { skippedType },
      approveDetail
    } = this.state
    const title = state === 'RECEIVING' ? i18n.get('确认收单并转交下一审批人') : i18n.get('审批同意')
    const text = skippedType !== 'NO_ABILITY' && skippedType !== 'PAY_AMOUNT_IS_0' ? title : i18n.get('同意该单据')
    const isCommentRequired = isRequired || approvalOpinions
    return (
      <div className="flow-allow-modal">
        <div className="modal-header">
          <div className="flex">{i18n.get(text)}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose} />
        </div>
        <div className="content">
          <div className="agree-risk-warning">{this.renderRiskTip()}</div>
          <MoneyView datas={[flowId]} approveDetail={approveDetail} />
          {window.__PLANTFORM__ !== 'MC' &&
            skippedType !== 'NO_ABILITY' &&
            skippedType !== 'PAY_AMOUNT_IS_0' &&
            this.renderApprover()}
          <div className="line"></div>
          <ApprovalComments
            form={this.props.form}
            bus={this.bus}
            required={isCommentRequired}
            type={'AGREE'}
            canSelectDP={true}
            useClipboard={true}
            showAttachment={true}
          />
          <div className="line"></div>
          <ApproveSignature mustBeUsedSignature={mustBeUsedSignature} bus={this.bus} />
          <div className="line"></div>
        </div>
        <AddCreditNote
          plan={flowPlan || flowId?.plan}
          flowId={flowId?.id}
          type={'agree'}
          onChange={this.addCreditNote}
        />
        <div className="modal-footer-flow modal-footer-start">
          <div>
            <Button category="primary" data-testid="flow-allowmodal-confirm" onClick={this.handleModalSave} disabled={isUploading}>
              {i18n.get('确定')}
            </Button>
            <Button category="secondary" data-testid="flow-allowmodal-cancel" className="btn-ml" onClick={this.handleModalClose}>
              {i18n.get('取消')}
            </Button>
          </div>
          <ApprovalAmountConfig fn={this.getAmountConfig} />
        </div>
      </div>
    )
  }
}
