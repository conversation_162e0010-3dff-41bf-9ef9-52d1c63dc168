import React, { useMemo, useRef, forwardRef, useImperativeHandle } from 'react'
import MessageCenter from '@ekuaibao/messagecenter'
import { Backlog, Action } from './types'
import { actionsMap } from './bill-actions/actions'
import { FlexableButtonGroup, getActionType, ButtonGroupItem } from '../../../elements/flexable-button-group'
import styles from './index.module.less'
import { pick } from 'lodash'
import { BillActionBaseProps } from './bill-actions/base-action'

export interface BillActionsProps extends Omit<BillActionBaseProps, 'name'> {
  /**
   * 按钮配置数组
   */
  buttons: Action[]
  /**
   * 单据详情数据
   */
  backlog: Backlog
}

export interface BillActionsRef {
  forceUpdate: () => void
}

export const BillActions = forwardRef<BillActionsRef, BillActionsProps>(({
  buttons,
  backlog,
  bus = new MessageCenter(),
  layer,
  ...otherProps
}, ref) => {
  const buttonGroupRef = useRef<{ forceUpdate: () => void }>(null)

  useImperativeHandle(ref, () => ({
    forceUpdate: () => buttonGroupRef.current?.forceUpdate()
  }), [])

  const actions = useMemo(() => {
    return buttons
      .map(button => {
        const Action = actionsMap[button.action]
        const actionName = getActionType(button as any)
        if (!Action) {
          if (button.onAction && button.name) {
            return {
              onClick: button.onAction.bind(null, { backlog, bus, layer }),
              buttonProps: {
                ...pick(button, ['category', 'theme']),
                children: button.name,
                'data-testid': `bill-action-${actionName}`
              }
            }
          }
          return null
        }
        const buttonProxy = new Proxy(button, {
          set: (target, prop, value) => {
            if (prop === 'loading' || prop === 'disabled') {
              buttonGroupRef.current?.forceUpdate()
            }
            return Reflect.set(target, prop, value)
          }
        })
        return new Action(
          {
            backlog,
            // @ts-ignore
            bus,
            layer,
            name: button.action,
            ...otherProps,
          },
          Object.assign(
            buttonProxy,
            {
              children: i18n.get(button.name),
              title: button.describe,
              'data-testid': `bill-action-${actionName}`
            }
          ),
        )
      })
      .filter(Boolean)
  }, [buttons, backlog, bus, layer, otherProps])

  return (
    <div className={styles['bill-details-footer-actions']}>
      <FlexableButtonGroup ref={buttonGroupRef} buttons={actions} />
    </div>
  )
})