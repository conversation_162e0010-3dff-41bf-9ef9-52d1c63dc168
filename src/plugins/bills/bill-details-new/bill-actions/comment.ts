import { BillActionBase, BillActionBaseProps } from './base-action'
import { app } from '@ekuaibao/whispered'
import { action } from './constant'
import { get } from 'lodash'

export class CommentAction extends BillActionBase<BillActionBaseProps> {
  action = action.commnet
  label = i18n.get('评论')

  onAction = () => {
    return this.handleComment()
  }

  handleComment = () => {
    const { backlog, privilegeId, noCheckPermissions, } = this.props
    app.close()
    let dataSource = backlog.flowId
    return new Promise((resolve, reject) => {
      app.open('@bills:BillCommentModal', { flow: backlog.flowId, privilegeId }).then((params: any) => {
        const paramsValue = noCheckPermissions ? { ...params, checkPermissions: false } : params
        let args = { params: paramsValue, id: backlog.flowId.id }
        // @ts-ignore
        args.privilegeId = privilegeId
        app.invokeService('@common:insert:assist:record', {
          title: '评论' + get(dataSource, 'form.title') + '单据'
        })
        app.invokeService('@bills:comment:flow', args).then(() => {
          this.forceUpdateFlow()
          resolve(true)
        }).catch(e => {
          reject(e)
        })
      }).catch(e => {
        reject(e)
      })
    })
  }

}
