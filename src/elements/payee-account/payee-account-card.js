/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/12.
 */

import './payee-account-card.less'
import React, { PureComponent } from 'react'
import { Dropdown, Icon, Menu, Switch } from 'antd'
import { Tooltip } from "@hose/eui"
import EkbHighLighter from '../EkbHighLighter'
import WALLET_ICON from '../../images/home-myLoan.svg'
import EKBIcon from '../../elements/ekbIcon'
import { showMessage } from '@ekuaibao/show-util'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import get from 'lodash/get'
import { EnhanceConnect } from '@ekuaibao/store'
import { getDisplayName } from '../utilFn'
import { concat } from 'lodash'

const typeEnum = { PUBLIC: i18n.get('对公账户'), PERSONAL: i18n.get('个人账户') }
const CARDNUM_SEPARATOR = i18n.get('　')

/*
 *.	active:true
 .	bank:"平安银行"
 .	branch:"平安银行股份有限公司北京分行"
 .	cardNo:"********"
 .	city:"北京市"
 .	corporationId:"6qc5GCGEqs0000"
 .	icon:"https://images.ekuaibao.com/bank/bank-pingan.svg"
 .	id:"w8g5KWfSrM6M00"
 .	isDefault:true
 .	name:"sfsdf"
 .	province:"北京市"
 .	staffId:"6qc5GCGEqs0000:****************"
 .	type:"PERSONAL"
 * */

function fnCardNumber(number, index = 4) {
  if (/[^0-9]/.test(number)) {
    return number
  }
  let k = Math.floor(number.length / index)
  let arr = []
  for (let i = 0; i < k + 1; i++) {
    arr.push(number.slice(index * i, index * (i + 1)))
  }

  return arr.join(CARDNUM_SEPARATOR)
}

function cardNumberSearchTexts(searchTest) {
  if (/[^0-9]/.test(searchTest)) {
    return [searchTest]
  }
  let result = []
  for (let i = 0; i < Math.min(searchTest.length, 4); i++) {
    let arr = []
    arr.push(searchTest.slice(0, i + 1))
    if (searchTest.length > i + 1) {
      arr.push(fnCardNumber(searchTest.slice(i + 1, searchTest.length)))
    }

    result.push(arr.join(CARDNUM_SEPARATOR))
  }

  return result
}

@EnhanceConnect(state => ({
  payeeConfig: state['@common'].payeeConfig,
  allCurrencyRates: state['@common'].getCurrencyAll,
  standardCurrency: state['@common'].standardCurrency
}))
export default class PayeeAccountCard extends PureComponent {
  static defaultProps = {
    name: '',
    accountNo: '',
    branch: '',
    bank: '',
    icon: '',
    owner: '',
    type: '',
    isDefault: false,
    state: '',
    active: false,
    dynamicCard: 'readonly',
    className: null,
    searchText: '',
    canShare: false,
    isSelf: false,
    fnActiveCheck: () => { },
    fnEditAccount() { },
    fnSetDefault() { },
    fnShareAccount() { },
    fnChangeLog() { },
    fnShareSelectAccount() { },
    multiplePayeesMode: false
  }

  constructor(props) {
    super(props)
    this.handleActiveCheck = this.handleActiveCheck.bind(this)
  }

  fnActionsMapper(card) {
    let actions = [
      {
        key: 'setDefault',
        label: i18n.get('设为默认'),
        scope: ['readonly', 'edit-read'],
        fn: card.fnSetDefault,
        conditions: { readonly: !card.isDefault, 'edit-read': !card.isDefault }
      },
      {
        key: 'editAccount',
        label: i18n.get('编辑账户'),
        scope: ['editable', 'edit-read'],
        fn: card.fnEditAccount,
        conditions: { editable: true, 'edit-read': card.isSelf }
      },
      {
        key: 'changeLog',
        label: i18n.get('变更记录'),
        scope: ['editable', 'edit-read'],
        fn: card.fnChangeLog,
        conditions: { editable: true, 'edit-read': true }
      }
    ]

    let result = actions.filter(o => {
      return o.scope.filter(e => e === card.dynamicCard).length && o.conditions[card.dynamicCard]
    })
    if (card.sort === 'WALLET') {
      result = result.filter(o => o.key === 'setDefault')
    }

    return result
  }

  handleActiveCheck(value, card) {
    this.props.fnActiveCheck(value, card)
  }

  renderActions(card) {
    let actionList = this.fnActionsMapper(card)
    let isShowActive = card.dynamicCard === 'editable' || (card.dynamicCard === 'edit-read' && card.isSelf)

    let ActiveComponent = props => {
      let { card } = props
      if (card.dynamicCard === 'readonly') {
        return null
      }

      return (
        <div
          onClick={e => {
            e.stopPropagation()
            e.preventDefault()
          }}
        >
          <span className="segment-line-vertical" />
          <span className={!card.active ? 'color-gray action-wrapper' : 'fs-12 color-blue action-wrapper'}>
            {card.active ? i18n.get('启用中') : i18n.get('已停用')}
            <Switch
              className="enable-switch"
              checked={card.active}
              onChange={this.handleActiveCheck.bind(this, card.active, card)}
            />
          </span>
        </div>
      )
    }

    switch (actionList.length) {
      case 0:
        return card.sort !== 'WALLET' && <ActiveComponent card={card} />
      case 1:
        return (
          <div className="ta-r">
            <span
              onClick={e => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              <a onClick={actionList[0].fn}>{actionList[0].label}</a>
            </span>
            {isShowActive && card.sort !== 'WALLET' && <ActiveComponent card={card} />}
          </div>
        )
      default:
        if (isShowActive) {
          return (
            <div className="dis-f">
              <Dropdown
                onClick={e => {
                  e.stopPropagation()
                  e.preventDefault()
                }}
                overlay={this.renderMenu(actionList)}
              >
                <span className="fs-12 color-blue cur-p">
                  {i18n.get('更多操作')}
                  <Icon type="down" />
                </span>
              </Dropdown>
              {isShowActive && card.sort !== 'WALLET' && <ActiveComponent card={card} />}
            </div>
          )
        } else {
          let ops = actionList.map(o => {
            return (
              <div
                key={o.key}
                onClick={e => {
                  e.stopPropagation()
                  e.preventDefault()
                }}
                className="dis-f op-line-wrapper"
              >
                <span>
                  <a onClick={o.fn}>{o.label}</a>
                </span>
                <span className="segment-line-vertical" />
              </div>
            )
          })
          return <div className="ops-row-wrap">{ops}</div>
        }
    }
  }

  renderMenu = actionList => {
    let actionFn = e => {
      e.domEvent.stopPropagation()
      e.domEvent.preventDefault()
      actionList.filter(o => o.key === e.key)[0]['fn']()
    }
    return (
      <Menu onClick={actionFn}>
        {actionList.map(o => {
          return <Menu.Item key={o.key}>{i18n.get(o.label)}</Menu.Item>
        })}
      </Menu>
    )
  }

  renderSharedWrapper(card) {
    let actionList = [
      {
        key: 'shareAccount',
        label: card.sharedLabel,
        fn: card.fnShareAccount
      },
      {
        key: 'cancelShare',
        label: i18n.get('部分可见'),
        fn: card.fnShareSelectAccount
      }
    ]

    return (
      <div className="share-actions-wrapper">
        <Dropdown
          onClick={e => {
            e.stopPropagation()
            e.preventDefault()
          }}
          overlay={this.renderMenu(actionList)}
          trigger={['click']}
        >
          {/* @i18n-ignore */}
          {card.sharedLabel !== i18n.get('取消全员可见') && (card.sharedInfo || card.isSelf) ? (
            <Tooltip placement="top" title={card.sharedInfo}>
              <span className="fs-12 color-blue cur-p">
                {i18n.get('部分可见')}
                <Icon type="down" />
              </span>
            </Tooltip>
          ) : (
            <span className="fs-12 color-blue cur-p">
              {/* // @i18n-ignore */}
              {card.sharedLabel === i18n.get('取消全员可见') ? i18n.get('全员可见') : i18n.get('部分可见')}
              <Icon type="down" />
            </span>
          )}
        </Dropdown>
        <span className="segment-line-vertical" />
      </div>
    )
  }

  renderShouldComplementInfo = () => (
    <div className="complement-info">
      <EKBIcon name="#EDico-plaint-circle" className="warning-icon" />
      {i18n.get('待补充')}
    </div>
  )

  handleClick = () => {
    const { onClick } = this.props
    if (typeof this.props.handleComplementInfo === 'function') {
      this.props.handleComplementInfo()
    }
    if (onClick) {
      onClick()
    }
  }

  renderCopyBtn = value => {
    const textData = value.replace(/\s/g, '')
    return (
      <CopyToClipboard text={textData}>
        <div type="primary" onClick={this.handleCopyBtnClick} style={{ cursor: 'pointer' }}>
          {i18n.get('复制')}
        </div>
      </CopyToClipboard>
    )
  }

  handleCopyBtnClick = e => {
    e.stopPropagation()
    e.preventDefault()
    showMessage.success(i18n.get('复制成功！'))
  }

  renderIconObj = (card) => {
    if (card.sort === 'WALLET') {
      return { icon: WALLET_ICON, bank: i18n.get('钱包') }
    } else if (card.sort === 'ALIPAY') {
      const icon = <EKBIcon className="mr-5 stand-20-icon" name="#EDico-zhifubao" />
      return { icon, bank: i18n.get('支付宝') }
    } else {
      const icon = card.unionBank ? card.unionIcon : card.icon
      const bank = card.unionBank ? card.unionBank : card.bank
      return { icon, bank }
    }
  }

  renderInternationBank() {
    let { icon, active, id, swiftCode, bankName } = this.props
    const wrapperClass = active ? 'international-bank-wrap' : 'international-bank-wrap international-bank-inactive'
    return (<div className="account-adr-line-style">
      <div className="bank-icon-wrapper mr-5">
        <img src={icon} width={20} height={20} />
      </div>
      <div className={wrapperClass}>
        {!!swiftCode && <div className="international-bank-code">{swiftCode}</div>}
        <Tooltip key={id} placement="topLeft" title={bankName}>
          <div className='bank-info-style'>{bankName}</div>
        </Tooltip>
      </div>
    </div>)
  }

  renderBank() {
    let { ...card } = this.props
    let { sort, unionBank } = card
    if (sort === 'OVERSEABANK') return this.renderInternationBank()
    const iconObj = this.renderIconObj(card)
    return (<div className="account-adr-line-style translate-ignore-class">
      <div className="bank-icon-wrapper mr-5">
        {card.sort !== 'ALIPAY' ? <img src={iconObj.icon} width={20} height={20} /> : iconObj.icon}
      </div>
      <div className={card.active ? 'fs-12 color-gray-8 flex-s-0'
        : 'fs-12 color-gray-7 flex-s-0'}>{iconObj.bank}</div>

      {!unionBank && sort === 'BANK' && <span className="segment-line-vertical" />}
      {!unionBank && (
        <Tooltip key={card.id} placement="topLeft" title={card.branch}>
          <div className={card.active ? 'bank-info-style' : 'fs-12 color-gray-7'}>{card.branch}</div>
        </Tooltip>
      )}
    </div>)
  }

  render() {
    let { hiddenActions, payeeConfig, allCurrencyRates, standardCurrency, ...card } = this.props
    let { sort, style = {}, receivingCurrencyNumCode } = card
    if (!sort) {
      return null
    }
    let accountNo = fnCardNumber(card.accountNo || card.cardNo)
    let staffname = getDisplayName(card?.staffId)
    const _staffname = staffname?.length > 5 ? staffname?.substr(0, 5) + '...' : staffname
    let owner = card.owner === 'INDIVIDUAL' ? _staffname : i18n.get('企业')
    const remarkDisplay =
      card.type === 'PERSONAL'
        ? payeeConfig?.personalAccountConfig?.remarkDisplay
        : payeeConfig?.publicAccountConfig?.remarkDisplay
    const remark = card?.remark || ''
    const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
    const showRemark = remark && remarkDisplay
    if (receivingCurrencyNumCode) {
      const allCurrency = concat(standardCurrency, allCurrencyRates)
      card.receivingCurrency = allCurrency.find(item => item.numCode === receivingCurrencyNumCode) || {}
    }
    return (
      <div className={`payee-account-card ${card.className}`} style={style} onClick={this.handleClick}>
        <div className="title-line-style">
          <Tooltip key={card.id} placement="topLeft" title={card.accountName || card.name}>
            <span
              className={
                card.active ? 'title-line-text fs-14 color-gray-9' : 'title-line-text fs-14 color-gray-7 mr-8'
              }
            >
              <EkbHighLighter
                highlightClassName="highlight"
                searchWords={[card.searchText]}
                textToHighlight={card.accountName || card.name}
              />
            </span>
          </Tooltip>
          <div
            className={
              card.active
                ? 'account-type-wrapper account-type-blue-border ml-8'
                : 'account-type-wrapper account-type-gray-border ml-8'
            }
          >
            <span className={card.active ? 'fs-11 color-blue2' : 'fs-11 color-gray-7'}>
              {i18n.get(typeEnum[card.type] || i18n.get('个人账户'))}
            </span>
          </div>
          {card.dynamicCard !== 'readonly' && card.state === 'SHARED' && (
            <div
              className={
                card.active
                  ? 'account-type-wrapper account-type-blue2-border ml-8'
                  : 'account-type-wrapper account-type-gray-border ml-8'
              }
            >
              <span className={card.active ? 'fs-11 color-blue2' : 'fs-11 color-gray-7'}>{i18n.get('共享')}</span>
            </div>
          )}
          {showRemark && <span className="account-remark ml-8">{remark?.length < 6 ? remark : <Tooltip title={remark}>{_remark}</Tooltip>}</span>}
          {receivingCurrencyNumCode && <span className='receiving-currency'>{card.receivingCurrency.strCode}{i18n.get(card.receivingCurrency.name)}</span>}
        </div>
        <div className="card-content">
          <div className="card-left-wrapper">
            <Tooltip placement="topLeft" title={this.renderCopyBtn.bind(this, accountNo)}>
              <div className="account-number-line-style">
                <span className={card.active ? 'account-number-style color-gray-9' : 'account-number-style color-gray-7'}>
                  <EkbHighLighter
                    highlightClassName="highlight"
                    searchWords={cardNumberSearchTexts(card.searchText)}
                    textToHighlight={accountNo}
                  />
                </span>
              </div>
            </Tooltip>
            {this.renderBank()}
          </div>
          <div className="card-right-wrapper">
            <div className="op-wrapper">
              {this.props.multiplePayeesMode && this.renderShouldComplementInfo()}
              {card.canShare && this.renderSharedWrapper(card)}
              {!hiddenActions && this.renderActions(card)}
            </div>
            <div className="owner-line">
              {owner ? (
                <span className="owner-text">
                  {i18n.get('所有者')}
                  {i18n.get('：')}
                </span>
              ) : null}
              {owner ? (
                <Tooltip placement="topRight" title={staffname}>
                  <span className="owner-text translate-ignore-class">
                    <EkbHighLighter
                      highlightClassName="highlight"
                      searchWords={[card.searchText]}
                      textToHighlight={owner}
                    />
                  </span>
                </Tooltip>
              ) : null}
              {card.dynamicCard !== 'editable' && card.isDefault && (
                <div className="default-wrapper ml-15">
                  <span className="default-text-style">{i18n.get('默认')}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }
}
