/**
 *  Created by pw on 2019-03-19 22:00.
 */
import { MoneyIF } from '@ekuaibao/ekuaibao_types'
import { ListView, RefetchParams } from '@ekuaibao/eui-isomorphic'
import { EnhanceConnect } from '@ekuaibao/store'
import { app } from '@ekuaibao/whispered'
import { OutlinedDirectionDown } from '@hose/eui-icons'
import { Alert, Menu, Dropdown, Spin } from 'antd'
import { ClickParam } from 'antd/es/menu'
import { get, set } from 'lodash'
import React, { PureComponent, useEffect, useState } from 'react'
import Money from '../../../../elements/puppet/Money'
import Currency from '../../../../elements/puppet/Currency'
import CardListView from '../../../../elements/InvoiceCard/InvoiceItem'
import { handleBillDetailClickById, handleShowDetail } from '../../bill-stacker-manager/util'
import {
  getChildrenPrimary,
  getFlowInfoById,
  getLedgerRelationEntityList,
  getLedgerRelationList,
  getLedgerRelationListAmount
} from '../../bills.action'
import ListTitle from '../mine-datalink-list/ListTitle'
import {
  LedgerEntityListItem,
  LedgerInterface,
  LedgerListItemInterface,
  LedgerListViewInterface,
  StatisticsSourceEnum
} from './LedgerRelationInterface'
import { formatResult } from './ledgerUtil'
const Empty_PIC = app.require<any>('@elements/transfer/images/empty.svg')
import { getBoolVariation } from '../../../../lib/featbit'

type Sort = 'DESC' | 'ASC'

interface FilterData {
  sort: Sort
  childrenId: string
}

interface State {
  count: number
  items: any[]
  filterData: FilterData
  refreshed: boolean
  originData: any[]
  listData: any[]
}

interface Props {
  entityInfo: any
  baseDataPropertiesMap?: any
  keel: any
  currentLedger: LedgerInterface
  isModal?: boolean
}

const defaultAmount: MoneyIF = {
  standard: '0.00',
  standardNumCode: '156',
  standardScale: '2',
  standardStrCode: 'CNY',
  standardSymbol: '¥',
  standardUnit: i18n.get('元')
}

@EnhanceConnect(
  (state: any) => ({
    baseDataPropertiesMap: state['@common'].globalFields.baseDataPropertiesMap
  }),
  { getFlowInfoById }
)
export default class LedgerRelationTabPanne extends PureComponent<Props, State> {
  listInfo = {
    count: 0,
    total: undefined,
    filterCount: 0,
    filterTotal: undefined
  }
  constructor(props: Props) {
    super(props)
    this.state = {
      originData: [],
      count: 0,
      items: undefined,
      listData: [],
      refreshed: false,
      filterData: {
        sort: 'ASC',
        childrenId: 'all'
      }
    }
  }

  handleClick = (line: LedgerListItemInterface | LedgerEntityListItem) => {
    const { currentLedger, isModal } = this.props
    if (currentLedger.statisticsSource === StatisticsSourceEnum.MASTER) {
      const params = { id: (line as LedgerListItemInterface).flowId, flowId: (line as LedgerListItemInterface).flowId }
      handleBillDetailClickById.call(this, params, isModal, undefined, undefined, undefined, false)
    } else if (currentLedger.statisticsSource === StatisticsSourceEnum.DATA_LINK) {
      const { item, template } = line as LedgerEntityListItem
      return app.open('@bills:DataLinkDetailModal', {
        entityInfo: { ...item, dataLink: { id: item.dataLink.id } },
        showClose: true,
        viewKey: 'DataLinkDetailModal'
      })
    } else {
      const params = { id: (line as LedgerListItemInterface).flowId, flowId: (line as LedgerListItemInterface).flowId }
      return handleShowDetail.call(this, {
        requestParam: params,
        detailId: (line as LedgerListItemInterface).detailId,
        needPosition: false,
        suppleInvoiceBtn: true,
        flowId: (line as LedgerListItemInterface).flowId,
        isOnlyShowCurrentIdx: true,
        showAllFeeType: true
      })
    }
  }

  refetch = async (props?: RefetchParams): Promise<any> => {
    const statisticsSource = get(this.props, 'currentLedger.statisticsSource')
    const id = get(this.props, 'currentLedger.id')
    const dataLinkId = get(this.props, 'entityInfo.dataLink.id')
    const { startIndex = 0, offset = 100 } = props || {}
    const { filterData } = this.state
    const params = {
      ledgerConfigId: id,
      dataLinkId,
      statisticsSource,
      start: startIndex,
      count: offset,
      ...filterData
    }
    let count = 0
    let data = []
    let totalAmount = defaultAmount
    if (statisticsSource === StatisticsSourceEnum.DATA_LINK) {
      const { totalCount, dataSource, amount } = await this.getRelationEntityData(params)
      count = totalCount
      data = dataSource
      totalAmount = amount
    } else {
      const { totalCount, dataSource, amount } = await this.getMasterAndDetailsData(params)
      if (statisticsSource === StatisticsSourceEnum.INVOICE) {
        const invoicelds = dataSource?.map(item => item.invoiceId)
        if (dataSource?.length > 0) {
          const res = await app.invokeService('@bills:get:invoice:info:by:ids', invoicelds)
          this.setState({ originData: res })
        }
      }
      count = totalCount
      data = dataSource
      totalAmount = amount
    }
    if (filterData.childrenId === 'all') {
      this.listInfo.count = count
      this.listInfo.total = totalAmount
    } else {
      this.listInfo.filterCount = count
      this.listInfo.filterTotal = totalAmount
    }
    this.setState({
      count: count,
      listData: data,
      refreshed: true
    })
    return {
      items: data,
      count
    }
  }

  getRelationEntityData = async (params): Promise<any> => {
    const result = await Promise.all([getLedgerRelationEntityList(params), getLedgerRelationListAmount(params)])
    const res = result[0]
    const amount = result[1]
    const { baseDataPropertiesMap } = this.props
    const { template, data, total } = res
    const dataSource = formatResult([{ items: data, template }], params.statisticsSource, baseDataPropertiesMap)
    return { totalCount: total, dataSource, amount }
  }

  getMasterAndDetailsData = async (params): Promise<any> => {
    const result = await Promise.all([getLedgerRelationList(params), getLedgerRelationListAmount(params)])
    const res = result[0]
    const amount = result[1]
    const { baseDataPropertiesMap } = this.props
    const { items, count } = res
    const dataSource = formatResult(items, params.statisticsSource, baseDataPropertiesMap)
    return { totalCount: count, dataSource, amount }
  }

  renderItem = (props: any) => {
    const statisticsSource = get(this.props, 'currentLedger.statisticsSource')
    if (statisticsSource === StatisticsSourceEnum.DATA_LINK) {
      return this.renderEntityItem(props)
    }
    if (statisticsSource === StatisticsSourceEnum.INVOICE) {
      return this.renderInvoiceItem(props)
    }
    return this.renderMasterAndDetailsItem(props)
  }

  // 渲染发票
  renderInvoiceItem = (props: any) => {
    const { key, status, style, data } = props
    const { originData } = this.state

    // originData找到对应发票的发票
    const currentInvoice = originData?.find(item => item?.master?.id === data?.invoiceId)

    const cloneInvoice = JSON.parse(JSON.stringify(currentInvoice))

    if (data?.amount) {
      set(cloneInvoice, 'sourceAmount', data?.amount)
    }

    if (status === 1) {
      return (
        <div key={key}
          className='render-invoice-item-wrapper'
          onClick={(e) => {
            e.stopPropagation()
            app.open('@bills:InvoiceDetailModal', {
              title: '预览详情',
              dataSource: [cloneInvoice].filter(Boolean) || [],
              isEdit: false,
              isReadOnlyPage: false
            })
          }}>
          <CardListView
            dataSource={[cloneInvoice].filter(Boolean) || []}
            showInfoBtn={false}
            isEdit={true}
            isReadOnlyPage={false}
          />
        </div>
      )
    }
    return this.renderSpin(style, key)
  }
  renderEntityItem = (props: any) => {
    const { data, key, status, style } = props
    const item = get(data, 'item')
    const template = get(data, 'template')
    if (!template) return null
    if (status === 1) {
      return (
        <div
          key={key}
          className="ledger_relation_entity_item_wrapper"
          style={style}
          onClick={() => this.handleClick(data)}
        >
          <ListTitle data={item} template={template} />
        </div>
      )
    }
    return this.renderSpin(style, key)
  }

  renderMasterAndDetailsItem = (props: any) => {
    const { data, key, status, style } = props
    if (status === 1) {
      return <LedgerListItem key={key} onClick={this.handleClick} item={data} style={style} />
    }
    return this.renderSpin(style, key)
  }

  renderSpin = (style, key) => {
    return (
      <div key={key} style={style} className="horizontal">
        <Spin />
      </div>
    )
  }

  filterDataChange = (data: FilterData) => {
    this.setState({ filterData: data })
  }

  calculateRowHeight = index => {
    const statisticsSource = get(this.props, 'currentLedger.statisticsSource')
    if (statisticsSource === StatisticsSourceEnum.INVOICE) {
      const { originData, listData } = this.state
      const currentInvoice = originData?.find(item => item?.master?.id === listData[index]?.invoiceId)

      const cloneInvoice = JSON.parse(JSON.stringify(currentInvoice))
      if (!cloneInvoice) {
        return 0
      }

      const key = cloneInvoice?.master?.entityId
      const mapItem = {
        system_发票主体: 115,
        system_非税收入类票据: 105,
        system_医疗发票: 105,
        system_出租车票: 105,
        system_过路费发票: 105,
        system_定额发票: 100,
        system_火车票: 105,
        system_航空运输电子客票行程单: 105,
        system_客运汽车发票: 105,
        system_其他: 105,
        system_消费小票: 105,
        system_机打发票: 126,
        invoicePhoto: 105
      }
      return mapItem[key] ?? 105
    }

    if (statisticsSource === StatisticsSourceEnum.DATA_LINK) {
      return 105
    }

    return 68
  }

  render() {
    const { entityInfo, currentLedger, isModal } = this.props
    const { count, filterData, refreshed } = this.state
    const isEmpty = refreshed && !count
    const dataLinkId = get(entityInfo, 'dataLink.id')
    const statisticsSource = get(currentLedger, 'statisticsSource')
    const cls = isModal ? 'ledger_relation_list_tab_list' : 'ledger_relation_list_tab_list_height'
    let countHeight = 20

    for (var i = 0; i < count; i++) {
      const rowHeight = this.calculateRowHeight(i)
      countHeight += rowHeight
    }

    return (
      <div className={'pl-24 pr-24'}>
        {currentLedger.isCalculation ? (
          <Alert
            className="ledger_relation_warning"
            message={i18n.get('管理员修改了待开发票金额的统计方式，系统正在更新数据中，当前的金额可能会与实际有所偏差')}
            type="warning"
            showIcon
          />
        ) : null}
        <BillHeader
          title={i18n.get('统计明细')}
          currentLedger={currentLedger}
          dataLinkId={dataLinkId}
          filterData={filterData}
          filterDataChange={this.filterDataChange}
          data={this.listInfo}
          isEmpty={isEmpty}
        />
        <div className={cls} id={'ledger_relation_list_height'} style={{ minHeight: '150px', height: countHeight }}>
          <ListView
            rowHeight={({ index }) => this.calculateRowHeight(index)}
            noRows={<EmptyView />}
            refetch={this.refetch}
            threshold={count}
            renderItem={this.renderItem}
            key={`${currentLedger.id}-${filterData.childrenId}-${filterData.sort}-${statisticsSource}`}
          />
        </div>
      </div>
    )
  }
}

const EmptyView = () => {
  return (
    <div className="ledger_relation_empty_wrapper">
      <img src={Empty_PIC} alt={i18n.get('费用类型为空')} />
      <span>{i18n.get('无统计明细')}</span>
    </div>
  )
}

interface HeaderInterface {
  title: string
  currentLedger: LedgerInterface
  dataLinkId: string
  filterData: FilterData
  filterDataChange: (data: FilterData) => void
  data?: {
    count: number
    total: MoneyIF
    filterCount: number
    filterTotal: MoneyIF
  }
  isEmpty: boolean
}

interface ChildDimension {
  id: string
  name: string
  active: boolean
}

const BillHeader: React.FunctionComponent<HeaderInterface> = props => {
  const defaultOption = [{ name: i18n.get('全部明细'), id: 'all' }]
  const { title, data, dataLinkId, filterData, filterDataChange, isEmpty, currentLedger } = props
  const [checkedValue, setCheckedValue] = useState(filterData.childrenId)
  const [sort, setSort] = useState(filterData.sort)
  const [options, setOptions] = useState(defaultOption)

  useEffect(() => {
    const params = { dataLinkId, ledgerConfigId: currentLedger.id }
    getChildrenPrimary(params).then((result: ChildDimension[]) => {
      setOptions([...defaultOption, ...result])
    })
  }, [dataLinkId, currentLedger.id])

  const menuClick = (item: ClickParam) => {
    setCheckedValue(item.key)
    filterDataChange({ ...filterData, childrenId: item.key })
  }

  const handleSortClick = () => {
    const s = sort === 'ASC' ? 'DESC' : 'ASC'
    setSort(s)
    filterDataChange({ ...filterData, sort: s })
  }

  const menus = (
    <Menu onClick={menuClick}>
      {options.map(item => {
        return <Menu.Item key={item.id}>{item.name}</Menu.Item>
      })}
    </Menu>
  )

  const amount = (value: any) => {
    return currentLedger.fieldType === 'MONEY' ? (
      <Money value={value || defaultAmount} valueSize={14} symbolSize={14} />
    ) : (
      <div>
        {Number(value) || 0}
        {currentLedger?.unit}
      </div>
    )
  }

  const checkedItem = options.find(item => item.id === checkedValue)
  const dropdownDisable = options.length <= 1

  return (
    <div className="ledger_relation_bill_header">
      <div className="title-content">
        <div className="title_wrapper">
          <span className={'title_wrapper-title'}>{title}</span>
          {!isEmpty && (
            <div className="subTitle">
              {checkedItem.id !== 'all' && (
                <span className="desc">{i18n.get('筛选出 {__count} 条明细', { __count: data.filterCount })}</span>
              )}
              <span className="desc">{i18n.get('总共 {__count} 条明细', { __count: data.count })}</span>
            </div>
          )}
        </div>
        <div className="action_wrapper">
          <Dropdown overlay={menus} disabled={dropdownDisable}>
            <span className={dropdownDisable ? 'action_disable' : ''}>
              {checkedItem.name}
              <OutlinedDirectionDown className="ml-4" fontSize={16} />
            </span>
          </Dropdown>
          <span onClick={handleSortClick}>
            {sort === 'ASC' ? i18n.get('按最近提交日期降序') : i18n.get('按最近提交日期升序')}
          </span>
        </div>
      </div>
    </div>
  )
}

function LedgerListItem(props: LedgerListViewInterface) {
  const { onClick, item, style } = props
  return (
    <div className="ledger_relation_detail_list" style={style} onClick={() => onClick(item)}>
      <div className="ledger_relation_detail_list_left">
        {item.icon && <img className="img-view" style={{ background: item.background }} src={item.icon} />}
        <div className="center-view">
          <span className="detail-name">
            {item.title}
            {item.active ? '' : i18n.get('(已停用)')}
          </span>
          {item.desc && <span className="r2 mt4">{item.desc}</span>}
        </div>
      </div>
      <div className="ledger_relation_detail_list_right">
        {item.fieldType === 'MONEY' ? (
          <Currency
            value={item.amount}
            apportionTotal={item.fromApporation ? item.originalApportionAmount : undefined}
            style={{ textAlign: 'right', display: 'inline-flex', flexDirection: 'column' }}
            styleTwo={{ fontSize: '14px' }}
          />
        ) : (
          <div>
            {item.fromApporation ? (
              <div className="center-view">
                <span className="r1">{`实际统计数值：${item.totalNum || 0} ${item?.unit}`}</span>
                <span className="r2 right mt4">{`原始数值：${item.originalApportionTotalNum || 0} ${item?.unit}`}</span>
              </div>
            ) : (
              <span>{`${item.totalNum || 0} ${item?.unit}`}</span>
            )}
          </div>
        )}
        {!item.fromApporation && <div className="right_desc">{
          getBoolVariation('aprd-5665-datalink') && item.manualApporationLabel ? item.manualApporationLabel :  item.sumFieldName
        }</div>}
      </div>
    </div>
  )
}
