import React, { useEffect, useMemo, useState } from 'react'
import { app } from '@ekuaibao/whispered'
import { SkeletonButton } from '../parts/right-part/billInfo/SkeletonModal'
import './BillInfoButtons.less'
import { Fetch } from '@ekuaibao/fetch'
import { showMessage } from '@ekuaibao/show-util'
import { getBoolVariation } from '../../../lib/featbit'
import { Button, Dropdown, Modal } from '@hose/eui'
import { FilledTipsWarning } from '@hose/eui-icons'
import { FlexableButtonGroup } from '../../../elements/flexable-button-group/FlexableButtonGroup'
import MessageCenter from '@ekuaibao/messagecenter'

export interface IButton {
  id: string
  name: string
  enName: string
  action: string
  category: 'primary' | 'secondary' | 'ghost' | 'dashed' | 'text'
  theme: 'default' | 'highlight' | 'danger'
  desc: string
  type: 'system' | 'hab' | 'customize'
  onClick: () => void
  customData?: Record<string, any>
}

export interface ICustomButtonResult {
  result: Record<string, any>
  success: boolean
  message: string
}

interface IProps {
  children?: JSX.Element
  flowId: string // 单据ID
  scene: 'OWNER' | 'APPROVER' | 'ADMIN' // 场景类型 制单人 (我的单据)：OWNER；审批人 (待办)：APPROVER；管理员 (单据管理)：ADMIN；
  privilegeId?: string // 权限ID
  needConfigButton?: boolean // 是否需要使用后台配置的按钮
  flowActionMap: Record<string, { onClick: () => void }>
  bus?: MessageCenter
  forceUpdateFlow?: () => void
}

const DEFAULT_SHOW_BUTTON_COUNT = 5

const BillInfoButtons = (props: IProps) => {
  const { children, flowId, scene = 'OWNER', privilegeId, needConfigButton = false, flowActionMap = {}, bus, forceUpdateFlow } = props
  const [buttons, setButtons] = useState<IButton[]>([])
  const [isLoading, setLoading] = useState<boolean>(false)
  // 使用前端默认按钮， 默认false，后端计算后真实的没有返回true，
  const [useDefault, setUseDefault] = useState(false)
  const [buttonLoadingMap, setButtonLoadingMap] = useState<Record<string, boolean>>({})
  // 使用featBit开关，默认false
  const customExtendButton = getBoolVariation('custom-extend-button', false)

  useEffect(() => {
    if (needConfigButton && customExtendButton && flowId?.length) {
      // 请求后台接口获取按钮列表
      refreshButton()
    }
  }, [flowId, scene, privilegeId])

  const fnGetButtonName = (button: IButton) => {
    return Fetch.lang === 'zh-CN' ? button.name : button.enName
  }

  const showButtons = useMemo(() => {
    if (buttons.length > DEFAULT_SHOW_BUTTON_COUNT) {
      return buttons.slice(0, DEFAULT_SHOW_BUTTON_COUNT)
    }
    return buttons
  }, [buttons])

  const moreButtons = useMemo(() => {
    if (buttons.length > DEFAULT_SHOW_BUTTON_COUNT) {
      return buttons.slice(DEFAULT_SHOW_BUTTON_COUNT).map(button => {
        return {
          key: button.id,
          label: fnGetButtonName(button)
        }
      })
    }
    return []
  }, [buttons])

  const refreshButton = async () => {
    setLoading(true)
    const { useDefault, buttons } = await fetchButtons()
    const data = buttons.map(button => {
      return { ...button, label: fnGetButtonName(button), onClick: () => handleButtonClick(button) }
    })
    setUseDefault(useDefault)
    setButtons(data)
  }

  const fetchButtons = async (): Promise<{ useDefault: boolean; buttons: IButton[] }> => {
    try {
      let platform = 'APP'
      if (window.isDingtalk) {
        platform = 'DINGTALK'
      } else if (window.isFeishu) {
        platform = 'FEISHU'
      }
      const { value } = await app.invokeService('@bills:get:flow:buttons', {
        flowId,
        scene,
        privilegeId,
        deviceType: 'DESKTOP',
        platform
      })

      return { useDefault: value.useDefault, buttons: value.buttons }
    } catch (e) {
      console.error(e)
      return { useDefault: true, buttons: [] }
    } finally {
      setLoading(false)
    }
  }

  const triggerFeishuEvent = async (button: IButton): Promise<ICustomButtonResult> => {
    const statusRes = await app.invokeService('@bills:get:feishu:chat:status', flowId)
    if (statusRes?.value?.status) {
      const res = await app.invokeService('@bills:get:feishu:chat:url', flowId)
      app.sdk.openLink(res.value.shareLink)
      return Promise.resolve({ success: true, result: {}, message: '已经绑定了群聊，前往飞书查看' })
    }
    const userInfo = app.getState()['@common'].userinfo?.data
    const ownerId = userInfo?.staff?.id
    let staffIds = []
    try {
      // 先关闭loading，因为选人组件关闭后，loading不会关闭
      buttonLoadingMap[button.id] = false
      setButtonLoadingMap({ ...buttonLoadingMap })
      const checkedList = await app
        .open('@organizationManagement:SelectStaff', {
          title: i18n.get('选择人员'),
          staffLimitData: undefined,
          required: true,
          multiple: true,
          fetchDataSourceAction: {
            staff: app.invokeService('@organizationManagement:get:visibility:staffs'),
            department: app.invokeService('@organizationManagement:get:visibility:departments')
          },
          data: [
            {
              type: 'department-member',
              checkIds: [ownerId]
            }
          ]
        })
      const staffData = checkedList[0]
      staffIds = staffData?.checkIds
    } catch (e) {
      return Promise.reject({ success: false, result: {}, message: '取消创建群聊' })
    }
    if(staffIds.length > 50){
      return Promise.reject({ success: false, result: {}, message: '最大支持50人发起群聊，请重新选择后进行发起' })
    }
    // 重新打开loading
    buttonLoadingMap[button.id] = true
    setButtonLoadingMap({ ...buttonLoadingMap })
    try {
      await app.invokeService('@bills:create:feishu:chat', {
        staffIds,
        ownerId, //群主ID 费控
        flowId // 绑定单据ID
      })
    } catch (e) {
      return Promise.reject({ success: false, result: {}, message: '创建群聊失败' })
    }
    return new Promise((resolve) => {
      Modal.confirm({
        title: '群聊创建成功',
        icon: <FilledTipsWarning />,
        content: '飞书群聊创建成功，点击去飞书查看该群聊',
        okText: '前往飞书',
        cancelText: '暂不前往',
        onOk: async () => {
          const res = await app.invokeService('@bills:get:feishu:chat:url', flowId)
          app.sdk.openLink(res.value.shareLink)
          resolve({ success: true, result: {}, message: '创建群聊成功，前往飞书查看' })
        },
        onCancel: () => {
          resolve({ success: true, result: {}, message: '创建群聊成功，暂不前往' })
        }
      });
    })
  }

  // 处理 HAB 按钮点击事件
  const handleHabButtonClick = async (button: IButton): Promise<ICustomButtonResult> => {
    if (button.customData?.type === 'FEISHU') {
      return triggerFeishuEvent(button)
    }
    // 需要的上下文数据在button.data里面
    return { success: true, result: {}, message: '' }
  }

  // 处理系统按钮点击事件
  const handleSystemButtonClick = (button: IButton) => {
    const flowActionButton = flowActionMap[button.action]
    if (flowActionButton && flowActionButton.onClick) {
      flowActionButton.onClick()
    }
  }

  const handleButtonClick = (button: IButton) => {
    buttonLoadingMap[button.id] = true

    // 处理 HAB 按钮点击事件
    if (button.type === 'hab') {
      // 调用 HAB 接口
      handleHabButtonClick(button)
        .then(result => {
          refreshButton()
          buttonLoadingMap[button.id] = false
          showMessage.success(result.message)
          setButtonLoadingMap({ ...buttonLoadingMap })
        })
        .catch(error => {
          buttonLoadingMap[button.id] = false
          showMessage.error(error.message)
          setButtonLoadingMap({ ...buttonLoadingMap })
        })
    } else if (button.type === 'system') {
      // 处理系统按钮点击事件
      handleSystemButtonClick(button)
      buttonLoadingMap[button.id] = false
    } else {
      showMessage.info(`暂未实现「${button.name}」的功能，请联系系统管理员`)
      buttonLoadingMap[button.id] = false
    }
    setButtonLoadingMap({ ...buttonLoadingMap })
  }

  const handleMenuClick = (info: { key: string }) => {
    const button = buttons.find(button => button.id === info.key)
    if (button) {
      handleButtonClick(button)
    }
  }

  if (!needConfigButton || !customExtendButton || !flowId?.length) {
    return children
  }

  if (isLoading) {
    return <SkeletonButton length={4} />
  }

  if (useDefault) {
    return children
  }

  if (!buttons.length && !isLoading) {
    return children
  }

  const btns = buttons.map(button => {
    return { ...button, label: fnGetButtonName(button), loading: buttonLoadingMap[button.id], onClick: () => handleButtonClick(button) }
  })

  return (
    <div className={'bill-info-buttons-wrapper'}>
      <FlexableButtonGroup buttons={btns} />
    </div>
  )
}

export default BillInfoButtons
