@import '~@ekuaibao/web-theme-variables/styles/default';
@import '~@ekuaibao/eui-styles/less/token.less';

.new-select-payee-modal {
  background: @color-white-1;
  width: 100%;
  height: 100%;
  border-radius: @radius-4;

  .select-payee-content {
    height: calc(100% - 64px);

    .payee-card-wrapper {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 16px;
      padding-bottom: 0px;
      .eui-tabs-nav {
        margin-bottom: 12px !important;
      }
      .card-header {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .search-card {
          flex: 1;
          margin-right: 12px;
        }
      }

      .card-tab-personal {
        margin-bottom: 12px;
      }

      .payer-item {
        margin-bottom: 12px;
        padding: 0 2px;
        background: #ffffff;
      }

      .select-payee {
        border: 1px solid var(--brand-base);
      }

      .payee-card-list {
        overflow-y: auto;
        margin-right: -16px; // 滚动条位置
        padding-right: 16px;

        .payee-empty {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: #bdbdbd;

          .fw-b {
            padding-left: 2px;
            font-weight: bold;
            color: #666666;
          }

          .clickable {
            .fw-b;
            cursor: pointer;
            color: var(--brand-base);
          }
        }

        .ant-tabs-content {
          height: calc(100% - 66px);
        }
      }
    }
  }

  .modal-footer {
    height: 64px;
    padding: 16px;
    border: 0px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.1));
    background: var(--eui-bg-body, #fff);
    box-shadow: 0px -2px 4px -4px rgba(29, 33, 41, 0.02), 0px -4px 8px 0px rgba(29, 33, 41, 0.02),
      0px -4px 16px 4px rgba(29, 33, 41, 0.03);
  }
}
.new-select-paypee-drawer-wrapper {
  .ant-drawer-header {
    padding: 16px;
  }
  .ant-drawer-body {
    overflow: hidden;
  }
  .eui-tabs-nav-wrap {
    padding: 0 !important;
  }
}
