/**
 *  Created by <PERSON><PERSON> on 2018/7/30 下午2:54.
 */
import React from 'react'
import Money from '../puppet/Money'
import { checkPayerInfo } from "../../lib/lib-util";
import { InvoicePayerInfoName, InvoicePayerInfoNum } from "./InvoiceHeaderInfo";

export function InvoiceMasterInfo(props) {
  const { dataSource, ...others } = props
  return (
    <div className="peyee-info">
      {dataSource.map((line, index) => {
        return getInvoiceItem(line, index, others)
      })}
    </div>
  )
}

function getInvoiceItem(line, index, others) {
  const { type } = line
  switch (type) {
    case 'money':
      return <InvoiceMoneyItem key={index} line={line} />
    case 'seller':
      return <SellerItem key={index} {...others} buyer={line} />
    case 'sellerTaxNo':
      return <SellerTaxNoItem key={index} {...others} buyer={line} />
    default:
      return <InvoiceItem key={index} line={line} />
  }
}

// 销售方名称展示
function SellerItem(props) {
  const { buyer, payerInfoArr, ischeck, status, message, isFrom, isOld, invoiceMark } = props
  const {
    label,
    value: { payertaxno, payer }
  } = buyer
  const { checkPayerName, checkPayerNumber } = checkPayerInfo(buyer.value, isOld)
  return (
    <div className="row">
      <div className="title">{label}</div>
      <div className="label">
        <InvoicePayerInfoName
          ischeck={ischeck}
          status={status}
          message={message}
          payer={payer}
          payertaxno={payertaxno}
          checkPayerName={checkPayerName}
          checkPayerNumber={checkPayerNumber}
          payerInfoArr={payerInfoArr}
          isFrom={isFrom}
          isSeller={true}
          invoiceMark={invoiceMark}
        />
      </div>
    </div>
  )
}

// 销售方纳税人识别好展示
function SellerTaxNoItem(props) {
  const { buyer, payerInfoArr, ischeck, status, message, isFrom, isOld, invoiceMark } = props
  const {
    value: { payertaxno, payer },
    label
  } = buyer
  const { checkPayerName, checkPayerNumber } = checkPayerInfo(buyer.value, isOld)
  return (
    <div className="row">
      <div className="title">{label}</div>
      <div className="label">
        <InvoicePayerInfoNum
          ischeck={ischeck}
          status={status}
          message={message}
          payer={payer}
          payertaxno={payertaxno}
          checkPayerName={checkPayerName}
          checkPayerNumber={checkPayerNumber}
          payerInfoArr={payerInfoArr}
          isFrom={isFrom}
          isSeller={true}
          invoiceMark={invoiceMark}
        />
      </div>
    </div>
  )
}

function InvoiceItem(props) {
  const {
    line: { label, value }
  } = props
  return (
    <div className="row">
      <div className="title">{label}</div>
      <div className="label">{value || '-'}</div>
    </div>
  )
}

function InvoiceMoneyItem(props) {
  const {
    line: { label, value }
  } = props
  return (
    <div className="row">
      <div className="title">{label}</div>
      <div className="label">
        {value ? <Money value={value} /> : '-'}
      </div>
    </div>
  )
}
