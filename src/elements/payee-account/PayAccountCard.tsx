import './PayAccountCard.less'
import React, { PureComponent } from 'react'
import { Divider, Ellipsis, Tooltip, Select } from '@hose/eui'

import EKBIcon from '../ekbIcon'
import classNames from 'classnames'
import { ChannelProps, Props } from './intreface'
import { EnhanceConnect } from '@ekuaibao/store'
import _compact from 'lodash/compact'

const { Option } = Select
const formChannelEnum = {
  payee: 'payee',
  paymentc: 'paymentConfirmation',
  pay: 'pay'
}

const BANK_DISABLE_BG = require('../bank-card/images/bank_disable_bg.svg')
const BANK_BG = require('../bank-card/images/bank_bank_bg.svg')
const BANK_CORPORATE_BG = require('../bank-card/images/bank_corporate_bg.svg')
const BANK_OTHER_BG = require('../bank-card/images/bank_other_bg.svg')
const BANK_WECHAT_BG = require('../bank-card/images/bank_wechat_bg.svg')

const getColorBg = (type: string, disable: boolean) => {
  if (disable) {
    return 'disable-status'
  }
  switch (type) {
    case 'WEIXIN':
      return 'wx-status'
    case 'BANK':
      return 'bank-status'
    default:
      return 'account-status'
  }
}
import { payFromChannelMap } from './account-list-consts'
import { FilledTipsCheck, OutlinedTipsMaybe } from '@hose/eui-icons'

interface State {
  channel: string
}

@EnhanceConnect(state => ({
  allCurrencyRates: state['@common'].allCurrencyRates,
  standardCurrency: state['@common'].standardCurrency,
  payeeConfig: state['@common'].payeeConfig,
  FOREIGN_CURRENCY_PAY: state['@common'].powers.FOREIGN_CURRENCY_PAY,
  globalFields: state['@common'].globalFields.data
}))
export default class AccountListItem extends PureComponent<Props, State> {
  constructor(props) {
    super(props)
    this.state = { channel: props.data.defaultChannel || 'OFFLINE' }
  }

  componentWillReceiveProps(np) {
    if (np.data !== this.props.data) {
      this.setState({ channel: np.data.defaultChannel || 'OFFLINE' })
    }
  }

  channelClick = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
  }

  handleClickSetDefault = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleSetDefault } = this.props
    handleSetDefault && handleSetDefault()
  }

  handleClickCancelDefault = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleClickCancelDefault } = this.props
    handleClickCancelDefault && handleClickCancelDefault()
  }

  handleCollect = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleCollect } = this.props
    handleCollect && handleCollect()
  }

  handleCancelCollect = (e: any) => {
    e.stopPropagation()
    e.preventDefault()
    const { handleCancelCollect } = this.props
    handleCancelCollect && handleCancelCollect()
  }
  handleWalletClick = (e: any) => {
    const { handleWalletClick, isWalletManager = false } = this.props
    if (!isWalletManager) {
      e.preventDefault()
      e.stopPropagation()
      return
    }
    handleWalletClick && handleWalletClick(e)
  }

  handleSelectOnChange = value => {
    const { onChannelChange, data } = this.props
    onChannelChange && onChannelChange(value)
  }
  getBgImg = (sort: string) => {
    const { data, payeeConfig } = this.props
    if (!data.active) {
      return BANK_DISABLE_BG
    } else if (data.type === 'PUBLIC' && payeeConfig?.publicAccountConfig?.publicBackgroundCls) {
      return BANK_CORPORATE_BG
    }
    switch (sort) {
      case 'WEIXIN':
        return BANK_WECHAT_BG
      case 'BANK':
        return BANK_BG
      default:
        return BANK_OTHER_BG
    }
  }
  renderCardInfo = () => {
    const {
      isManangePage,
      formChannel,
      data,
      onClick,
      isWalletManager,
      parentID,
      selectDisabled = false,
      configDisabled = false,
      dynamicChannels = [],
      channelList = [],
      dynamicChannelMap = {},
      allCurrencyRates,
      standardCurrency,
      FOREIGN_CURRENCY_PAY,
      isCNY
    } = this.props
    const channel = this.props.channel || 'OFFLINE'
    const newChanneList = channelList.map((channel: ChannelProps) => {
      const target: ChannelProps = dynamicChannels.find((item: ChannelProps) => item.code === channel.channel)
      if (target) channel.active = target.active
      return channel
    })
    const dataChannels = data.channels || []
    const newDataChannels = dataChannels.filter(channel => {
      return newChanneList.find((item: ChannelProps) => item.channel === channel && item.active)
    })
    const accountName = data.name || data.accountName
    const accountNo = data.accountNo || data.cardNo
    const defaultChannel = data.defaultChannel || 'OFFLINE'
    const notWallet = data.sort !== 'WALLET'
    const bankName = data.bank || data.unionBank
    const payChannel = formChannel === formChannelEnum.pay
    const paymentcChannel = formChannel === formChannelEnum.paymentc
    const bankIcon = data.icon || data.unionIcon

    const currency = data?.extensions?.currency || []
    const currencyList = []
    currency.forEach(item => {
      let currencyItem = allCurrencyRates.concat(standardCurrency).filter(el => el.numCode === item)
      if (currencyItem[0]) {
        currencyList.push(currencyItem[0].strCode)
      }
    })
    const remark = data?.remark || ''
    const _remark = remark?.length < 6 ? remark : remark?.substr(0, 5) + '...'
    return (
      <>
        <div className="bank-card">
          <img src={this.getBgImg(data.sort)} className="bank-item-top-bg"></img>
          <div className="bank-card-info">
            <Tooltip
              overlayStyle={{ maxWidth: 600 }}
              placement="bottomLeft"
              title={accountName}
              getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
            >
              <div className="bank-name">
                <div className="bank-name-ellipsis">{accountName}</div>
                {data.code && <span className="bank-code">{`(${data.code})`}</span>}
              </div>
            </Tooltip>
          </div>
          <div className="bank-card-no">
            <span>{formatCardNo(accountNo)}</span>
            {remark && (
              <span className="bank-remark">
                {i18n.get('备注：')}
                {remark?.length < 6 ? remark : <Tooltip title={remark}>{_remark}</Tooltip>}
              </span>
            )}
          </div>
          <div className="bank-card-footer">
            <div className="bank-card-icon">
              {bankName && <img className="bank-icon" src={`${bankIcon}?********`} alt={bankName} />}
              <Tooltip
                placement="topLeft"
                title={bankName}
                getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
              >
                {data.sort === 'OVERSEABANK' ? (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div className="bank-branch">{data.swiftCode}</div>
                    <Divider type="vertical" style={{ height: '16px' }} />
                    <div className="bank-branch">
                      <Ellipsis direction="end" content={data.bankName} />
                    </div>
                  </div>
                ) : (
                  <div className="bank-branch">
                    <Ellipsis direction="end" content={bankName} />
                  </div>
                )}
              </Tooltip>
            </div>
            {paymentcChannel && (
              <div className="payment-con-wrap" onClick={this.channelClick}>
                <Select
                  bordered={false}
                  style={{ width: 'max-content' }}
                  disabled={selectDisabled}
                  dropdownStyle={{ width: 180 }}
                  getPopupContainer={() => (parentID ? document.getElementById(parentID) : document.body)}
                  onChange={this.handleSelectOnChange}
                  value={newDataChannels.length && newDataChannels.find(v => v === channel)}
                >
                  {newDataChannels.length &&
                    newDataChannels.map((item: any, idx: number) => {
                      return (
                        <Option key={idx} value={item}>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <EKBIcon className="mr-8" fontSize={16} name={dynamicChannelMap?.[item]?.icon} />
                            {i18n.get(dynamicChannelMap[item].name)}
                          </div>
                        </Option>
                      )
                    })}
                </Select>
                {FOREIGN_CURRENCY_PAY && !isCNY && (
                  <Tooltip placement="top" title={i18n.get('当前仅展示支持外币支付的支付方式')}>
                    <OutlinedTipsMaybe fontSize={16} />
                  </Tooltip>
                )}
              </div>
            )}
            {payChannel && notWallet && (
              <div className="channel-wrap">
                <EKBIcon
                  className={classNames('stand-16-icon', { 'disable-img': !data.active })}
                  name={dynamicChannelMap[defaultChannel]?.icon}
                />
                <div>{dynamicChannelMap[defaultChannel]?.name}</div>
              </div>
            )}
          </div>
        </div>
      </>
    )
  }

  render() {
    const {
      onClick,
      className,
      data,
      isSelf,
      payFromChannel,
      inSelectPayMethodModal,
      isSelected,
      payeeConfig
    } = this.props
    // 当前类型是公共账户并且设置了当前账户底色
    const isPublicConfig = data.type === 'PUBLIC' && payeeConfig?.publicAccountConfig?.publicBackgroundCls
    let bgClass = isPublicConfig ? 'public-status' : getColorBg(data.sort, !data.active)
    const isSlefPersonal = payFromChannelMap.personal === payFromChannel && isSelf
    const fnOnClick = inSelectPayMethodModal
      ? onClick
      : isSlefPersonal || payFromChannelMap.personal !== payFromChannel
      ? // 企业钱包，数科钱包，数科虚拟卡不可点击
        data.sort === 'WALLET' || data.sort === 'CORPWALLET' || data.sort === 'VIRTUALCARD'
        ? null
        : onClick
      : null

    bgClass = !data.active ? 'gray-bg' : bgClass

    return (
      <div
        className={classNames('bank-card-wrap', bgClass, className, {
          'cur-n': data.sort === 'WALLET',
          'card-selected': isSelected
        })}
        onClick={fnOnClick}
      >
        {this.renderCardInfo()}
        {isSelected && (
          <div className="card-item-mask">
            <FilledTipsCheck className="mask-icon" fontSize={16} />
          </div>
        )}
      </div>
    )
  }
}

const formatCardNo = (cardNo: string) => {
  if (/[^0-9]/.test(cardNo)) {
    return cardNo
  }
  return cardNo.replace(/(\d{4})(?=\d)/g, '$1   ')
}
