import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Timeline, Tooltip } from '@hose/eui'
import { OutlinedDirectionDown, OutlinedDirectionUp } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
import { IAttributes, IFlowInfo, IFlowLog, INode, IPlan } from './types'
import moment from 'moment'
import { getV } from '@ekuaibao/lib/lib/help'
import { flowStateMap, fnMapLogs } from '../../../../../../elements/ekbc-business/bills/FlowLogItemWrapper'
import { StaffIF } from '@ekuaibao/ekuaibao_types'
import './FlowPlanContent.less'
import messageCenter from '@ekuaibao/lib/lib/message-center'
import { filterFlowPlanLogs, isEbotNodeByNode, shouldHideNode, fnFilterHiddenNodes } from './helper'
import { get } from 'lodash'
import { FlowPlanVersion } from './index'
import classNames from 'classnames'
import { Fetch } from '@ekuaibao/fetch'
import { getBoolVariation } from '../../../../../../lib/featbit'
import { getAIAgentLabelDom, getAIAgentObj, isAIAgentNode } from '../../../../../../elements/ai-agent-utils'

interface Props {
  flowInfo: IFlowInfo
  dynamicChannelMap: Record<string, any>
  staffDisplayConfigField: any
  userInfo: StaffIF
  isEditConfig: boolean
  filterType: string
  planNodes: IPlan[]
  onViewModifyClick: (currentLog: IFlowLog) => void
  canDeleteComment: boolean
  privilegeId: string
  bus: any
  staffMap?: Record<string, StaffIF>
  nodesAIAgentMap?: any
}

const FlowPlanContent: React.FC<Props> = props => {
  const {
    flowInfo,
    staffDisplayConfigField,
    userInfo,
    isEditConfig,
    filterType,
    planNodes,
    onViewModifyClick,
    canDeleteComment,
    privilegeId,
    bus,
    staffMap,
    nodesAIAgentMap
  } = props
  const [data, setData] = useState<IFlowLog[]>([])
  const [allData, setAllData] = useState<IFlowLog[]>([])
  // 构建将要审批的节点日志，实际就是剩余的几点拼接而成的日志Log
  const [featureLogs, setFeatureLogs] = useState<IFlowLog[]>([])
  const [showMore, setShowMore] = useState<boolean>(
    window.localStorage.getItem(`FlowPlanHistoryShowMore:${Fetch.ekbCorpId}`) === 'true'
  )
  const dynamicChannelMap = app.getState()['@audit']?.dynamicChannelMap

  useEffect(() => {
    const data = fnMapLogs(flowInfo.logs)
    // 当前正在审批的节点
    let resubmitMethod = ''
    const billState = flowInfo.state === 'draft' ? (flowInfo.plan ? 'retract' : 'draft') : flowInfo.state
    let remainingNodes = []
    if (billState !== 'archived' && billState !== 'paid') {
      remainingNodes =
        flowInfo.plan.nodes.filter(node => {
          const { ebotConfig } = node
          if (getBoolVariation('ao-79-flow-plan-filter-hiddenNode')) {
            if (shouldHideNode(node)) {
              return false
            }
            if (ebotConfig?.type !== 'costControlCheck' && get(node, 'config.isNeedCashierNode', true)) {
              return true
            }
          } else {
            if (ebotConfig?.type !== 'costControlCheck' && get(node, 'config.isNeedCashierNode', true)) {
              return true
            }
            if (shouldHideNode(node, { includeAIAgent: true })) {
              return true
            }
          }
          return false
        }) || []
    }
    let currentApprovingNodeIndex = remainingNodes.findIndex(node => node.id === flowInfo.plan.taskId)
    if (currentApprovingNodeIndex === -1) {
      // 从日志里面找到最后一条驳回/作废日志
      const rejectOrNullifyLogs =
        flowInfo?.logs?.filter(log => log.action === 'freeflow.reject' || log.action === 'freeflow.nullify') || []
      if (rejectOrNullifyLogs.length) {
        const lastRejectLog = rejectOrNullifyLogs[rejectOrNullifyLogs.length - 1]
        resubmitMethod = billState !== 'nullify' ? lastRejectLog.attributes.resubmitMethod : ''
        currentApprovingNodeIndex = remainingNodes.findIndex(node => node.id === lastRejectLog.attributes.nodeId)
      }
    }

    if (billState === 'pending') {
      data.push({
        time: flowInfo.createTime,
        action: 'current-approving',
        node: null,
        operatorId: get(flowInfo, 'form.submitterId'),
        attributes: {
          name: i18n.get('提交单据')
        }
      })
    } else if (billState !== 'draft' && billState !== 'nullify') {
      // 不是草稿和作废单据时
      // 如果找到了往日志里面增加一个当前正在审批的节点
      const currentApprovingNode = remainingNodes[currentApprovingNodeIndex]
      let currentApprovingNodeLog: IFlowLog
      if (flowInfo.logs?.length) {
        const allCurrentApprovingNodeLogs = flowInfo.logs.filter(log => log.state === 'approving')
        currentApprovingNodeLog = allCurrentApprovingNodeLogs[allCurrentApprovingNodeLogs.length - 1]
      }
      if (billState !== 'archived' && billState !== 'paid') {
        data.push({
          time: currentApprovingNodeLog?.time || Date.now(),
          action: 'current-approving',
          node: billState !== 'rejected' ? currentApprovingNode : null,
          operatorId:
            billState === 'rejected' || billState === 'retract'
              ? get(flowInfo, 'form.submitterId')
              : currentApprovingNode?.approverId,
          attributes: {
            name: billState === 'retract' || billState === 'rejected' ? i18n.get('提交单据') : '',
            nodeId: currentApprovingNode?.id,
            nodeName: fnGetNodeName(currentApprovingNode)
          }
        })
      }
    }
    if (billState !== 'pending' && billState !== 'draft' && remainingNodes.length) {
      if (billState === 'approving' && flowInfo.plan?.nextNodeId && flowInfo.plan?.resubmitMethod === 'TO_REJECTOR') {
        const { nextNodeId, addNodeIds = [] } = flowInfo.plan || {}
        // 审批时驳回到指定节点，重新审批时直接跳到指定节点,计算剩余流程的节点
        resubmitMethod = 'TO_REJECTOR'
        const nextNodeIndex = remainingNodes.findIndex(o => o.id === addNodeIds[1] || o.id === nextNodeId)
        if (nextNodeIndex > -1) {
          currentApprovingNodeIndex = nextNodeIndex
        }
      }
      if (resubmitMethod === 'TO_REJECTOR') {
        currentApprovingNodeIndex = currentApprovingNodeIndex - 1
      }
      if (resubmitMethod !== 'FROM_START') {
        remainingNodes = remainingNodes.slice(currentApprovingNodeIndex + 1)
      }
    }

    if (remainingNodes.length) {
      const featureLogs: IFlowLog[] = remainingNodes.map(node => {
        return {
          state: '',
          action: 'feature',
          node,
          operatorId: node.approverId,
          attributes: {
            nodeId: node.id,
            nodeName: fnGetNodeName(node)
          } as IAttributes
        }
      })
      setFeatureLogs(featureLogs)
    }

    setData(data)
    setAllData(data)
  }, [flowInfo])

  useEffect(() => {
    if (filterType && !!filterType.length) {
      const data = filterFlowPlanLogs(allData, filterType)
      setData(data)
    }
  }, [filterType, allData])

  const flowPlanNodeMap: Record<string, INode> = useMemo(() => {
    return planNodes.reduce((acc, cur) => {
      cur.nodes.forEach(node => {
        acc[node.id] = node
      })
      return acc
    }, {})
  }, [planNodes.length])

  useEffect(() => {
    if (
      getBoolVariation('ao-79-flow-plan-filter-hiddenNode') &&
      data.length &&
      flowPlanNodeMap &&
      Object.keys(flowPlanNodeMap).length
    ) {
      setData(fnFilterHiddenNodes(data, flowPlanNodeMap))
    }
  }, [flowPlanNodeMap])

  const fnGetNodeName = (node: INode): string => {
    if (node?.name === '出纳支付') {
      return i18n.currentLocale === 'en-US' && node?.enName ? node?.enName : node?.label
    }
    return i18n.currentLocale === 'en-US' && node?.enName ? node?.enName : node?.name
  }

  const fnGetFlowPlanLogColor = (ac: string, el: any) => {
    const colorMap = {
      reject: 'red',
      retract: 'red',
      receiveExcep: 'red',
      agree: 'green',
      activate: 'green',
      receive: 'green',
      pay: 'green',
      alter: 'var(--eui-function-info-500)',
      modify: 'var(--eui-function-info-500)',
      addnode: 'var(--eui-function-info-500)',
      'select.approver': 'var(--eui-function-info-500)',
      'current-approving': 'blue'
    }
    let color: string = colorMap[ac]
    const isEbotNode = isEbotNodeByNode(el)
    if (isEbotNode && ac === 'agree') {
      if (isEbotNode) {
        color = 'var(--eui-decorative-cyan-600)'
      }
    }
    const type = get(el, 'attributes.type')
    if (ac === 'back' && !type) {
      color = 'red'
    }
    const expressNum = get(el, 'attributes.expressNum')
    if (ac === 'send' && expressNum) {
      color = 'var(--eui-function-info-500)'
    }
    return color || 'gray'
  }

  if (!data.length) {
    return null
  }

  const fnGetNodeTitle = (ac: string, log: any) => {
    const nodeActionMap: Record<string, string> = {
      submit: i18n.get('提交单据')
    }
    // 不显示节点名称的action
    const noNodeTitleActions = ['back', 'comment', 'admin.skipnode', 'alter', 'select.approver']
    if (noNodeTitleActions.includes(ac)) {
      return ''
    }

    if (log?.node?.name === '出纳支付') {
      return log?.attributes?.nodeName
    }

    return (
      nodeActionMap[ac] ||
      fnGetNodeName({ name: log?.attributes?.name, enName: log?.attributes?.enName } as INode) ||
      fnGetNodeName({ name: flowPlanNodeMap[log?.attributes?.nodeId]?.label, enName: flowPlanNodeMap[log?.attributes?.nodeId]?.enName } as INode) ||
      fnGetNodeName(flowPlanNodeMap[log?.attributes?.nodeId]) ||
      log?.attributes?.nodeName
    )
  }

  const handleVersionClick = item => {
    onViewModifyClick(item)
  }

  const handleShowMore = () => {
    setShowMore(!showMore)
    window.localStorage.setItem(`FlowPlanHistoryShowMore:${Fetch.ekbCorpId}`, String(!showMore))
  }

  const handleModifiedStaff = node => {
    messageCenter.emit('on-click-modify-flow', node, flowInfo, true)
  }

  const renderTimelineItem = (el: any, index: number, isLastNode = false, isFeature = false) => {
    const { action } = el
    const ac = action.replace('freeflow.', '')
    let nodeTitle = fnGetNodeTitle(ac, el)
    const tipTitle = nodeTitle?.length > 14 ? nodeTitle : ''
    if (isAIAgentNode(el)) {
      nodeTitle = getAIAgentLabelDom(nodeTitle, true)
      const { agent } = getAIAgentObj(el, nodesAIAgentMap)
      el.operatorId = { name: agent?.name, avatar: agent?.icon, isAIAgentNode: true }
    }
    el.isEditConfig = isEditConfig
    const hideTime = isFeature || ac === 'current-approving'

    return (
      <Timeline.Item
        key={index}
        className={classNames({
          'current-approving': ac === 'current-approving',
          'flow-log-last-node': isLastNode,
          'flow-log-feature': isFeature
        })}
        color={fnGetFlowPlanLogColor(ac, el)}
      >
        {hideTime ? null : <div className="time">{moment(el.time).format('YYYY-MM-DD HH:mm')}</div>}
        <div className="flow-plan-content">
          {nodeTitle ? (
            <div className="flow-plan-content-header">
              <Tooltip title={tipTitle}>
                <div className={classNames('flow-plan-title', { 'flow-plan-feature-title': isFeature })}>
                  {nodeTitle}
                </div>
              </Tooltip>
            </div>
          ) : null}
          <div className="flow-plan-content-middle">
            {flowStateMap[ac] &&
              flowStateMap[ac].render({
                item: el,
                onViewVersionClick: handleVersionClick,
                userInfo,
                billInfo: flowInfo,
                bus,
                staffMap,
                config: {
                  useNewPlanStyle: true,
                  useReg: false,
                  flowLogs: flowInfo.logs,
                  onModifiedStaff: handleModifiedStaff,
                  canDeleteComment,
                  privilegeId,
                  nodesAIAgentMap
                }
              })}
          </div>
        </div>
      </Timeline.Item>
    )
  }

  const renderFeatureLogs = () => {
    return (
      <>
        {showMore
          ? featureLogs.map((el, index) => {
            return renderTimelineItem(el, index, index === featureLogs.length - 1, true)
          })
          : null}
        {featureLogs.length ? (
          <div className={classNames('show-more-logs', { 'show-more-logs-show': showMore })} onClick={handleShowMore}>
            {showMore ? (
              <OutlinedDirectionUp className="mr-4" fontSize={12} />
            ) : (
              <OutlinedDirectionDown className="mr-4" fontSize={12} />
            )}
            {showMore ? i18n.get('收起剩余流程') : i18n.get('查看剩余流程')}
          </div>
        ) : null}
      </>
    )
  }

  return (
    <div className="flow-plan-content-wrapper">
      <Timeline className="flow-plan-content-wrapper-time-line">
        <div className="flow-plan-content-log-wrapper">
          {data.map((el: any, index) => {
            el.dynamicChannelMap = dynamicChannelMap
            const isEbotNode = getV(el, 'attributes.isEbotNode')
            const isInvoiceApplicationNode = getV(el, 'attributes.isInvoiceApplicationNode')
            const isRecalNode = getV(el, 'attributes.isRecalNode')
            if (isAIAgentNode(el)) {
              const { agent } = getAIAgentObj(el.node, nodesAIAgentMap)
              el.operatorId = { name: agent?.name, avatar: agent?.icon }
            }
            if (isEbotNode) {
              el.operatorId = { name: 'EBot', avatar: 'EBotIconNode' }
            }
            if (isInvoiceApplicationNode) {
              el.operatorId = { name: i18n.get('开票申请'), avatar: 'EBotIconNode' }
            }
            if (isRecalNode) {
              el.operatorId = { name: i18n.get('重算节点'), avatar: 'EBotIconNode' }
            }
            // 通讯录显示配置
            flowStateMap.staffDisplayConfigField = staffDisplayConfigField
            const isLastNode = showMore ? false : index === data.length - 1
            return renderTimelineItem(el, index, isLastNode)
          })}
          {renderFeatureLogs()}
        </div>
        <FlowPlanVersion flowInfo={flowInfo} />
      </Timeline>
    </div>
  )
}

export default FlowPlanContent
