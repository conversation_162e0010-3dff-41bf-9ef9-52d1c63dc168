/**
 * <AUTHOR> <<EMAIL>>
 * @date 2018-02-05
 * @description 差旅行程组件样式
 */
@import '~@ekuaibao/web-theme-variables/styles/colors';
@import '~@ekuaibao/eui-styles/less/token.less';
.trips {
  width: 100%;
  margin-bottom: 16px;
  :global {
    h3 {
      font-size: 14px;
      color: @gray-9;
      font-weight: 400;
    }

    .add-button {
      width: 168px;
      height: 38px;
      color: @color-brand;
      border-radius: @radius-1;
      background-color: rgba(237, 249, 251, 1);
      line-height: 38px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      margin-right: 8px;
    }

    .trip-list {
      width: 100%;
      .trips-list-wrapper {
        .trips-item {
          &:hover {
            cursor: pointer;
            background: rgba(29, 43, 61, 0.06) !important;
          }
        }
      }
      li {
        position: relative;
        padding-bottom: 24px;

        &::before {
          position: absolute;
          left: -16px;
          top: 5px;
          content: '';
          display: inline-block;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background: #bbbdbd;
        }

        &::after {
          position: absolute;
          content: '';
          left: -14px;
          top: 0;
          bottom: 0;
          width: 1px;
          background: #bbbdbd;
        }

        &:first-child::after {
          top: 5px;
        }

        &:last-child::after {
          height: 5px;
          bottom: auto;
        }
      }

      h5 {
        font-size: 14px;
        color: #333333;
        font-weight: 500;
      }

      .card {
        display: flex;
        justify-content: space-between;
        padding: 12px;
        border-radius: 2px;
        background-color: #f8f9f9;
        .card-content {
          display: flex;
          flex: 1;
          .card-icon {
            display: inline-block;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            margin-right: 10px;
            background-color: var(--brand-base);
          }

          .trip-content {
            flex: 1;
          }

          .city {
            font-size: 14px;
            font-weight: 500;
            color: #3a3f3f;
            line-height: 22px;
          }

          .detail {
            display: block;
            font-size: 14px;
            line-height: 1.5;
            color: #959898;
            margin-bottom: 24px;
            padding-left: 2px;

            .label {
              color: #6c6c6c;
            }
          }
        }

        .content-money {
          display: flex;
          align-items: center;
          flex-direction: column;
          padding-right: 15px;
          p {
            margin: 0px;
            padding: 0px;
            line-height: 20px;
          }
          .ant-btn {
            margin-right: 0px;
            padding: 4px 8px;
          }
          span {
            cursor: pointer;
            padding: 4px 8px;
            line-height: 22px;
            color: var(--brand-base);
          }
        }
      }
    }
  }
}
