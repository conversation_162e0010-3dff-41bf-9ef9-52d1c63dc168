import React, { useState, useEffect } from 'react'
import { Fetch } from '@ekuaibao/fetch'
import styles from './TravelItem.module.less'
import { app as api } from '@ekuaibao/whispered'
import { Button } from 'antd'
import { Popover, Tooltip } from '@hose/eui'
import moment from 'moment'
const EKBIcon = api.require('@elements/ekbIcon')
const TRAIN = require('./images/train_new.svg')
const FLIGHT = require('./images/flight_new.svg')
const TAXI = require('./images/car_new.svg')
const HOTEL = require('./images/hotel_new.svg')
const COMMON = require('./images/common_new.svg')
const FOOD = require('./images/food_new.svg')
const DELETE = require('./images/delete.svg')
const LINE = require('./images/line_icon.svg')
import { getWeek } from '../../lib/lib-util'
import {
  FilledTipsNode,
  OutlinedDirectionDownRound,
  OutlinedEditDeleteTrash,
  OutlinedTipsInfo,
  TwoToneGeneralAirplane,
  TwoToneGeneralFood,
  TwoToneGeneralHotel,
  TwoToneGeneralInternationalBusiness,
  TwoToneGeneralTaxi,
  TwoToneGeneralTrain
} from '@hose/eui-icons'
import { getBoolVariation } from '../../lib/featbit'
import { Resource } from '@ekuaibao/fetch'
const v2CityGroup = new Resource('/api/v2/basedata/cityGroup/')
const cityFetch = new Resource('/api/v2/basedata/city/')

const TYPE_MAP = {
  用车: TAXI,
  飞机: FLIGHT,
  火车: TRAIN,
  酒店: HOTEL,
  餐饮: FOOD,
  通用: COMMON
}

const typeIconMap: Record<string, React.ReactNode> = {
  飞机: <TwoToneGeneralAirplane />,
  火车: <TwoToneGeneralTrain />,
  酒店: <TwoToneGeneralHotel />,
  餐饮: <TwoToneGeneralFood />,
  用车: <TwoToneGeneralTaxi />,
  通用: <TwoToneGeneralInternationalBusiness />
};

interface TravelItemProps {
  line: any
  onlyOne?: boolean
  hiddenLine?: boolean
  readOnly?: boolean
  showButton?: boolean
  isNewGroup?: boolean
  showOrderButton?: boolean
  tripType?: string
  onOpen?: () => void
  onDeleteTrip?: () => void
  onShowDetail?: () => void
  onOrderClick?: () => {}
}

const TravelItem: React.FC<TravelItemProps> = ({
  line,
  tripType,
  onOpen,
  onDeleteTrip,
  onShowDetail,
  readOnly,
  showButton,
  isNewGroup,
  isFirstGroup,
  isLastGroup,
  isSingleGroup,
  isOriginalTravelPlainning,
  onOrderClick,
  showOrderButton,
  hiddenLine,
  onlyOne,
  editable = true,
  hiddleLeft,
  sceneTypeField
}) => {
  const [startCityGroupDisplay, setStartCityGroupDisplay] = useState('')
  const [endCityGroupDisplay, setEndCityGroupDisplay] = useState('')
  const [localSt, setLocalSt] = useState<any[]>([])
  const [localEt, setLocalEt] = useState<any[]>([])

  const fetchAndProcessCityDetails = async (travelFromCity: string, travelToCity: string) => {
    const fromCityData = (travelFromCity && JSON.parse(travelFromCity)) || []
    const toCityData = (travelToCity && JSON.parse(travelToCity)) || []
    
    const foreignCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        if (city?.enLabel && !city.enLabel.includes('China')) {
          foreignCityKeys.push(city.key)
        }
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        if (city?.enLabel && !city.enLabel.includes('China')) {
          foreignCityKeys.push(city.key)
        }
      }
    }

    if (foreignCityKeys.length > 0) {
      try {
        const res = await cityFetch.GET('[ids]', { ids: foreignCityKeys.join(',') })
        
        if (res && res.items && Array.isArray(res.items)) {
          const processCityDetails = (cityData: any[], cityDetails: any[]) => {
            if (!cityData || !cityDetails || !Array.isArray(cityData) || !Array.isArray(cityDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))
            
            cityDetails.forEach(cityDetail => {
              if (cityDetail.fullName) {
                const countryName = cityDetail.fullName.split(',')[0]
                newCityData.forEach(city => {
                  if (city.key === cityDetail.id && !city.label.includes(`(${countryName})`)) {
                    city.label = `${city.label}(${countryName})`
                  }
                })
              }
            })
            
            return newCityData
          }

          const enhanceFromCityData = processCityDetails(fromCityData, res.items)
          const enhanceToCityData = processCityDetails(toCityData, res.items)
          
          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市详情失败:', error)
      }
    }
    
    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  const handleGetPrice = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    onOpen && onOpen(e)
  }
  const handleDeleteTrip = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    editable && onDeleteTrip && onDeleteTrip(e)
  }
  const handleShowDetail = () => {
    onShowDetail && onShowDetail()
  }

  const handleOrderClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    onOrderClick && onOrderClick()
  }


  let { type, travelToCity, travelFromCity, startDate, endDate, referencePrice, scene, sceneList } = line
  let dateString
  type = tripType ? tripType : type
  const startDateStr = moment(startDate).format(i18n.get('MM月DD日'))
  if (startDate && endDate) {
    const endDateStr = moment(endDate).format(i18n.get('MM月DD日'))
    const daysNumber = moment(endDate).diff(startDate, 'days')
    const desc = type === '酒店' ? i18n.get('共 {days} 天 {nights} 晚', { days: daysNumber + 1, nights: daysNumber }) : i18n.get('共 {days} 天', { days: daysNumber + 1 })
    dateString = (
      <>
        <span>{`${startDateStr} - ${endDateStr}`}</span>
        <span className="date-line">&nbsp;</span>
        <span className="date-count">{desc}</span>
      </>
    )
  } else {
    dateString = `${startDateStr} ${getWeek(startDate)}`
  }

  // 保留原始数据同步
  useEffect(() => {
    const fromCityData = (travelFromCity && JSON.parse(travelFromCity)) || []
    const toCityData = (travelToCity && JSON.parse(travelToCity)) || []
    setLocalSt(fromCityData)
    setLocalEt(toCityData)
  }, [travelFromCity, travelToCity])

  useEffect(() => {
    if(!getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')) return
    const processData = async () => {
      const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityDetails(travelFromCity, travelToCity)
      setLocalSt(enhanceFromCityData)
      setLocalEt(enhanceToCityData)
    }
    processData()
  }, [travelFromCity, travelToCity])

  const st = localSt?.length > 0 ? localSt : (travelFromCity && JSON.parse(travelFromCity)) || []
  const et = localEt?.length > 0 ? localEt : (travelToCity && JSON.parse(travelToCity)) || []
  const sCity = st[0]
  const eCity = et[0]
  const allStartCity = st?.map(item => item?.label)?.join('、')
  const allEndCity = et?.map(item => item?.label)?.join('、')
  let startCity = i18n.currentLocale === 'en-US' ? (sCity?.enLabel || sCity?.label || '') : (sCity?.label || '')
  let endCity = i18n.currentLocale === 'en-US' ? (eCity?.enLabel || eCity?.label || '') : (eCity?.label || '')
  let tooltipStr = ''
  if (Fetch.lang === 'zh-CN') {
    startCity = startCity.split('/').pop()
    endCity = endCity.split('/').pop()
  }
  if (st.length > 1) {
    startCity += `(+${st.length - 1})`
    tooltipStr = st?.map((v: any) => v.label)?.join('、')
  }
  if (et.length > 1) {
    endCity += `(+${et.length - 1})`
  }
  let startMonthStr = ''
  let startdayStr = ''
  if (isNewGroup) {
    startMonthStr = moment(startDate).month() + 1 + ''
    const startdayNum = moment(startDate).date()
    startdayStr = startdayNum > 9 ? `${startdayNum}` : `0${startdayNum}`
  }

  useEffect(() => {
    if(st?.[0]?.type !== 'cityGroup' && et?.[0]?.type !== 'cityGroup') {
      setStartCityGroupDisplay('')
      setEndCityGroupDisplay('')
      return
    }
    const fetchData = async () => {
      const userInfo = api.getState('@common')?.userinfo?.data
      if(st?.[0]?.type === 'cityGroup'){
        v2CityGroup.GET('findLimitedCities', {
          cityGroupId: st?.[0]?.key,
          staffId: userInfo.staff.id,
          count: 3000
        }).then(res => {
          const result = res?.items?.map(item => item.name).join('、')
          setStartCityGroupDisplay(result)
        })
      }
      if(et?.[0]?.type === 'cityGroup'){
        v2CityGroup.GET('findLimitedCities', {
          cityGroupId: et?.[0]?.key,
          staffId: userInfo.staff.id,
          count: 3000
        }).then(res => {
          const result = res?.items?.map(item => item.name).join('、')
          setEndCityGroupDisplay(result)
        })
      }
    }
    fetchData()
  }, [st, et])

  return (
    <section className={styles['travel-item']} onClick={handleShowDetail}>
      {getBoolVariation('fkrd-4949-new-travel-plan-card') && isOriginalTravelPlainning ? <>
        {!isSingleGroup && (
          <div className="left-part-new">
            {isNewGroup && !isFirstGroup && <div className="line-icon-front" />}
            {isNewGroup &&
                (!isFirstGroup && !isLastGroup ? (
                  <OutlinedDirectionDownRound style={{ margin: readOnly ? '3px 0' : '4px 0 3px' }} />
                ) : (
                  <FilledTipsNode style={{ margin: readOnly ? '3px 0' : '4px 0 3px', color: '#2555FF' }} />
                ))}
            {!isSingleGroup && !isLastGroup && <div className="line-icon" />}
          </div>
        )}
      <div className="right-part-new">
        {isNewGroup && <div className="date-line" style={{ marginTop: isFirstGroup ? 0 : 16 }}>
          {startMonthStr}.{startdayStr}&nbsp;{getWeek(startDate)}
        </div>}
        <div className="trip-card">
          <div className='title-line'>
            <div className='title'>
              <div style={{ fontSize: 24, marginRight: 4 }}>{typeIconMap[type]}</div>
              <div className='trip-type'>{type}</div>
            </div>
            {!readOnly && (
              <div onClick={e => handleDeleteTrip(e)}>
                {!editable ? <Tooltip title={i18n.get('该行程已经发生订购，若想删除行程， 请先取消对应的订单')}>
                  <OutlinedEditDeleteTrash className="info-top-delete-icon disabled" />
                </Tooltip> : <OutlinedEditDeleteTrash className="info-top-delete-icon" />}
              </div>
            )}
          </div>
          <div className='info-box'>
          <div className="city-line">
            <span className="start">
              {startCityGroupDisplay ? (
                <>
                  {startCity}
                  <Popover content={<div style={{ maxHeight: 480, maxWidth: 400 }}>{startCityGroupDisplay}</div>}>
                      <OutlinedTipsInfo style={{ fontSize: 16, marginLeft: 4, marginBottom: '-1px', color: 'rgba(29, 33, 41, 0.40)' }} />
                  </Popover>
                </>
              ) : (
                st?.length === 1 && startCity
              )}
              {st?.length > 1 && <Tooltip title={allStartCity}>{startCity}</Tooltip>}
            </span>
            {!endCity}
            {endCity && <span>-</span>}
            {endCity && (
              <span className="end">
                 {endCityGroupDisplay ? (
                  <>
                     {endCity}
                    <Popover content={<div style={{ maxHeight: 480, maxWidth: 400 }}>{endCityGroupDisplay}</div>}>
                       <OutlinedTipsInfo
                        style={{ fontSize: 16, marginLeft: 4, marginBottom: '-1px', color: 'rgba(29, 33, 41, 0.40)' }}
                      />
                    </Popover>
                  </>
                ) : (
                  et?.length === 1 && endCity
                  )}
                  {et?.length > 1 && <Tooltip title={allEndCity}>{endCity}</Tooltip>}
                </span>
              )}
            </div>
            <div className='detail-line'>
              <div>{dateString}</div>
              {(sceneList?.length > 0 || (!readOnly && editable && sceneTypeField)) && <div style={{ height: 20}}><div className='divider'/></div>}
              <div className='scene-line'>
                {sceneList?.length > 0 ? 
                  Array.isArray(sceneList) ? (sceneList?.map((item) => item?.sceneName) || []).join('、') : scene?.sceneName 
                  :
                  !readOnly && editable ? 
                    sceneTypeField === 'open_required' && <div style={{ color: '#F53F3F' }}>请选择场景(必填)</div>
                    : 
                    sceneTypeField === 'open_notRequired' && <div style={{ color: 'rgba(29, 33, 41, 0.50)' }}>请选择场景(非必填)</div>}
              </div>
              </div>
            {(!!referencePrice || showButton) && <div className='price-line'>
              {!!referencePrice && (
                <div className="price">
                  {i18n.get('参考报价:{money}元/', { money: referencePrice })}
                  {i18n.get(type === '酒店' ? '晚' : '张')}
                </div>
              )}
              {showButton && ['飞机', '酒店', '火车'].includes(type) && !readOnly && (
                <div className="btn-get-price" style={{ marginLeft: 8 }} onClick={handleGetPrice}>
                  {i18n.get(referencePrice > 0 ? '重新获取' : '获取参考报价')}
                </div>
              )}
            </div>}
          </div>
        </div>
      </div >
      </> : 
      <>
      {!hiddenLine && (
        <div className="left-line">
          {isNewGroup && <div className="date">{startMonthStr}{i18n.currentLocale !== 'en-US' && i18n.get('月')}</div>}
          {isNewGroup && <div className="month">{startdayStr}</div>}
          {!onlyOne && <div className="line-icon" />}
        </div>
      )}
      <div className="right-part">
        <div className="info-top">
          <div className="info-top-first-line">
            <img className="info-top-icon" src={TYPE_MAP[type] || COMMON} />
            <div className="info-top-city">
              <span className="start">{startCity}</span>
              {!endCity && <small>{Array.isArray(sceneList) ? (sceneList?.map((item) => item?.sceneName) || []).join('、') : scene?.sceneName}</small>}
              {endCity && <img className="end-line" src={LINE} />}
              {endCity && <span className="end">{endCity}</span>}
            </div>
          </div>
          {endCity && <div className='scene-line'><small>{Array.isArray(sceneList) ? (sceneList?.map((item) => item?.sceneName) || []).join('、') : scene?.sceneName}</small></div>}
          <div className="info-top-second-line">
            <div className="info-top-name">{i18n.get(type)}</div>
            <div className="info-top-date">{dateString}</div>
          </div>
          {showOrderButton && (
            <Button type="primary" className="travel-item-button" onClick={handleOrderClick}>
              {i18n.get('订购')}
            </Button>
          )}
          {!readOnly && (
            <div onClick={e => handleDeleteTrip(e)}>
              {!editable ? <Tooltip title='该行程已经发生订购，若想删除行程， 请先取消对应的订单' placement='topLeft'>
                <OutlinedEditDeleteTrash className="info-top-delete-icon disabled" />
              </Tooltip> : <OutlinedEditDeleteTrash className="info-top-delete-icon" />}
            </div>
          )}
        </div>
        <div className="price-wrapper">
          {!!referencePrice && (
            <div className="price-wrapper-money">
              {i18n.get('参考报价:{money}元/', { money: referencePrice })}
              {i18n.get(type === '酒店' ? '晚' : '张')}
            </div>
          )}
          {showButton && ['飞机', '酒店', '火车'].includes(type) && !readOnly && (
            <div className="price-wrapper-action" onClick={handleGetPrice}>
              {i18n.get(referencePrice > 0 ? '重新获取' : '获取报价')}
            </div>
          )}
        </div>
        <div className="margin-part" />
      </div >
      </>}
    </section >
  )
}

export default TravelItem
