import React from 'react'
import styles from './TripItem.module.less'
import { Tooltip } from '@hose/eui'
import { app as api } from '@ekuaibao/whispered'
const EKBIcon = api.require('@elements/ekbIcon')
import { useEffect, useState } from 'react'
import { Resource } from '@ekuaibao/fetch'
const cityFetch = new Resource('/api/v2/basedata/city/')
import { getBoolVariation } from '../../../lib/featbit'

const TripItem = ({
  tripType,
  dateString,
  cityStr,
  readonly,
  editable,
  showButton,
  money,
  type,
  onOpen,
  onDeleteTrip,
  onShowDetail,
  customField,
  tooltipStr,
  startCity,
  endCity
}) => {
  const [startCityEnhance, setStartCityEnhance] = useState([])
  const [endCityEnhance, setEndCityEnhance] = useState([])

  const [startCityStr, setStartCityStr] = useState([])
  const [endCityStr, setEndCityStr] = useState([])

  const formatCity = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray
    return cityArray.map(city => city.label || city.name || '').join(', ')
  }

  const formatCityOrigin = cityArray => {
    if (!Array.isArray(cityArray)) return cityArray
    
    if (cityArray.length === 1) {
      return cityArray[0].label || cityArray[0].name || ''
    }
    
    if (cityArray.length > 1) {
      const firstCity = cityArray[0].label || cityArray[0].name || ''
      return `${firstCity}(+${cityArray.length - 1})`
    }
    
    return ''
  }

  useEffect(() => {
    if(!getBoolVariation('fkrd-5582-travel-planning-show-foreign-country')) return
    const fetchData = async () => {
      const { enhanceFromCityData, enhanceToCityData } = await fetchAndProcessCityDetails(startCity, endCity)
      
      setStartCityStr(formatCityOrigin(enhanceFromCityData))
      setEndCityStr(formatCityOrigin(enhanceToCityData))
      setStartCityEnhance(formatCity(enhanceFromCityData))
      setEndCityEnhance(formatCity(enhanceToCityData))
    }
    fetchData()
  }, [startCity, endCity])

  const fetchAndProcessCityDetails = async (travelFromCity, travelToCity) => {
    const fromCityData = travelFromCity?.length > 0 ? (travelFromCity && JSON.parse(travelFromCity)) || [] : []
    const toCityData = travelToCity?.length > 0 ? (travelToCity && JSON.parse(travelToCity)) || [] : []
    const foreignCityKeys = []
    if (fromCityData && fromCityData.length > 0) {
      for (const city of fromCityData) {
        foreignCityKeys.push(city.key)
      }
    }
    if (toCityData && toCityData.length > 0) {
      for (const city of toCityData) {
        foreignCityKeys.push(city.key)
      }
    }

    if (foreignCityKeys.length > 0) {
      try {
        const res = await cityFetch.GET('[ids]', { ids: foreignCityKeys.join(',') })

        if (res && res.items && Array.isArray(res.items)) {
          const processCityDetails = (cityData, cityDetails) => {
            if (!cityData || !cityDetails || !Array.isArray(cityData) || !Array.isArray(cityDetails)) {
              return cityData
            }

            const newCityData = cityData.map(city => ({ ...city }))

            cityDetails.forEach(cityDetail => {
              if (cityDetail.fullName && cityDetail.countryCode !== 'CN') {
                const countryName = cityDetail.fullName.split(',')[0]
                newCityData.forEach(city => {
                  if (city.key === cityDetail.id && !city.label.includes(`(${countryName})`)) {
                    city.label = `${city.label}(${countryName})`
                  }
                })
              }
            })

            return newCityData
          }

          const enhanceFromCityData = processCityDetails(fromCityData, res.items)
          const enhanceToCityData = processCityDetails(toCityData, res.items)

          return { enhanceFromCityData, enhanceToCityData }
        }
      } catch (error) {
        console.error('请求城市详情失败:', error)
      }
    }

    return { enhanceFromCityData: fromCityData, enhanceToCityData: toCityData }
  }

  const handleGetPrice = e => {
    onOpen && onOpen(e)
  }
  const handleDeleteTrip = e => {
    onDeleteTrip && onDeleteTrip(e)
  }
  const handleShowDetail = () => {
    onShowDetail && onShowDetail()
  }
  return (
    <section className={styles['trip-item']} onClick={() => handleShowDetail()}>
      <div className="infoTop">
        <img className="trip-type-icon" src={tripType.icon} style={{ background: tripType.color }} />
        <div className="content-wrapper">
          <div className="date">{dateString}</div>
          {getBoolVariation('fkrd-5582-travel-planning-show-foreign-country') && startCityStr ? <>
            <Tooltip title={startCityEnhance || ''}>
                <span className="city">{startCityStr}</span>
            </Tooltip>
            {endCity && (
            <Tooltip title={endCityEnhance || ''}>
                <span className="city">&nbsp;-&nbsp;{endCityStr}</span>
            </Tooltip>
          )}
          </> : 
            <Tooltip title={tooltipStr || ''}>
                <span className="city">{cityStr}</span>
            </Tooltip>
           }
        </div>
        {!readonly && editable && (
          <div onClick={e => handleDeleteTrip(e)}>
            <EKBIcon name="#ico-7-a-Group87" className="close-icon" />
          </div>
        )}
      </div>
      <div className="price-wrapper">
        {showButton && (
          <div className="content-money">
            <span onClick={e => handleGetPrice(e)}>{money ? '重新获取 > ' : '获取参考报价 > '}</span>
          </div>
        )}
        {money ? (
          <div className="content-money">
            <div>
              {i18n.get('参考报价:{money}元/', { money })}
              {i18n.get(type == 'HOTEL' ? '晚' : '张')}
            </div>
          </div>
        ) : null}
      </div>
      <div className="custom-field-wrapper">{customField}</div>
    </section>
  )
}

export default TripItem
