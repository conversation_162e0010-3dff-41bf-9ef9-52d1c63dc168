import styles from './AdminAddNodeModal.module.less'
import React, { Fragment, PureComponent } from 'react'
import { Button, Icon, Input, Radio, Form, Select, Tooltip } from 'antd'
import { FilledTipsWarning } from '@hose/eui-icons'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { showMessage } from '@ekuaibao/show-util'
import { EnhanceConnect } from '@ekuaibao/store'
import { get, remove } from 'lodash'
import { adminOperateNode } from '../bills.action'
const FormItem = Form.Item
const { TextArea } = Input
const RadioGroup = Radio.Group
import { app as api } from '@ekuaibao/whispered'
import MoneyView from '../../../elements/MoneyView/MoneyView'
import AttachmentComponent from '../../../elements/attachment-component/AttachmentComponent'
import EnhanceFormCreate from '../../../elements/enhance/enhance-form-create'
const WarningAlert = api.require('@audit/WarningAlert')
const { RiskStatus } = api.require('@audit/elements/agree-bill-modal/AgreeBillUtil')
const ApprovalComments = api.require('@audit/ApprovalComments')
import MessageCenter from '@ekuaibao/messagecenter'
import TagSelector from '../../../elements/tag-selector-edit'
const EkbIcon = api.require('@elements/icon')
@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data
}))
@EnhanceModal({
  title: '',
  footer: [],
  className: 'respond-modal-layer'
})
@EnhanceFormCreate()
export default class AdminAddNodeModal extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      type: 'PRE_ADD_NODE',
      attachments: [],
      selectedStaffList: [],
      selectedStaffListIds: [],
      policyType: 'ALL',
      canSignNodes: [],
      currSingNodeId: {}
    }

    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
  }

  bus = new MessageCenter()

  componentDidMount() {
    const { flowPlan } = this.props
    this.fnfixPlanNodes(flowPlan)
  }
  getResult() {
    let { form } = this.props
    let { attachments, type, selectedStaffListIds, policyType, currSingNodeId } = this.state
    return new Promise((resolve, reject) => {
      form.validateFieldsAndScroll((error, fieldValue) => {
        this.bus.invoke('get:mention:content').then(mentionContent => {
          if (!!error) {
            reject()
            return
          }
          const data = {
            ...fieldValue,
            approverId: selectedStaffListIds,
            type: type,
            attachments,
            name: 'freeflow.admin.addnode',
            operatorRole: "admin",
            nodeIds: [currSingNodeId.id],
            ...mentionContent,
          }
          if (selectedStaffListIds.length > 1) {
            data.policy = policyType
          }
          resolve(data)
        })
      })
    })
  }

  //因为重算节点会把后续加签的节点删掉，所以：如果重算在当前审批节点之后则后续节点不能加签
  //如果当前节点之前有重算节点，需提示 避免驳回后重算引发 加签节点删除
  fnfixPlanNodes = (flowPlan) => {
    const nodes = get(flowPlan, 'nodes', [])
    const taskId = get(flowPlan, 'taskId', '')
    const curIndex = nodes.findIndex(v => v.id === taskId)
    if (curIndex === -1) {
      return
    }
    let curApproveNodeName = nodes[curIndex].name
    let hasRecalculateBefor = nodes.some((n, index) => n.config?.type === 'recalculate' && index < curIndex)
    const nextRecalculateIndex = nodes.findIndex((n, index) => n.config?.type === 'recalculate' && index > curIndex)
    let endIndex = nextRecalculateIndex < 0 ? nodes.length - 1 : nextRecalculateIndex

    const canSignNodes = nodes
      .map((item, index) => {
        //当前节点与最后节点 不能后加签
        if (index === nodes.length - 1 || index === curIndex) {
          Object.assign(item, { forbidAftAddNode: true })
        }
        return item
      })
      .filter((item, index) => (
        index >= curIndex &&
        index <= endIndex &&                                                   //当前节点 ~ 未执行的重算节点 之间的节点
        ['normal', 'countersign'].includes(item.type) &&                       //普通审批节，可过滤掉ebot
        !['recalculate', 'review', 'summary'].includes(item.config?.type) &&   //过滤重算/复核
        !['send', 'receive'].includes(item.expressConfig?.type) &&             //过滤 寄送 收单
        (typeof item.config.isNeedCashierNode == 'undefined' || item.config.isNeedCashierNode) //过滤关闭的出纳节点
      ))

    //若单据当前节点不是可加签节点，则默认选择节点组件为空；否则默认选中当前节点
    if (canSignNodes.length > 0) {
      this.setState({
        curApproveNodeName,
        hasRecalculateBefor,
        canSignNodes,
        currSingNodeId: canSignNodes[0].id === taskId ? canSignNodes[0] : {}
      })
    }
  }
  handleModalClose() {
    this.props.layer.emitCancel()
  }

  handleModalSave = () => {
    const { selectedStaffList, currSingNodeId } = this.state
    if (!currSingNodeId?.id) {
      showMessage.error(i18n.get('请选择需要加签审批的节点'))
      return
    }
    if (!!selectedStaffList.length) {
      this.fetcAddNodeAction()
    } else {
      showMessage.error(i18n.get('请选择员工'))
      return
    }
  }
  fetcAddNodeAction = async () => {
    const { id } = this.props
    const options = await this.getResult()
    const data = {
      id,
      action: 'addnode',
      ...options,
    }
    try {
      const res = await adminOperateNode(data)
      if (res?.id) {
        showMessage.success(i18n.get('操作成功'))
        this.props.layer.emitOk()
      } else {
        showMessage.error(res.errorMessage)
      }
      return res
    } catch (e) {
      return showMessage.error(e)
    }
  }

  handleSelectStaffs = () => {
    let { selectedStaffListIds } = this.state
    api
      .open('@layout:SelectStaffsModal', {
        checkedList: [{ type: 'department-member', multiple: true, checkedKeys: selectedStaffListIds }],
        isVisibilityStaffs: true,
        isNewContactList: window?.PLATFORMINFO?.isNewContactList
      })
      .then(data => {
        const { checkedList } = data
        let selectedStaffList = checkedList.find(o => o.type === 'department-member').checkedData
        let selectedStaffListIds = checkedList.find(o => o.type === 'department-member').checkedKeys
        this.setState({ selectedStaffListIds, selectedStaffList })
      })
  }

  handleTagChange = (data, deleteItem) => {
    let { selectedStaffListIds, selectedStaffList } = this.state
    let ids = selectedStaffListIds.slice()
    let dataList = selectedStaffList.slice()
    remove(ids, staffId => staffId === deleteItem.id)
    remove(dataList, staff => staff.id === deleteItem.id)
    this.setState({
      selectedStaffListIds: ids,
      selectedStaffList: dataList
    })
  }

  handleRadioChange(e) {
    this.setState({ type: e.target.value })
  }

  handlePolicyChange = e => {
    this.setState({ policyType: e.target.value })
  }
  //选中加签节点变化时 若禁止后加签 需自动改变加签类型
  handleChangeNodes = (value, item) => {
    const { type } = this.state
    const selectNode = item.props?.data
    const forbidAftAddNode = selectNode.forbidAftAddNode || selectNode.type === 'countersign'
    this.setState({
      currSingNodeId: selectNode,
      type: forbidAftAddNode ? 'PRE_ADD_NODE' : type
    })
  }
  handleUploading = isLoading => {
    this.setState({ isLoading })
  }

  handleAttactFinshed = attachments => {
    this.setState({ attachments })
  }
  //表格查询没有join.standardNumCode时 币种只有code 需要补充币种名称
  fixCurrency(moneys) {
    if (moneys && Array.isArray(moneys)) {
      let list = api.getState()['@common'].getCurrencyAll?.slice()
      const result = moneys.map((line) => {
        if (typeof line?.standardNumCode === 'string') {
          let name = list?.find(i => i?.numCode === line.standardNumCode)
          Object.assign(line, { standardNumCode: name })
        }
        return line
      })
      return result
    }

    return moneys
  }
  renderSingleRiskDetail = () => {
    let { riskData } = this.props
    let riskNum = get(riskData, 'value.moneyCount')
    const riskWarning = get(riskData, 'value.riskWarning', [])
    const existBudgetRisk = riskWarning.find(v => v.type === 'budget' && v.messages.length > 0) // todo zhb  看结构，确定层级和字段名
    if (!existBudgetRisk && !riskNum) return null
    const riskLength = get(existBudgetRisk, 'messages.length', 0)
    return (
      <Fragment>
        {
          !!existBudgetRisk || !!riskNum ? <FilledTipsWarning className="bill-icon" /> :null
        }
        {!!existBudgetRisk && (
          <WarningAlert
            status={RiskStatus.Warning}
            text={
              riskLength
                ? i18n.get('单据有{__k0}处「预算」风险，请注意', { __k0: riskLength })
                : i18n.get('所占预算超标，请注意')
            }
          />
        )}
        {!!riskNum && (
          <WarningAlert status={RiskStatus.Warning} text={i18n.get('有 x 处费用标准超标，请注意', { riskNum })} />
        )}
      </Fragment>
    )
  }

  render() {
    let { type, policyType, selectedStaffList, canSignNodes, currSingNodeId, curApproveNodeName, hasRecalculateBefor } = this.state
    let { flowData, form, riskData, title } = this.props
    const { getFieldDecorator, getFieldValue } = form
    const desc = i18n.get('当此单据你无法作出审批决策，或者需要他人辅助审批决策时，可通过「加签审批」来进行处理。')
    const forbidAftAddNode = currSingNodeId.forbidAftAddNode || currSingNodeId.type === 'countersign'
    return (
      <div className={styles['AdminAddNodeModal']}>
        <div className="modal-header" style={{ borderBottom: 0 }}>
          <div className="flex">{title}</div>
          <Icon className="cross-icon" type="cross" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="modal-content">
          <div className="title">
            {desc}
          </div>
          <div className="agree-risk-warning">
            {riskData && this.renderSingleRiskDetail()}
          </div>
          <MoneyView datas={flowData} fixCurrency={this.fixCurrency} />
          <div className="content">
            <div className="item-title">{i18n.get('请选择加签人：')}</div>
            <TagSelector
              value={selectedStaffList}
              className="tag-selector"
              placeholder={i18n.get('请选择审批人')}
              onClick={this.handleSelectStaffs}
              onChange={this.handleTagChange}
            />
            <div className="tip">{i18n.get('请谨慎选择，确定后即不可修改')}</div>
            {selectedStaffList.length > 1 && (
              <RadioGroup
                className="radio-group"
                onChange={this.handlePolicyChange.bind(this)}
                defaultValue={policyType}
              >
                <Radio className="item" value="ALL">
                  {i18n.get('需所有审批人都同意才算通过')}
                </Radio>
                <Radio className="item" value="ANY">
                  {i18n.get('任意审批人同意即认为通过')}
                </Radio>
              </RadioGroup>
            )}

            <div className="item-title">{i18n.get('请选择需要加签审批的节点')}:</div>
            <Select
              className="single_select"
              value={currSingNodeId?.id}
              size="large"
              placeholder={i18n.get('请选择需要加签审批的节点')}
              showSearch
              optionFilterProp={'title'}
              onChange={this.handleChangeNodes}>
              {canSignNodes?.length && canSignNodes.map((line) => {
                return (
                  <Option key={line.id} value={line.id} title={line.name} data={line}>
                    {line.name}
                  </Option>
                )
              })}
            </Select>
            {hasRecalculateBefor && <div className="tip">{i18n.get(`因当前审批节点【{__k0}】之前存在重算节点，后续如用户驳回至重算节点之前时，则当前的加签审批无效，请知晓！`, { __k0: curApproveNodeName })}</div>}
          </div>
          <div className="line" />
          <div className="trabster-group">
            <div className="fs-14 fw-500 mb-8">{i18n.get('加签方式')}</div>
            <RadioGroup className="radio-group" onChange={this.handleRadioChange.bind(this)} value={type}>
              <Radio className="item" value="PRE_ADD_NODE" disabled={currSingNodeId.forbidBeforeAddNode}>
                {i18n.get(`TA在【{__k0}】节点之前审批本单据（前加签）`, { __k0: currSingNodeId?.name })}
              </Radio>
              <Radio className="item" value="AFT_ADD_NODE" disabled={forbidAftAddNode}>
                {i18n.get(`在【{__k0}】节点同意之后，还需要TA审批单据（后加签）`, { __k0: currSingNodeId?.name })}
                <Tooltip placement="bottom" title={i18n.get('不支持后加签的场景为：当前节点, 出纳节点，会签节点以及普通审批节点配置了不可后加签的节点')}>
                  <span>
                    <EkbIcon name="EDico-help" style={{ width: 14, height: 14 }} />
                  </span>
                </Tooltip>
              </Radio>
            </RadioGroup>
            </div>
            <ApprovalComments 
              form={this.props.form} 
              bus = {this.bus}
              type = {'COUNTERSIGN'}
            />
            <div className="line" />
            <FormItem  className="trabster-group">
             <div className="fs-14 fw-500 mb-8">{i18n.get('附件')}</div>
              <AttachmentComponent onUploading={this.handleUploading} onFinshed={this.handleAttactFinshed} suffixesPath='APPROVE' />
            </FormItem>
        </div>
        <div className="add-node-footer mt-24">
          <Button type="primary" className="mr-8" onClick={this.handleModalSave}>
            {i18n.get('确定')}
          </Button>
          <Button type="ghost" onClick={this.handleModalClose.bind(this)}>
            {i18n.get('取消')}
          </Button>
        </div>
      </div>
    )
  }
}
