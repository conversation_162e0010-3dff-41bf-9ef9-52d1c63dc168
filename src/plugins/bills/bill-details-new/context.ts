import React from "react";

export interface BillDetailsV2ContextType {
  currentDataSource: {
    backlog: {
      id: string
      flowId: any
    }
    buttons: any
  } | null
  goNext: () => void
  goPrev: () => void
  dataSource: any[]
  disabledUpDown: ('prev' | 'next')[]
}

const BillDetailsV2Context = React.createContext<BillDetailsV2ContextType>({
  currentDataSource: null,
  goNext: () => {},
  goPrev: () => {},
  dataSource: [],
  disabledUpDown: [],
})

export const BillDetailsV2Provider = BillDetailsV2Context.Provider

export const useBillDetailsV2 = () => {
  return React.useContext(BillDetailsV2Context)
}