import React, { PureComponent } from 'react'
import { Icon, Collapse, Input } from 'antd'
import classNames from 'classnames'
import { debounce } from 'lodash'
import Highlighter from 'react-highlight-words'
import { isOpenTemplateModal } from '../bills.action'
import { app as api } from '@ekuaibao/whispered'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { fnFilterSpecification, getSpecificationName } from "../util/billUtils";
import AIChatEntry from '../../../components/AIChatEntry'
const { Panel } = Collapse
const { Search } = Input
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const styles = require('./changeTemplateModal.module.less')
const { EnhanceModal } = require('@ekuaibao/enhance-layer-manager')
const getSpecificationIconByName = api.require<any>('@elements/specificationIcon')

export interface Props {
  layer: any
  specificationGroups: any[]
  currentSpecification?: any
  overrideGetResult: any
}

export interface State {
  searchedSpecificationGroups: any[]
  searchKey: string
  activeKeys: string[]
}

@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class ChangeTemplateModal extends PureComponent<Props, State> {
  result: any
  constructor(props: Props) {
    super(props)
    this.getResult = this.getResult.bind(this)
    props.overrideGetResult(this.getResult)
    this.result = props.currentSpecification

    const filterSpecificactionGroup = fnFilterSpecification(props.specificationGroups)
      .filter(group => {
        group.specifications = group.specifications.filter(v => {
          const isActive = v => v.type !== 'settlement' && v.type !== 'reconciliation'
          if (!this.result || !this.result?.id?.includes('system:对账单')) {
            return isActive && !v.id.includes('system:对账单')
          }
          return isActive
        })
        return group
      })
      .filter(v => v.specifications.length)

    this.state = {
      searchedSpecificationGroups: [...filterSpecificactionGroup],
      searchKey: '',
      activeKeys: this.getSpecificationGroupIds() || []
    }
  }

  componentWillUnmount() {
    api.dispatch(isOpenTemplateModal({ isOpen: false }))
  }

  getSpecificationGroupIds = () => {
    const groupIds: string[] = []
    this.props.specificationGroups.forEach(group => {
      groupIds.push(group.id)
    })
    return groupIds
  }
  getResult() {
    return this.result
  }

  handleModalClose() {
    this.props.layer.emitCancel()
  }

  handleSelect = (item: any) => {
    this.result = item
    startOpenFlowPerformanceStatistics()
    this.props.layer.emitOk()
  }

  handleSearch = (value: string) => {
    const searchedSpecificationGroups = fnFilterSpecification(this.props.specificationGroups)
    // 搜索到的结果
    const searchedRes = [] as any
    // 精确匹配到的模版 id
    const searchedSpecificationIds: string[] = []
    // 过滤出需要展示的模版分组
    const filterGroup: string[] = []
    searchedSpecificationGroups.forEach(group => {
      if (group.name.includes(value)) {
        searchedRes.push(group)
        filterGroup.push(group.id)
      } else {
        group.specifications.forEach((item: any) => {
          const name = getSpecificationName(item)
          if (name.includes(value)) {
            filterGroup.push(group.id)
            searchedSpecificationIds.push(item.id)
          }
        })
      }
    })
    if (searchedSpecificationIds.length) {
      const specificationGroupsList = this.props.specificationGroups
      const notSearchedResList = specificationGroupsList.filter(
        item => !searchedRes.some((searchedResItem: any) => searchedResItem === item)
      )
      const SearchedResList = notSearchedResList.map((group: any) => {
        return {
          ...group,
          specifications: group.specifications.filter((item: any) => {
            const name = getSpecificationName(item)
            return name.includes(value)
          })
        }
      })
      this.setState({
        searchedSpecificationGroups: [...SearchedResList, ...searchedRes],
        activeKeys: filterGroup
      })
    } else {
      this.setState({ searchedSpecificationGroups: searchedRes, activeKeys: filterGroup })
    }
  }

  handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchKey = e.currentTarget.value
    this.setState({ searchKey })
    if (searchKey) {
      debounce((searchKey: string) => this.handleSearch(searchKey), 300)
    } else {
      this.setState({
        searchedSpecificationGroups: [...this.props.specificationGroups],
        activeKeys: this.getSpecificationGroupIds()
      })
    }
  }

  handleClear = () => {
    this.setState({
      searchKey: '',
      searchedSpecificationGroups: [...this.props.specificationGroups],
      activeKeys: this.getSpecificationGroupIds()
    })
  }

  renderHighLighter = (name: string) => (
    <Highlighter
      highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
      searchWords={[this.state.searchKey]}
      textToHighlight={name}
      autoEscape={true}
    />
  )

  switchPanel = (key: string[]) => {
    this.setState({ activeKeys: key })
  }

  renderGroup(item: any, index: number) {
    const { specifications = [] } = item
    const { currentSpecification } = this.props
    if (specifications.length === 0) {
      return null
    }
    const groupName = i18n.currentLocale === 'en-US' && item.enName ? item.enName : item.name

    return (
      <div key={index} className="collapse-wrapper">
        <Collapse defaultActiveKey={[...item.id]} activeKey={this.state.activeKeys} onChange={this.switchPanel}>
          <Panel header={this.renderHighLighter(groupName)} key={item.id}>
            {specifications.map((o: any, index: number) => {
              const IconCompo = getSpecificationIconByName(o.icon)
              const name = getSpecificationName(o)
              return (
                <div
                  className={`children-parent ${currentSpecification && o.id === currentSpecification.id ? 'children-parent-active' : ''
                    }`}
                  key={index}
                  onClick={() => {
                    this.handleSelect(o)
                  }}
                >
                  <IconCompo className="icon icon-my" style={{ color: o.color }} fontSize={18} />
                  <div className="children">{this.renderHighLighter(name)}</div>
                </div>
              )
            })}
          </Panel>
        </Collapse>
      </div>
    )
  }

  render() {
    const { searchedSpecificationGroups = [], searchKey } = this.state
    let hasTemp = false

    return (
      <div id={'changeTemplateModal'} className={styles['change-template-modal-style']}>
        <div className="modal-header-new">
          <div className="title flex-1">{i18n.get('选择单据模板')}</div>
          <OutlinedTipsClose className="cross-icon" onClick={this.handleModalClose.bind(this)} />
        </div>
        <div className="search-template-wrapper">
          <Search
            placeholder={i18n.get('搜索')}
            value={searchKey}
            enterButton={i18n.get('搜索')}
            onSearch={this.handleSearch}
            onChange={this.handleChange}
          />
          <div className={classNames('clear', { hidden: !searchKey })} onClick={this.handleClear}>
            <Icon type="cross-circle-o" />
          </div>
        </div>
        <AIChatEntry hasRight={true} style={{margin: '0 8px 12px'}}/>
        <div className="modal-content-new">
          {searchedSpecificationGroups.map((item: any, index: number) => {
            const view = this.renderGroup(item, index)
            if (view && !hasTemp) {
              hasTemp = true
            }
            return view
          })}
          {!hasTemp && <div className="empty">{i18n.get('无可用模板')}</div>}
        </div>
      </div>
    )
  }
}
