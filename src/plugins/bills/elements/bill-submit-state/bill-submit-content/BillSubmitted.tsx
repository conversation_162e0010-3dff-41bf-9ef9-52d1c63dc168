/**
 *  Created by <PERSON><PERSON><PERSON><PERSON> on 2018/11/30 下午7:44.
 */
import React, { Fragment, useEffect, useState } from 'react'
import { get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import RequisitionBanner from '../../RequisitionBanner'
import styles from './BillSubmitted.module.less'
import moment from 'moment'

import { fnFormatTrips } from '../../../util/fnFormatTrips'
// @ts-ignore
import { Questionnaire, Button } from '@hose/eui'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import classnames from "classnames";
import { FilledTipsYes, FilledTipsClose } from '@hose/eui-icons'

const { questionnaireConfig } = api.require('@components/utils/questionnaireConfig')

interface Props {
  handleOk?: Function
  status?: string
  failText?: string
  failMsgs?: any[]
  isModify?: boolean
  riskData?: any
}

export function BillSubmittedWidthSuc(props: Props) {
  return RenderContent({ status: 'suc', ...props })
}

export function BillSubmittedWidthFail(props: Props) {
  const { failText, riskData, handleOk } = props
  const failMsgs = failText?.split('\n')?.filter(el => !!el)
  return failMsgs?.length > 1 ? renderMulFailsContent({ status: 'fail', failMsgs, ...props }) : RenderContent({ status: 'fail', ...props })
}

function renderMulFailsContent(props: Props) {
  const { handleOk, failText, failMsgs } = props
  const [url, className, btnText] = ['#EDico-plaint-circle', 'fail', i18n.get('立即修改')]

  return (
    <div className={styles['bill-submitted-fail-wrapper']}>
      <div className={`title-warning ${className}`}>
        <svg className="icon" aria-hidden="true">
          <use xlinkHref={url} />
        </svg>
        <span className='title-text'>{i18n.get('提示')}</span>
      </div>
      <div className="text">
        {failMsgs.map((t, idx) => {
          if (t?.includes('$TITLE$')) {
            return <div key={idx} className="dis-f"><span className="grow">{t?.split('$TITLE$')?.[1]}</span></div>
          }
          return !!t ? <div key={idx} className="dis-f"><span className="circle"/><span className="grow">{t}</span></div> : ''
        })}
      </div>
      <div className="footer">
        <div
          className="buttons"
          onClick={() => {
            handleOk()
          }}
        >
          {failText ? btnText : i18n.get('关闭')}
        </div>
      </div>

    </div>
  )
}

function RenderContent(props: Props) {
  const { status, handleOk, failText, params } = props
  const [trips, setTrips] = useState({})
  const [url, className, btnText] =
    status === 'suc'
      ? ['#EDico-check-circle', 'suc', i18n.get('知道了')]
      : ['#EDico-plaint-circle', 'fail', i18n.get('立即修改')]
  useEffect(() => {
    const isMallRole = api.getState()['@common'].mallRole?.mallRole === '0'
    if (params?.formType === 'requisition' && status === 'suc' && isMallRole) {
      (async () => {
        let trips = []
        if (params?.form?.u_行程规划) {
          const result = await api.invokeService('@bills:get:Specifications', params?.form?.specificationId)
          const components = result.items[0]?.components || []
          let field = components.find(oo => oo.field === 'u_行程规划')
          const list = params?.form?.u_行程规划 ?? []
          trips = await fnFormatTrips(field, list)
        } else if (params?.form?.trips) {
          trips = params?.form?.trips || []
        }
        const flightTrip = trips.find(
          el => el?.tripTypeId?.endsWith(':flight') && moment(el?.tripForm?.tripDate).add(1, 'd').isAfter(Date.now(), 'day')
        )
        const trainTrip = trips.find(
          el => el?.tripTypeId?.endsWith(':train') && moment(el?.tripForm?.tripDate).add(1, 'd').isAfter(Date.now(), 'day')
        )
        const hotelTrip = trips.find(
          el => el?.tripTypeId?.endsWith(':hotel') && moment(el?.tripForm?.tripDatePeriod?.end).add(1, 'd').isAfter(Date.now(), 'day')
        )
        setTrips({ flightTrip, trainTrip, hotelTrip})
      })()
    }
  }, [])
  let isShowBanner = trips?.flightTrip || trips?.trainTrip || trips?.hotelTrip

  return (
    <div className={styles['bill-submitted-wrapper']}>
      <div className="content">
        <div className="content-icon">
          {className === 'suc' ? (
            <FilledTipsYes style={{ color: 'var(--eui-function-success-500)' }} />
          ) : (
            <FilledTipsClose style={{ color: 'var(--eui-function-danger-500)' }} />
          )}
        </div>
        <div className="content-text">{renderText(props)}</div>
      </div>
      <div className="button">
        <Button
          category="primary"
          onClick={() => {
            !failText && initSurvey(props)
            handleOk()
          }}
        >
          {failText ? btnText : i18n.get('知道了')}
        </Button>
      </div>
      {isShowBanner && (
        <div className="banner">
          <p>{i18n.get('与你行程相关的机酒推荐：')}</p>
          <RequisitionBanner trips={trips} isTicketParam={true} />
        </div>
      )}
    </div>
  )
}
function renderFailReason(text) {
  const failTextAndReason = text?.split('\n') || ['']
  return failTextAndReason.map((t, idx) => <div>{t}</div>)
}
async function initSurvey(props) {
  const result = await api.invokeService('@bills:get:Specifications', props?.params?.form?.specificationId)
  let type = result?.items[0]?.type
  type = billTypeMap()[type || 'expense']
  Questionnaire.initSurvey({
    sid: questionnaireConfig?.submit?.sid,
    channelId: questionnaireConfig?.submit?.channelId,
    width: questionnaireConfig?.submit?.width,
    externalUserId: api.getState()['@common'].userinfo?.data?.staff?.userId,
    externalCompanyId: api.getState()['@common'].userinfo?.data?.staff?.corporationId,
    parameters: {
      name: result?.items[0]?.name,
      type
    }
  })
}

function renderText(props: Props) {
  const { status, failText, isModify } = props
  return (
    <Fragment>
      {status === 'suc' ? (
        <Fragment>
          <div className='title'>{isModify ? i18n.get('保存成功') : i18n.get('提交成功')}</div>
          <div className='text'>{i18n.get('请耐心等待审批')}</div>
        </Fragment>
      ) : (
        <Fragment>
          {failText ? (
            <>{renderFailReason(failText)}</>
          ) : (
            <Fragment>
              <div>{i18n.get('网络错误，请稍后重试')}</div>
            </Fragment>
          )}
        </Fragment>
      )}
    </Fragment>
  )
}

/**
 * 针对存在关联单据以及 messageV2 字段的数据进行渲染风险的代码
 * @param props
 * @constructor
 */
const FailWithRisk: React.FC<{
  reason: string,
  riskData?: {
    value: any,
    singleInvoiceRiskWarning: any[]
  },
  onConfirm?: Function
}> = props => {
  const {
    reason,
    riskData,
    onConfirm
  } = props

  const riskInfoList = React.useMemo(() => {
    const riskNameAndInvoiceRiskDataMap: any = {}
    if (!riskData) {
      return []
    }
    const { singleInvoiceRiskWarning } = riskData

    singleInvoiceRiskWarning.forEach((item) => {
      const {
        invoiceId,
        invoiceMsg,
        riskWarning,
        pathValueId
      } = item
      riskWarning?.forEach?.(name => {
        const list = riskNameAndInvoiceRiskDataMap[name] ?? []
        const relatedFlows = item.relatedFlows?.[name] ?? []
        riskNameAndInvoiceRiskDataMap[name] = list.concat({
          invoiceMsg,
          invoiceId,
          pathValueId,
          relatedFlows
        })
      })
    })

    return Object.entries(riskNameAndInvoiceRiskDataMap).filter(([key]) => key.includes('重复') || key.includes('连号'))
  }, [riskData])

  return (
    <div className={classnames('bill-submitted-modal-v2', styles['bill-submitted-wrapper'])}>
      <div className="tips-title">
        <svg className="icon" aria-hidden="true">
          <use xlinkHref="#EDico-plaint-circle" />
        </svg>
        <div className={classnames('reason')}>
          {reason}
        </div>
      </div>
      <div className="risk-content">
        {riskInfoList.map(([name, value]) => (
          <div className="risk-item">
            <div className="risk-item-header">
              {name}
            </div>
            {value.map((risk: any) => (
              <div className="risk-item-content" key={risk.invoiceMsg}>
                <div className="flex-container">
                  <div className="risk-item-label">
                    {i18n.get('风险信息')}:
                  </div>
                  <div className="flex-1 risk-item-info">
                    {risk.invoiceMsg}
                  </div>
                </div>
                {!!risk.invoiceRiskExplainContent && (
                  <div className="flex-container">
                    <div className="risk-item-label">
                      {i18n.get('风险原因')}:
                    </div>
                    <div className="flex-1 risk-item-info">
                      {risk.invoiceRiskExplainContent}
                    </div>
                  </div>
                )}
                <div className="flex-container">
                  <div className="risk-item-label">
                    {i18n.get('相关单据')}:
                  </div>
                  <div className="flex-1 risk-item-info">
                    {risk.relatedFlows.map(item => `${item.flowCode}${item.invoiceNum ? `(${item.invoiceNum})` : ''}`).join('，')}
                  </div>
                </div>

              </div>
            ))}
          </div>
        ))
        }
      </div>

      <div className="footer">
        <div
          className="confirm-button"
          onClick={onConfirm as any}
        >
          {i18n.get('立即修改')}
        </div>
      </div>
    </div>
  )
}
