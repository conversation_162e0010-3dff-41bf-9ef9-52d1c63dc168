/**************************************************
 * Created by nany<PERSON><PERSON>feng on 10/07/2017 17:41.
 **************************************************/
import React, { PureComponent } from 'react'
import BillInfoEditable from './BillInfoEditable'
import FlowPlanReadonly from './FlowPlanReadonly'
import ExpressReadonly from './ExpressReadonly'
import RightPartHeader from './RightPartHeader'
import ETabs from '../../../../../elements/ETabs'
import { EnhanceConnect } from '@ekuaibao/store'
import { getBudgetList } from '../../../bills.action'
import { app as api } from '@ekuaibao/whispered'
import styles from './newSearchBillInfo.module.less'
import { get, cloneDeep } from 'lodash'
import { Icon } from 'antd'
import { showModal } from '@ekuaibao/show-util'
import MessageCenter from '@ekuaibao/messagecenter'
import MoreInfo from '../billMore/MoreInfo'
import { getBoolVariation } from '../../../../../lib/featbit'
import { AIFillFormTracker, FillMode } from '../../../../../lib/aiFillFormTracker'
import { uuid } from '@ekuaibao/helpers'
import classNames from 'classnames'
import BillTopOperator from '../billInfo/BillTopOperator'
@EnhanceConnect(
  state => ({
    Express: state['@common'].powers.Express,
    remunerationBatchField: state['@remuneration'].remunerationBatchField,
    expressNums: state['@audit'].expressNums,
    staffDisplayConfigField: state['@common'].organizationConfig?.staffDisplayConfig?.[1] || ''
  }),
  { getBudgetList }
)
export default class BillInfoEditableContainer extends PureComponent {
  constructor(props) {
    super(props)
    this.ref = React.createRef()
    this.state = {
      activeKey: 'billInfo',
      budgetMsg: '',
      budgetList: [],
      remunerationTabs: null,
      tabIconState: {},
      isFullscreen:false
    }
    this.__DataSource = props.dataSource
    this.bus = props.bus || new MessageCenter()
    this.newFormId = `NEW_ID_${uuid(10)}`
    this.bus.newFormId = this.newFormId
  }

  componentWillMount() {
    try {
      console.log('BillInfoEditableContainer componentWillMount', this.props)
      const template = this.props?.dataSource?.id ? this.props?.dataSource?.form?.specificationId : this.props?.dataSource?.currentSpecification
      AIFillFormTracker.trackAIFillFormStart({
        form_id: this.props?.dataSource?.id || this.newFormId,
        form_template_id: template?.id,
        form_template_name: template?.name,
        form_type: this.props?.dataSource?.formType,
        form_create_time: this.props.dataSource.createTime || Date.now(),
        fill_mode: this.props?.dataSource?.state === 'new' ? FillMode.CREATE : FillMode.EDITDRAFT,
      })
    } catch (error) {
      console.error('AIFillFormTracker.trackAIFillFormStart error', error)
    }
    const { dataSource, bus } = this.props
    this.setBudgetValue(dataSource)
    dataSource.id && api.invokeService('@audit:get:express:nums', dataSource.id)
    api.invokeService('@remuneration:get:remuneration:config:setting')
    api.watch('bill:specification:change', this.handleSpecificationChange)
    api.watch('remuneration:creare:details', this.checkRemunerationDetails)
    api.on('tabs:show:otherIcon', this.handleChangeIconState)
  }

  componentDidMount() {
    const { isNewSearchInfo = false } = this.props
    if (isNewSearchInfo && !!this.ref.current) {
      const tabLine = this.ref.current.getElementsByClassName('ant-tabs-ink-bar')
      if (tabLine && tabLine.length) {
        const child = document.createElement('div')
        child.className = 'new-search-tab-line-child'
        tabLine[0].appendChild(child)
      }
    }
    api.invokeService('@user-info:get:payee:config:check')
  }

  componentWillReceiveProps(nextProp) {
    if (this.props.dataSource !== nextProp.dataSource) {
      const { dataSource } = nextProp
      this.setBudgetValue(dataSource)
      this.__DataSource = dataSource
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    api.un('bill:specification:change', this.handleSpecificationChange)
    api.un('remuneration:creare:details', this.checkRemunerationDetails)
    api.un('tabs:show:otherIcon', this.handleChangeIconState)
  }

  handleChangeIconState = data => {
    const { tabKey, typeMapIcon } = data
    const { tabIconState, remunerationTabs } = this.state
    const objIcon = get(tabIconState, `${tabKey}`)
    if (objIcon === typeMapIcon) {
      return
    }
    const newIconState = cloneDeep(tabIconState)
    const newTabs = cloneDeep(remunerationTabs)
    newTabs['iconType'] = typeMapIcon
    this.setState({ remunerationTabs: newTabs, tabIconState: newIconState })
  }

  handleSpecificationChange = data => {
    const { dataSource } = data
    this.__DataSource = cloneDeep(dataSource)
    this.checkRemunerationDetails(dataSource)
  }

  createRemunerationTabs = dataSource => {
    let { bus, remunerationBatchField } = this.props
    api
      .invokeService('@remuneration:creat:tabs', {
        bus,
        dataSource,
        remunerationBatchField
      })
      .then(res => {
        const { value } = res
        this.setState({ remunerationTabs: value })
      })
  }
  checkRemunerationDetails = dataSource => {
    //检查是否酬金申报，是：自动生成明细
    let { bus, remunerationBatchField } = this.props
    api.invokeService('@remuneration:init:details', {
      bus,
      dataSource: dataSource ? dataSource : this.props.dataSource,
      remunerationBatchField
    })
  }
  setBudgetValue = (dataSource = {}) => {
    const whiteList = ['approving', 'paying', 'sending', 'receiving']
    if (dataSource.formType === 'expense' && !!~whiteList.findIndex(v => v === dataSource.state)) {
      const ID = dataSource.id
      this.getBudgetMsg(ID)
      this.getBudgetList(ID)
    }
  }

  getBudgetMsg = expenseID => {
    let _this = this
    api.invokeService('@common:get:budgetMsg', {
      ids: expenseID,
      callback(items) {
        if (items) {
          _this.setState({
            budgetMsg: items[0]
          })
        }
      }
    })
  }

  getBudgetList = expenseId => {
    let { getBudgetList } = this.props
    getBudgetList(expenseId).then(action => {
      if (action.error) return
      let data = action.payload
      this.setState({
        budgetList: data.items
      })
    })
  }

  handleClickTab = (activeKey = 'billInfo') => {
    const { bus, remunerationBatchField } = this.props
    const batchId = get(this.__DataSource, `form.${remunerationBatchField}.id`)
    if (activeKey === 'remuneration' && !batchId) {
      api.invokeService('@remuneration:first:save:bill', { bus })
      return false
    }
    this.changeTab(activeKey)
  }

  changeTab = (activeKey = 'billInfo', flowlogType = '') => {
    this.setState({ activeKey, flowlogType })
  }
  _handleHistoryVersionClick = item => {
    const {
      dataSource: { id }
    } = this.props
    api.open('@bills:BillHistoryVersionModal', { item, flowId: id })
  }

  onFullscreen = () => {
    this.setState({ isFullscreen: !this.state.isFullscreen })
  }

  renderNewLayoutForBill = (billContent) => {
    const { billFooter, mode } = this.props
    return (
      <div className={classNames(styles['new-layout-container'], {
        [styles['new-layout-container-table']]: mode === 'table'
      })}>
        {this.renderHeader()}
        {billContent}
        {billFooter}
      </div>
    )
  }

  render4New = () => {
    let { bus, dataSource, riskData, inModal, creatStyle = '',layer } = this.props
    dataSource = this.__DataSource
    let contentView = this.renderNewLayoutForBill(
      <BillInfoEditable
        creatStyle={creatStyle}
        bus={this.bus || bus}
        dataSource={dataSource}
        riskData={riskData}
        singleRiskData={riskData}
        inModal={inModal}
        layer = {layer}
        newFormId={this.newFormId}
      />
    )
    return creatStyle === 'new_modal_style' ? contentView : <div className="dis-f flex-110 ovr-y-h">
    {contentView}
    
    {getBoolVariation('aprd-5394-ai-chat') && <MoreInfo {...this.props} bus={bus} billDetails={dataSource} riskData={riskData}/> }
</div> 
  }

  render4Draft = () => {
    const { bus, riskData, inModal, creatStyle = '' } = this.props
    const dataSource = this.__DataSource
    const contentView = this.renderNewLayoutForBill(
      <BillInfoEditable
        creatStyle={creatStyle}
        bus={this.bus || bus}
        dataSource={dataSource}
        riskData={riskData}
        singleRiskData={riskData}
        inModal={inModal}
      />
    )
    return creatStyle === 'new_modal_style' ? contentView : (
      <div className="dis-f flex-110 ovr-y-h">
          {contentView}
          <MoreInfo className={styles['bill-more-info']} {...this.props} bus={bus} billDetails={dataSource} riskData={riskData}/>
      </div>
    )
  }

  render4Rejected = () => {
    const {
      bus,
      dataSource,
      expressNums,
      Express,
      riskData,
      staffDisplayConfigField,
      inModal,
    } = this.props
    const { activeKey, flowlogType } = this.state
    const { state, plan } = dataSource
    let billState = state === 'draft' ? (plan ? 'retract' : 'draft') : state
    let isRetract = billState === 'retract'
    const tabSource = [
      {
        tab: i18n.get('单据详情'),
        children: (
          <BillInfoEditable
            bus={bus}
            dataSource={dataSource}
            changeTabAction={this.changeTab}
            riskData={riskData}
            isRetract={isRetract}
            singleRiskData={riskData}
            inModal={inModal}
            layer={this.props.layer}
          />
        ),
        key: 'billInfo'
      }
    ]
    if (!getBoolVariation('off_old_flow_tab')) {
      tabSource.push({
        tab: i18n.get('审批流程'),
        children: (
          <FlowPlanReadonly
            bus={bus}
            versionItemAction={this._handleHistoryVersionClick}
            dataSource={dataSource}
            flowlogType={flowlogType}
            staffDisplayConfigField={staffDisplayConfigField}
          />
        ),
        key: 'flowPlan'
      })
    }
    const isHaveNode =
      dataSource.plan &&
      dataSource.plan.nodes.find(item => {
        return item.expressConfig && item.expressConfig.type && item.expressConfig.type === 'send'
      })
    if (Express && isHaveNode) {
      tabSource.push({
        tab: i18n.get('寄送信息'),
        children: <ExpressReadonly bus={bus} expressNums={expressNums} flowId={dataSource.id} />,
        key: 'express'
      })
    }
    return (
      <div
        className={`dis-f flex-1 ovr-y-h ${styles['e-tabs-wrapper']} ${
          styles['bill-info-edit-container']
        } ${styles['new-search-bill-info']}`}
        ref={this.ref}
      >
        {
          this.renderNewLayoutForBill(
            <ETabs
              type={'line'}
              activeKey={activeKey}
              onTabClick={this.handleClickTab}
              dataSource={tabSource}
              isHoseEUI
              tabBarStyle={{ marginBottom: 0, display: tabSource?.length > 1 ? '' : 'none' }}
              isNewSearchInfo={true}
            />
          )
        }
        <MoreInfo className={styles['bill-more-info']} { ...this.props} isActiveInfo = {activeKey === 'billInfo'} bus={bus} billDetails={dataSource} riskData={riskData}/>
      </div>
    )
  }

  renderBody = state => {
    const renderMap = {
      new: this.render4New,
      draft: this.render4Draft,
      retract: this.render4Rejected,
      rejected: this.render4Rejected
    }
    return renderMap[state](state)
  }

  handleCancel = () => {
    const { closeDrawer, scanViewData } = this.props
    if (this.bus?.__CURRENT_IS_CHANGED) {
      showModal.confirm({
        title: i18n.get('尚未保存，是否确认返回？'),
        onOk: () => {
          closeDrawer && closeDrawer()
          scanViewData?.fn && scanViewData?.fn()
        }
      })
    } else {
      closeDrawer && closeDrawer()
      scanViewData?.fn && scanViewData?.fn()
    }
  }


  renderHeader = () => {
    const { dataSource, bus, hideHeader, hiddenCancel = false,showHeaderClose, showFullScreenDrawer } = this.props
    const { state, plan } = dataSource
    let billState = state === 'draft' ? (plan ? 'retract' : 'draft') : state
    if (hideHeader) {
      return null
    }
    return dataSource?.openFrom === 'permit-form' ? (
        <div className={styles['modal-header-new']}>
          <div className="title">{dataSource?.title}</div>
          {!!!hiddenCancel && <Icon className="cross-icon" type="cross" onClick={this.handleCancel} />}
        </div>
      ) : (
        <RightPartHeader
          showHeaderClose={showHeaderClose}
          showFullScreenDrawer={showFullScreenDrawer}
          layer={this.props.layer}
          bus={bus}
          dataSource={dataSource}
          state={billState} />
      )
  }

  render() {
    const { dataSource, mode, layer, showFullScreenDrawer, showHeaderClose, isModal, showUpDown } = this.props
    const { state, plan } = dataSource
    let billState = state === 'draft' ? (plan ? 'retract' : 'draft') : state
    return (
      <div
        className={classNames('dis-f fd-c flex-1 ovr-y-h',
          styles['bill-info-editable-container'],
          mode === 'list' ? styles[`bill-info-editable-container-list`] : styles[`bill-info-editable-container-table`]
        )}
      >
        {mode !== 'list' && !isModal && <BillTopOperator
          showFullScreenDrawer={showFullScreenDrawer}
          showHeaderClose={showHeaderClose}
          showUpDown={showUpDown}
          bus={this.bus}
          layer={layer}
        />}
        {this.renderBody(billState)}
      </div>
    )
  }
}
