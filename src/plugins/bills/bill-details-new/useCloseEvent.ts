import { useEffect, useRef } from "react"
import { ifCloseWhenClickBody } from '../../../lib/featbit/feat-switch'
import { ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER } from '../../../elements/configure/bill-drawer'
import { ILayerProps } from "@ekuaibao/enhance-layer-manager"


const useDrawerCloseEvent = (layer: ILayerProps['layer']) => {

  const containerElementRef = useRef(document.getElementById('layout-wrapper') || document.body);

  useEffect(() => {
    const handleClick = (e: any) => {
      if (!layer) {
        return;
      }
      if (!ifCloseWhenClickBody()) {
        return;
      }
      if (ELEMENTS_IDS_NOT_CLOSE_BILL_DRAWER.includes(e.target?.id)) {
        return;
      }
      if (layer) {
        layer.emitCancel();
      }
    };

    containerElementRef.current.addEventListener('click', handleClick, true);

    return () => {
      containerElementRef.current.removeEventListener('click', handleClick, true);
    };
  }, [layer]);
};

export default useDrawerCloseEvent