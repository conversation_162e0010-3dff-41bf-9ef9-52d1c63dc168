import styles from './table-view.module.less'

import React, { FC, useState, useRef, useEffect, useCallback } from 'react'

import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import { QuerySelect } from 'ekbc-query-builder'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { Resource } from '@ekuaibao/fetch'

import ETabs from '../../../../../elements/ETabs'
import LoanDataGrid, { LoanDataGridRef } from './table-view-data-grid'

const flowResource = new Resource('/api/flow/v1/flows')

interface IProps {
  size: any
  baseDataProperties: any
  openDetail: (id: string) => void
}

const TableView: FC<IProps> = props => {
  const { size, baseDataProperties, openDetail } = props
  const [activeKey, setActiveKey] = useState('applying')
  const bus = useRef<any>(new MessageCenter())
  const applyingRef = useRef<LoanDataGridRef>(null)

  const tabMenu = [
    {
      tab: i18n.get('申请中'),
      key: 'applying',
      children: <LoanDataGrid ref={applyingRef} size={size} baseDataProperties={baseDataProperties} tabKey="applying" bus={bus.current} />
    },
    {
      tab: i18n.get('待确认'),
      key: 'pending-confirmation',
      children: (
        <LoanDataGrid
          size={size}
          baseDataProperties={baseDataProperties}
          tabKey="pending-confirmation"
          bus={bus.current}
        />
      )
    },
    {
      tab: i18n.get('待还款'),
      key: 'pending-repayment',
      children: (
        <LoanDataGrid
          size={size}
          baseDataProperties={baseDataProperties}
          tabKey="pending-repayment"
          bus={bus.current}
        />
      )
    },
    {
      tab: i18n.get('已完成'),
      key: 'completed',
      children: (
        <LoanDataGrid size={size} baseDataProperties={baseDataProperties} tabKey="completed" bus={bus.current} />
      )
    }
  ]

  const handleTabChange = useCallback(
    data => {
      if (activeKey === 'applying') {
        handleLineLoanDetails({
          line: data,
          privilegeId: '',
          canActivate: false,
          dataSource: applyingRef.current?.getCurrentDataSource(),
          bus: bus.current,
        })
      } else {
        openDetail(data.id)
      }
    },
    [activeKey]
  )

  useEffect(() => {
    bus.current?.watch('table:row:click', handleTabChange)

    return () => {
      bus.current?.un('table:row:click', handleTabChange)
    }
  }, [activeKey])

  return (
    <div className={styles['loan-package-table-view']}>
      <ETabs
        type="line"
        activeKey={activeKey}
        onChange={(key: string) => {
          setActiveKey(key)
        }}
        dataSource={tabMenu}
        tabBarStyle={{ paddingTop: 8, width: '100%' }}
        isHoseEUI
      />
    </div>
  )
}

export default TableView

// 打开单据
function handleLineLoanDetails({
  line,
  privilegeId = '',
  canActivate = false,
  dataSource,
  bus,
}: {
  line: any
  privilegeId?: string
  canActivate?: boolean
  dataSource: any
  bus: MessageCenter
}) {
  const id = line.flowId ? line.flowId.id : line.id
  const params = { id: id, type: 'loan' }

  if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
    api.open('@bills:BillInfoDrawerV2', {
      currentId: id,
      flows: dataSource || [],
      closeAfterAction: true,
      bus: bus,
    })
    return
  }

  api.dispatch(getBacklogInfoById(params, privilegeId)).then(resp => {
    const flowId = resp.value
    api.open(
      '@bills:BillInfoPopup',
      {
        title: `${i18n.get(billTypeMap()[flowId.formType])}${i18n.get('详情')}`,
        backlog: { id: -1, flowId },
        privilegeId,
        bus: new MessageCenter(),
        canActivate,
        // add 评论立即刷新数据使用
        params: id,
        invokeService: '@audit:get:history-flow:info'
      },
      true
    )
  })
}

function getBacklogInfoById({ id }, privilegeId) {
  const { joinParams, selectEntity } = api.require('@lib/entity.join')
  const { fetchAttachment, fetchInvoice } = api.require('@lib/attachment-fetch')
  const fetchMutilValue = api.require('@lib/mutil-staff-fetch')

  const query = new QuerySelect()
    .filterBy(`id=="${id}"`)
    .select(`${selectEntity()}`)
    .value()

  const params = {
    id,
    flowManager: true,
    privilegeId,
    ...joinParams()
  }

  return {
    type: '@loan-manage/GET_BACKLOG_INFO_BY_ID',
    payload: flowResource
      .POST('/flowId/$id', query, params)
      .then(fetchAttachment)
      .then(fetchInvoice)
      .then(fetchMutilValue)
  }
}
