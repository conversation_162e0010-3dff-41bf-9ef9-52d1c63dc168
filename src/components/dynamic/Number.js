/**************************************************
 * Created by nanyuantingfeng on 30/06/2017 15:34.
 **************************************************/
import styles from './Number.module.less'
import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { InputNumber as AntdInputNumber } from 'antd'
import { InputNumber as EUIInputNumber } from '@hose/eui'
import classNames from 'classnames'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import fnPredefine4Number from '../utils/fnPredefine4Number'
import { fnDefineIsFormula } from '../../lib/misc'
import { isDisable } from '../utils/fnDisableComponent'
import { getPlaceholder } from '../utils/fnGetFieldLabel'
@EnhanceField({
  descriptor: {
    type: 'number'
  },
  validator: (field, props) => (rule, value, callback) => {
    let { label, min, max, dataType = {}, editable, scale: filedScale = 0 } = field
    let re = /^-?[0-9]+$/
    let { scale = 0 } = dataType
    if (filedScale) {
      scale = filedScale
    }
    if (scale) {
      re = new RegExp(`^-?(([1-9]\\d*)|0)(\\.\\d{1,${scale}})?$`)
    }
    if (value !== undefined) {
      let msg = scale ? i18n.get('please-number-format', { scale }) : i18n.get('请输入整数')
      if (!re.test(value)) return callback(msg)
      if (!editable && value * 1 > 10000000000) {
        return callback(i18n.get('cannot-be-greater', { label, max: 10000000000 }))
      }
      if (!editable && value * 1 < -10000000000) {
        return callback(i18n.get('cannot-be-less', { label, min: -10000000000 }))
      }
      if (editable && value * 1 > max * 1) {
        return callback(i18n.get('cannot-be-greater', { label, max }))
      }
      if (editable && value * 1 < min * 1) {
        return callback(i18n.get('cannot-be-less', { label, min }))
      }
      if (field.name === 'min' || field.name === 'minLength') {
        let fieldName = field.name === 'min' ? 'max' : 'maxLength'
        let max = props.form.getFieldValue(fieldName)
        if (max && value && value * 1 > max * 1) {
          return callback(i18n.get('最小值不能大于最大值'))
        }
      }
    }
    if (rule.level > 0) return callback()
    callback(required(field, value))
  },

  initialValue(props) {
    let { field, autoCalFields } = props
    let { defaultValue, field: fieldName } = field
    let isAuto = fnDefineIsFormula(fieldName, autoCalFields)
    if (isAuto && defaultValue?.type === 'constant') {
      return fnPredefine4Number(field)
    }
    if (!isAuto && defaultValue) return fnPredefine4Number(field)
    return undefined
  },
  wrapper: wrapper()
})
export default class Number extends PureComponent {
  onChange = value => {
    let { onChange, bus, getExpenseStandardItemsLength } = this.props
    value = value || value === 0 ? value + '' : ''
    onChange && onChange(value)
    bus && bus.emit('number:value:changed', value)
    getExpenseStandardItemsLength && setTimeout(getExpenseStandardItemsLength, 0)
  }

  handleBlur = e => {
    let { value } = e.target
    if (!value) return
    let { field } = this.props
    let scale = field.dataType && field.dataType.scale
    if (scale) {
      value = new Big(value).toFixed(scale)
      this.onChange(value)
    }
  }

  render() {
    let { field, value, useEUI } = this.props
    const placeholder = getPlaceholder(field)
    let {
      step,
      defaultValue,
      dataType = {},
      unit: unitFiled = ''
    } = field
    let unit = dataType.unit || unitFiled
    if (dataType.scale) {
      step = 1 / Math.pow(10, dataType.scale)
    }

    let attributes = {
      step,
      defaultValue: defaultValue * 1,
      value: value
    }

    if (defaultValue && defaultValue.value) {
      attributes.defaultValue = isNaN(defaultValue.value * 1) ? undefined : defaultValue.value
    }
    let disabled = isDisable(this.props)
    let numberClass = classNames('number-input', { 'with-unit': unit }, { 'ekb-number-disabled': disabled })
    return (
      <div className={styles.number_wrap}>
        {useEUI ? (
          <div className="number">
            <EUIInputNumber
              {...attributes}
              disabled={disabled}
              placeholder={placeholder}
              onChange={this.onChange}
              onBlur={this.handleBlur}
              className={numberClass}
              addonAfter={unit}
              data-testid={`field-number-${field.name}`}
            />
            </div>
        ) : (
          <div className="number">
            <AntdInputNumber
              {...attributes}
              size="large"
              disabled={disabled}
              placeholder={placeholder}
              onChange={this.onChange}
              onBlur={this.handleBlur}
              className={numberClass}
              data-testid={`field-number-${field.name}`}
            />
            {unit && <span className="unit-text">{unit}</span>}
          </div>
        )}
      </div>
    )
  }
}
