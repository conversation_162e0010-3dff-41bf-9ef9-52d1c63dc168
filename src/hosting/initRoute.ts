/**************************************
 * Created By LinK On 2020/6/28 18:32.
 **************************************/
import { filterFromUrl } from '../lib/lib-util'
import { localCacheGet, session } from '@ekuaibao/session-info'
import { app } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import { pageViewCacheKey } from '@ekuaibao/lib/lib/help'

import { trackFMP } from '../lib/utils'

/**
 * 登录时，跳转到用户上次登录时最后一次访问的路由
 */
export function initRoute({ noTrack = false } = {}) {
  // 取url中的noPathCache参数，如果有值，不做转向处理
  const noPathCache = filterFromUrl('noPathCache')

  // 取当前url中的hash值，如果是以下数组中的值，不做转向处理
  const locationHash = location.hash.slice(1)
  const blackList = ['/login', '/auth-check', '/selectEnterprise5', '/mc-error', '/shared-error']
  const shouldNotRedirect = !!~blackList.indexOf(locationHash)
  if (shouldNotRedirect || noPathCache) {
    if(noPathCache) {
      !noTrack && trackFMP(locationHash || '/new-homepage')
    }
    return
  }

  // 给Fetch.ekbCorpId赋值
  // 钉钉应用中不能通过这个方法给Fetch.ekbCorpId赋值
  if (!Fetch.ekbCorpId && (window.__PLANTFORM__ === 'APP' || window.__PLANTFORM__ === 'THIRDPARTY')) {
    initFetchEkbCorpId()
  }

  // 获取上次登录后最后一次访问的页面
  const { beforePagePathKey, beforePageKeyAppId } = pageViewCacheKey(Fetch.ekbCorpId)
  let beforePage = decodeURIComponent(localCacheGet(beforePagePathKey, true))
  const beforePageAppId = decodeURIComponent(localCacheGet(beforePageKeyAppId, true))
  if (beforePageAppId) {
    Fetch.appId = beforePageAppId
  }
  // 需要定向到首页的情况:
  // const isReportPage = beforePage && !!~beforePage.indexOf('/reports/adv/') //智能报表页
  // const isDataLinkPage = beforePage && !!~beforePage.indexOf('/datalink/') //业务对象页
  const cannotInit = !beforePage || !beforePage.startsWith('/') //如果取出的路由不是以‘/’开头，会报错导致白屏
  if (cannotInit) {
    window.__PLANTFORM__ === 'MC' ? (beforePage = '/mc-tenant-list') : (beforePage = '/new-homepage')
  }

  !noTrack && trackFMP(beforePage)
  // @ts-ignore
  app.useHistory({
    initialEntries: [beforePage],
    search: location.search.slice(1)
  })
}
function handleRefresh(path) {
  // app.dispatchO(() => ({ type: '@@redux/INIT' }));
  setTimeout(() => {
    //api.go(-1)
    location.replace(path)
  }, 4)
}
function initFetchEkbCorpId() {
  // Fetch.ekbCorpId没有值时，需要赋值后才能使用localCacheGet的第二个参数，用来区别不同企业
  let ekbCorpId = filterFromUrl('ekbCorpId')
  if (!ekbCorpId) {
    // 原生环境的url中没有这个参数
    const userInfo = session.get('user')
    if (userInfo) {
      ekbCorpId = userInfo.corpId
    }
  }
  if (ekbCorpId) {
    Fetch.ekbCorpId = ekbCorpId
  }
}
function initUrlCorpId() {
  let ekbCorpId = filterFromUrl('ekbCorpId')
  if (!ekbCorpId) {
    // 原生环境的url中没有这个参数
    const userInfo = session.get('user')
    if (userInfo) {
      ekbCorpId = userInfo.corpId
    }
  }
  if (ekbCorpId) {
    Fetch.ekbCorpId = ekbCorpId
    // 设置 url 替换
    let n = new Date().getTime()
    handleRefresh(`?_=${n}&corpId=${ekbCorpId}#/`)
    return void 0
  }
}
