import React from 'react'
import { app } from '@ekuaibao/whispered'
import { Button } from '@hose/eui'
import { TwoToneLogoAi, OutlinedDirectionRight } from '@hose/eui-icons'
import styles from './AIChatEntry.module.less'
import { getBoolVariation } from '../lib/featbit'
import classNames from 'classnames'

export interface AIChatEntryProps {
  hasRight?: boolean
  style?: React.CSSProperties
  className?: string
}

export interface AIChatEntryButtonProps {
  style?: React.CSSProperties
  className?: string
}

export const AIChatEntry: React.FC<AIChatEntryProps> = ({ hasRight, style, className }) => {
  if (getBoolVariation('aprd-5394-ai-chat')) {
    return <div
      className={classNames(styles['ai-chat-entry'], className)}
      style={style}
      onClick={(e) => {
        e.stopPropagation()
        app.close()
        app.go('/bills')
      }}>
      <div className={styles['ai-chat-entry-left']}>
        <TwoToneLogoAi fontSize={20} />
        <span className={styles['ai-chat-entry-text']}>AI 帮你快速填写单据</span>
      </div>
      {hasRight && <OutlinedDirectionRight fontSize={16} color='var(--eui-icon-n2)' />}
    </div>
  }
  return null
}

export const AIChatEntryButton: React.FC<AIChatEntryButtonProps> = ({ style, className }) => {
  if (getBoolVariation('aprd-5394-ai-chat')) {
    return <Button
      category='text'
      icon={<TwoToneLogoAi fontSize={20} />}
      style={style}
      className={classNames(styles['ai-chat-entry-button'], className)}
      onClick={(e) => {
        e.stopPropagation()
        app.go('/bills')
      }}
    >
      <span className={styles['ai-chat-entry-text']}>AI 帮你快速填写单据</span>
    </Button>
  }
  return null
}

export default AIChatEntry