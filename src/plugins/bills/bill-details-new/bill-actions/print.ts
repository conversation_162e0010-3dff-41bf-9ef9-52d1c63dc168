import { BillActionBase } from './base-action'
import { action } from './constant'
import { handlePrint, PrintType } from './print.utils'

export class PrintRemindAction extends BillActionBase {
  action = action.printRemind
  label = i18n.get('打印提醒')
}


export class PrintDocAction extends BillActionBase {
  action = action.print
  label = i18n.get('打印单据')

  onAction = () => {
    return handlePrint({
      ...this.props,
      printType: PrintType.Print,
      printAttachment: false,
    })
  }
}

export class PrintAction extends BillActionBase {
  action = action.print
  label = i18n.get('打印单据')

  onAction = () => {
    return handlePrint({
      ...this.props,
      printType: PrintType.Print,
    })
  }
}

export class PrintInvoiceAction extends BillActionBase {
  action = action.print
  label = i18n.get('打印单据和发票')

  onAction = () => {
    return handlePrint({
      ...this.props,
      printType: PrintType.PrintInvoice,
    })
  }
}

export class PrintedAction extends BillActionBase {
  action = action.receivePrint
  label = i18n.get('收到打印')
}
