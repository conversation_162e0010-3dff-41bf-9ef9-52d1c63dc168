import LoaderWithLegacyData from '../../../../elements/data-grid-v2/LoaderWithLegacyData'
import { createNodeNameColumn, createNodeStaffColumn } from '../../../../elements/data-grid-v2/CreateColumn'
import React, { PureComponent } from 'react'
import { MessageCenter } from '@ekuaibao/messagecenter'
import { EnhanceConnect } from '@ekuaibao/store'
import { clone } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
const { mapping } = api.require('@audit/util/mapping4paid')
const createRiskWarningColumn = api.require('@audit/util/columnsAndSwitcherUtil').default
import { searchPaidList, searchPrintList, createActionColumn } from '../util'
import { getFlowInfoById, confirmTotalFlows } from '../../bills.action'
import { Resource } from '@ekuaibao/fetch'
const { getDefaultScenes } = api.require('@audit/util/Utils')
import { connect } from '@ekuaibao/mobx-store'
import { handleCompleteBillDetails } from '../util'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

const messageList = new Resource('/api/flow/v2/filter')
const printScenesType = 'PRINT_MESSAGE'
const paidScenesType = 'PAID_MESSAGE'
const listType = 'UnconfirmedList'
@connect(store => ({ windowSize: store.states['@layout'].windowSize }))
@EnhanceConnect(
  state => ({
    baseDataProperties: state['@common'].globalFields.data,
    budgetPower: state['@common'].budgetPower,
    specifications: state['@custom-specification'].specificationGroupsList,
    invoiceReviewPower: state['@common'].powers.invoiceReview,
    KA_GLOBAL_SEARCH_2: state['@common'].powers.KA_GLOBAL_SEARCH_2,
    showPrintBtn: state['@common'].showPrintBtn
  }),
  { confirmTotalFlows, getFlowInfoById }
)
export default class PrintListViewPrintListView extends PureComponent {

  constructor(props, ...args) {
    super(props, ...args)
    this.bus = new MessageCenter()
    this.state = { PRINT_MESSAGE: [], PAID_MESSAGE: [], selectedRowKeys: [] }
  }

  componentDidMount() {
    api.invokeService('@bills:get:getMyPrintRemindCount')
    this.bus.on('buttons:click', this._handleButtonsClick)
    this.bus.on('table:row:click', this._handleTableRowClick)
    this.bus.on('table:row:action', this._handleRowAction)
    this.bus.on('initScenes:action', this.initScenes)
    // 获取场景列表
    api.invokeService('@custom-specification:get:specificationGroups:withSpecificationVersioned').then(data => {
      if (this.props.type === 'printList') {
        messageList.GET('/$type', { type: printScenesType }).then(res => {
          const { value } = res
          this.initScenes(value, printScenesType)
        })
      } else {
        messageList.GET('/$type', { type: paidScenesType }).then(res => {
          const { value } = res
          this.initScenes(value, paidScenesType)
        })
      }
    })
  }

  componentWillUnmount() {
    this.bus.un('buttons:click', this._handleButtonsClick)
    this.bus.un('table:row:click', this._handleTableRowClick)
    this.bus.un('table:row:action', this._handleRowAction)
    this.bus.un('initScenes:action', this.initScenes)
  }

  initScenes = (data, type) => {
    let { specifications } = this.props
    const sourceType = this.props.type
    const defaultScenes = getDefaultScenes(
      sourceType === 'printList' ? 'flowId' : '',
      ['expense', 'loan', 'requisition'],
      specifications
    )
    const allScenes = { text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }
    const filter = data
      ? data.filter.map(d => {
          let temp = JSON.parse(d)
          if (temp.scene) {
            let newScene = defaultScenes.filter(item => item.sceneIndex === temp.scene)
            if (newScene.length > 0) {
              temp = newScene[0]
            }
          }
          temp.pageSize = data.pageSize || 20
          return temp
        })
      : defaultScenes
    const scenes = !!~filter.findIndex(el => el.scene === 'all') ? filter : [allScenes, ...filter]
    this.setState({ [type]: scenes })
  }

  fnfetchData = async (params = {}, dimensionItems = {}) => {
    const { type } = this.props
    const { PRINT_MESSAGE, PAID_MESSAGE, selectedRowKeys } = this.state
    const { scene } = params
    let scenes = type === 'printList' ? PRINT_MESSAGE : PAID_MESSAGE
    const findScene = scenes.find(s => s.sceneIndex === scene)
    findScene && (params.scene = findScene.scene || '')
    scene && this.setState({ scene })
    if (type === 'printList') {
      if (selectedRowKeys.length > 1) {
        this.setState({ selectedRowKeys: [] })
        return this.bus.emit('pagination:changed', 1, 10)
      } else {
        const res = await searchPrintList(params, findScene, dimensionItems)
        this._currentDataSource = res.dataSource
        return res
      }
    }
    if (type === 'paidList') {
      const { isSegment } = this.props
      if (isSegment) params.isSegment = isSegment
      const res = await searchPaidList(params, findScene, dimensionItems)
      this._currentDataSource = res.dataSource
      return res
    }
  }

  _handleButtonsClick = ({ name, data, keys }) => {
    const { confirmTotalFlows } = this.props
    keys = keys || this.bus.getSelectedRowKeys()
    data = data || this.bus.getSelectedRowData()
    switch (name) {
      case 'print':
        return this._handlePrintList(keys, data)
      case 'printInvoice':
        return this._handlePrintInvoice(keys, data)
      case 'confirm':
        return confirmTotalFlows(keys).then(this._updateList)
    }
  }

  _handlePrintList(keys, data) {
    const selectedRowKeys = this.bus.getSelectedRowKeys()
    this.setState({ selectedRowKeys })
    api.invokeService('@audit:handle:multipeprint', keys, data, this._updateList.bind(this), '0')
  }

  _handlePrintInvoice(keys, data) {
    const selectedRowKeys = this.bus.getSelectedRowKeys()
    this.setState({ selectedRowKeys })
    api.invokeService('@audit:handle:multipeprint', keys, data, this._updateList.bind(this), '1')
  }

  _handleTableRowClick = backlog => {
    const { isSegment, handleCopyBill, type } = this.props

    if (isSegment && type !== 'printList') {
      const needConfigButton = type === 'paidList'
      handleCompleteBillDetails.call(this, {params:{ id: backlog.id },copyAction:handleCopyBill,copyAble:true, showBillApproveResult:false,needConfigButton})
    }else{
      startOpenFlowPerformanceStatistics()

      if (api.require('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
        api.open('@bills:BillInfoDrawerV2', {
          currentId: backlog.id,
          flows: (this._currentDataSource || []).map(item => item.flowId),
          bus: this.bus,
          billDetailsProps: {
            showBillApproveResult: false,
          }
        })
        return
      }

      api.invokeService('@bills:get:flow-info', { id: backlog.id }).then(resp => {
        api.open(
          '@bills:BillInfoPopup',
          {
            backlog: { id: -1, flowId: resp?.value },
            invokeService: '@bills:get:flow-info',
            params: { id: resp?.value?.id },
            reload: this.bus.reload,
            showBillApproveResult: false,
            mask: false,
            handlePrintCallback: () => {
              this._updateList()
            },
          },
          false
        )
      })
    }
  }

  _handleRowAction = (type, line) => {
    switch (type) {
      case 'printList':
        return this._printItem(line)
      case 'paidList':
        const { confirmTotalFlows } = this.props
        return confirmTotalFlows(line.id).then(this._updateList)
    }
  }

  _printItem = data => {
    const { doPrint } = api.require('@audit/service-print')
    let obj = api.invokeService('@share:get:print:param', data.flowId)
    doPrint(
      [obj],
      false,
      function() {
        this._updateList.call(this)
      }.bind(this)
    )
  }

  _updateList = () => {
    const { updateList } = this.props
    this.bus.clearSelectedRowKeys && this.bus.clearSelectedRowKeys()
    this.bus.reload && this.bus.reload().then(() => {
      setTimeout(() => {
        this.bus.emit('table:select:current:row')
        updateList && updateList()
      }, 500);
    })

  }

  buttons = () => {
    const { type, showPrintBtn } = this.props
    const printBtn = [{ text: i18n.get('打印单据'), name: 'print', type: 'primary' }]
    if (showPrintBtn) printBtn.push({ text: i18n.get('打印单据和发票'), name: 'printInvoice', type: 'primary' })
    if (type === 'printList') return printBtn
    if (type === 'paidList') return [{ text: i18n.get('确认'), name: 'confirm' }]
  }

  fnFilterMapping = (mapping = { all: [] }, invoiceReviewPower) => {
    if (!invoiceReviewPower) {
      const filterAll = mapping.all.filter(oo => oo !== 'reviewStatus')
      return { ...mapping, all: filterAll }
    } else {
      return mapping
    }
  }

  render() {
    const {
      baseDataProperties,
      windowSize,
      type,
      segmentY,
      newSearch,
      searchOptions,
      globalSearchOptions,
      invoiceReviewPower,
      KA_GLOBAL_SEARCH_2
    } = this.props
    const { PRINT_MESSAGE, PAID_MESSAGE } = this.state
    if (!PRINT_MESSAGE.length && type == 'printList') {
      return null
    }
    if (!PAID_MESSAGE.length && type == 'paidList') {
      return null
    }

    let fixSize = clone(windowSize)
    if (fixSize) {
      fixSize.y = Math.max(620, fixSize.y)
      fixSize.y = segmentY ? fixSize.y - 280 - segmentY : fixSize.y - 280
    }
    let fixColumns = { state: '$', '*': 'form', ownerId: '$' }
    let scenes = PAID_MESSAGE
    let scenesType = 'PAID_MESSAGE'
    if (type === 'printList') {
      fixColumns = { state: 'flowId', '*': 'flowId.form' }
      scenes = PRINT_MESSAGE
      scenesType = 'PRINT_MESSAGE'
    }
    return (
      <LoaderWithLegacyData
        newSearch={newSearch}
        searchOptions={KA_GLOBAL_SEARCH_2 ? globalSearchOptions : searchOptions}
        enableGlobalSearch={KA_GLOBAL_SEARCH_2}
        scenes={scenes}
        completeBillType={listType}
        fetch={this.fnfetchData}
        scenesType={scenesType}
        buttons={this.buttons()}
        onButtonClick={this._handleButtonsClick}
        prefixColumns={fixColumns}
        bus={this.bus}
        resource={messageList}
        baseDataProperties={baseDataProperties}
        createAction={createActionColumn(type)}
        mapping={this.fnFilterMapping(mapping, invoiceReviewPower)}
        createRiskWarningColumn={createRiskWarningColumn}
        bodyStyle={{
          height: 200
        }}
        createNodeStaffColumn={
          type === 'printList' ? () => createNodeStaffColumn({ filterType: false }) : createNodeStaffColumn
        }
        createNodeNameColumn={createNodeNameColumn}
        useNewFieldSet
      />
    )
  }
}
