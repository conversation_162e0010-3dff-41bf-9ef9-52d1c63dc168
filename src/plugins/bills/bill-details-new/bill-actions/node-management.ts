import { message } from '@hose/eui'
import { get } from 'lodash'
import { app } from '@ekuaibao/whispered'
import { BillActionBase, BillActionBaseProps } from './base-action'
import { action } from './constant'

export class ShiftApproveAction extends BillActionBase {
  action = action.addnode
  label = i18n.get('转交审批')
}

export class AddSignNodeAction extends BillActionBase {
  action = action.addSignNode
  label = i18n.get('加签审批')
}

export class AdminShiftNodeAction extends BillActionBase<BillActionBaseProps> {
  label = i18n.get('转交')

  onAction = () => {
    return this.handleShiftNode()
  }

  handleShiftNode = async () => {
    const { layer, backlog, privilegeId } = this.props
    const state = get(backlog, 'flowId.state')
    const id = get(backlog, 'flowId.id')
    if (['nullify'].includes(state)) {
      return message.error(i18n.get('已作废单据状态不支持此功能'))
    }
    await app.invokeService('@order-manage:deliverFlow', { flowIds: [id], privilegeId })
  }
}

export class SuspendAction extends BillActionBase {
  action = action.hangUp
  label = i18n.get('暂挂审批')
}

export class BackAction extends BillActionBase<BillActionBaseProps> {
  label = i18n.get('回退')

  onAction = () => {
    return this.handleBack()
  }

  handleBack = async () => {
    const { backlog, layer, privilegeId } = this.props
    const state = get(backlog, 'flowId.state')
    const id = get(backlog, 'flowId.id')
    if (['nullify'].includes(state)) {
      return message.error(i18n.get('已作废单据状态不支持此功能'))
    }
    await app.invokeService('@order-manage:backFlow', { flowIds: [id], privilegeId })
  }
}