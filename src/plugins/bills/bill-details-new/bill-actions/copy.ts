import { app } from "@ekuaibao/whispered";
import { cloneDeep, get, } from "lodash";
import { BillActionBase } from "./base-action";
import { confirmCopy, formatCopyBillData, checkSpecificationActive } from "../../util/billUtils";
import MessageCenter from "@ekuaibao/messagecenter";

export class CopyAction extends BillActionBase {
  label = i18n.get('复制')

  onAction = () => {
    return this.handleCopy()
  }

  getCurrencySpecOriginalId = () => {
    const { backlog } = this.props
    // @ts-ignore
    let { form = {}, currentSpecification, requisitionInfo } = backlog.flowId
    const specification = currentSpecification || form.specificationId || requisitionInfo?.defaultSpecification || {}
    return get(specification, 'originalId')
  }


  handleCopy = async () => {
    // 单据使用的模板被停用时，拦截复制动作
    const specificationOriginalId = this.getCurrencySpecOriginalId()
    const specActive = await checkSpecificationActive(specificationOriginalId)
    if (!specActive) return

    const { backlog, from, bus } = this.props
    const dataSource = cloneDeep(backlog.flowId)
    delete dataSource.form.systemGeneration

    // 复制单据不能带费用明细序号，序号应由后端生成
    if (dataSource?.form?.details?.length) {
      dataSource.form.details = dataSource.form.details?.map(detail => {
        delete detail.feeTypeForm.detailNo
        return detail
      })
    }
    // 复制单据不能带行程id，行程应由后端生成
    if (dataSource?.form?.travelPlanning) {
      dataSource.form.travelPlanning = dataSource.form.travelPlanning?.map(travel => {
        travel.travelId = null
        return travel
      })
    }

    this.handleDrawerCopy(dataSource)
    app.invokeService('@common:insert:assist:record', {
      title: `复制${get(dataSource, 'form.title') || ''}单据`
    })
  }

  handleDrawerCopy = dataSource => {
    confirmCopy(dataSource).then((_: never) => {
      this.handleCopyBill(dataSource)
    })
  }

  handleCopyBill = async dataSource => {
    const { from, bus } = this.props
    const data = await formatCopyBillData(dataSource)
    const _bus = new MessageCenter()
    if (bus && bus.reload) {
      // @ts-ignore
      _bus.reload = bus.reload.bind(bus)
    }
    app.open('@bills:BillEditableDrawer', {
      data,
      from,
      bus: _bus,
      showUpDown: false,
    })
  }
}