const fs = require('fs')
const path = require('path')
const parser = require('@babel/parser')
const traverse = require('@babel/traverse').default
const t = require('@babel/types')

const dynamicList = []

// 存储所有图标定义
const iconDefs = new Set()

// 存储图标使用信息
const iconUsage = {
  // iconName: {
  //   files: [filePath1, filePath2, ...],
  //   source: 'iconfont.js' | 'iconfont7.js' | 'iconpark.js'
  // }
}

// 存储动态图标引用
const dynamicIconUsage = {
  svg: {
    // filePath: [{
    //   variable: 'variableName',
    //   codeSnippet: '相关代码片段'
    // }]
  },
  ekbIcon: {
    // filePath: [{
    //   variable: 'variableName',
    //   codeSnippet: '相关代码片段'
    // }]
  }
}

// 存储解析失败的文件信息
const parseErrors = {
  // filePath: {
  //   errorType: string,
  //   errorMessage: string,
  //   errorLine?: number,
  //   errorColumn?: number
  // }
}

// 添加黑名单文件列表，ast解析报错，人工review
const blacklist = []

// 解析 iconfont 文件
function parseIconFontFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8')
  const fileName = path.basename(filePath)

  // 提取 SVG 字符串
  let svgString = ''
  if (content.includes('window._iconfont_svg_string_=')) {
    const match = content.match(/_iconfont_svg_string_=\s*'([^']+)'/)
    if (match) {
      svgString = match[1]
    }
  } else if (content.includes('_iconfont_svg_string_')) {
    // iconfont.js 和 iconfont7.js 格式
    const match = content.match(/_iconfont_svg_string_[^=]+=\s*'([^']+)'/)
    if (match) {
      svgString = match[1]
    }
  } else {
    // iconpark.js 格式
    const match = content.match(/r='([^']+)'/)
    if (match) {
      svgString = match[1]
    }
  }

  if (!svgString) {
    console.warn(`No SVG string found in ${fileName}`)
    return
  }

  // 提取所有 symbol id
  const idRegex = /<symbol\s+id="([^"]+)"/g
  let match
  while ((match = idRegex.exec(svgString)) !== null) {
    const id = match[1]
    iconDefs.add(id)
    if (!iconUsage[id]) {
      iconUsage[id] = {
        files: [],
        source: fileName
      }
    }
  }
}

// 解析单个文件中的图标使用
function analyzeFile(filePath) {
  try {
    const code = fs.readFileSync(filePath, 'utf-8')

    // 尝试不同的解析配置
    let ast
    try {
      ast = parser.parse(code, {
        sourceType: 'module',
        plugins: [
          'jsx',
          [
            'typescript',
            {
              dts: false,
              isTSX: true,
              allowNamespaces: true,
              allowDeclareFields: true
            }
          ],
          'decorators-legacy',
          'classProperties',
          'classPrivateProperties',
          'classPrivateMethods',
          'optionalChaining',
          'nullishCoalescingOperator',
          'objectRestSpread',
          'dynamicImport',
          'exportDefaultFrom',
          'exportNamespaceFrom',
          'throwExpressions',
          'topLevelAwait',
          'doExpressions',
          'functionBind',
          'functionSent',
          'logicalAssignment',
          'partialApplication',
          ['pipelineOperator', { proposal: 'minimal' }],
          'recordAndTuple',
          ['optionalChainingAssign', { version: '2023-07' }],
          'decorators',
          [
            'proposal-decorators',
            {
              decoratorsBeforeExport: true,
              legacy: false
            }
          ],
          'classStaticBlock', // 添加更多可能需要的插件
          'importAssertions',
          'importAttributes'
        ],
        errorRecovery: true, // 即使有错误也继续解析
        allowImportExportEverywhere: true,
        allowReturnOutsideFunction: true,
        allowSuperOutsideFunction: true,
        allowUndeclaredExports: true,
        createParenthesizedExpressions: true
      })
    } catch (e) {
      // console.log('First parse attempt failed, trying fallback configuration')  // 添加解析失败日志
      // 如果第一次解析失败，尝试使用更宽松的配置
      ast = parser.parse(code, {
        sourceType: 'module',
        plugins: [
          'jsx',
          'typescript',
          'decorators-legacy',
          'classProperties',
          'optionalChaining',
          'nullishCoalescingOperator'
        ],
        errorRecovery: true,
        tokens: true,
        allowImportExportEverywhere: true,
        allowReturnOutsideFunction: true,
        allowSuperOutsideMethod: true,
        allowUndeclaredExports: true
      })
    }

    // 只关注我们需要的节点类型
    const visitor = {
      // 处理 svg use 标签
      JSXElement(path) {
        try {
          if (t.isJSXIdentifier(path.node.openingElement.name, { name: 'use' })) {
            const xlinkAttr = path.node.openingElement.attributes.find(
              attr =>
                t.isJSXAttribute(attr) &&
                (t.isJSXIdentifier(attr.name, { name: 'xlinkHref' }) ||
                  t.isJSXNamespacedName(attr.name, {
                    namespace: { name: 'xlink' },
                    name: { name: 'href' }
                  }))
            )

            if (xlinkAttr) {
              if (t.isStringLiteral(xlinkAttr.value)) {
                // 静态引用
                const iconName = xlinkAttr.value.value
                if (iconUsage[iconName]) {
                  iconUsage[iconName].files.push(filePath)
                }
              } else if (t.isJSXExpressionContainer(xlinkAttr.value)) {
                // 检查是否是字符串字面量
                const expr = xlinkAttr.value.expression
                if (t.isStringLiteral(expr)) {
                  // 如果是字符串字面量，当作静态引用处理
                  const iconName = expr.value
                  if (iconUsage[iconName]) {
                    iconUsage[iconName].files.push(filePath)
                  }
                } else {
                  // 真正的动态引用
                  if (!dynamicIconUsage.svg[filePath]) {
                    dynamicIconUsage.svg[filePath] = []
                  }

                  let variableName = ''
                  if (t.isIdentifier(expr)) {
                    variableName = expr.name
                  } else {
                    variableName = code.slice(expr.start, expr.end)
                  }

                  const startLine = path.node.loc.start.line - 2
                  const endLine = path.node.loc.end.line + 2
                  const codeLines = code.split('\n').slice(startLine > 0 ? startLine : 0, endLine)

                  dynamicIconUsage.svg[filePath].push({
                    variable: variableName,
                    codeSnippet: codeLines.join('\n')
                  })
                }
              }
            }
          }
        } catch (e) {
          // 忽略单个节点的处理错误
          console.error('Error in JSXElement:', e)
        }
      },

      // 处理 EKBIcon 组件
      JSXOpeningElement(path) {
        try {
          const nodeName = path.node.name.name

          // 处理 EKBIcon 组件
          if (t.isJSXIdentifier(path.node.name) && /^ekbicon$/i.test(nodeName)) {
            const nameAttr = path.node.attributes.find(
              attr => t.isJSXAttribute(attr) && t.isJSXIdentifier(attr.name, { name: 'name' })
            )

            if (nameAttr) {
              if (t.isStringLiteral(nameAttr.value)) {
                // 静态引用 - 直接字符串
                const iconName = nameAttr.value.value
                const normalizedName = iconName.startsWith('#') ? iconName.slice(1) : iconName
                if (iconUsage[normalizedName]) {
                  iconUsage[normalizedName].files.push(filePath)
                }
              } else if (t.isJSXExpressionContainer(nameAttr.value)) {
                const expr = nameAttr.value.expression

                // 获取实际的字符串值
                let actualValue = null
                if (t.isStringLiteral(expr)) {
                  actualValue = expr.value
                } else if (t.isTemplateLiteral(expr) && expr.quasis.length === 1) {
                  actualValue = expr.quasis[0].value.raw
                }

                if (actualValue) {
                  // 如果能获取到实际的字符串值，当作静态引用处理
                  const normalizedName = actualValue.startsWith('#') ? actualValue.slice(1) : actualValue
                  if (iconUsage[normalizedName]) {
                    iconUsage[normalizedName].files.push(filePath)
                    return // 跳过动态引用处理
                  }
                }

                // 如果不是静态字符串，才当作动态引用处理
                if (!dynamicIconUsage.ekbIcon[filePath]) {
                  dynamicIconUsage.ekbIcon[filePath] = []
                }

                let variableName = ''
                if (t.isIdentifier(expr)) {
                  variableName = expr.name
                } else {
                  variableName = code.slice(expr.start, expr.end)
                }

                const startLine = path.node.loc.start.line - 2
                const endLine = path.node.loc.end.line + 2
                const codeLines = code.split('\n').slice(startLine > 0 ? startLine : 0, endLine)

                dynamicIconUsage.ekbIcon[filePath].push({
                  variable: variableName,
                  codeSnippet: codeLines.join('\n')
                })
              }
            }
          }
        } catch (e) {
          console.error('Error processing JSX element:', e)
        }
      }
    }

    traverse(ast, visitor)
  } catch (error) {
    // 记录错误但不中断处理
    console.warn(`Warning: Error processing file ${filePath}`)
    console.warn(`Error type: ${error.name}`)
    console.warn(`Error message: ${error.message}`)

    // 收集错误信息
    parseErrors[filePath] = {
      errorType: error.name,
      errorMessage: error.message
    }

    // 如果是语法错误，添加位置信息
    if (error.code === 'BABEL_PARSER_SYNTAX_ERROR') {
      parseErrors[filePath].errorLine = error.loc?.line
      parseErrors[filePath].errorColumn = error.loc?.column
    }
  }
}

// 递归遍历目录
function analyzeDir(dir) {
  const files = fs.readdirSync(dir)
  const baseDir = path.resolve(__dirname, '../../..')

  files.forEach(file => {
    const fullPath = path.join(dir, file)
    const stat = fs.statSync(fullPath)

    if (stat.isDirectory()) {
      analyzeDir(fullPath)
    } else if (/\.(js|jsx|ts|tsx)$/.test(file)) {
      // 检查文件是否在黑名单中
      const relativePath = path.relative(baseDir, fullPath)

      if (!blacklist.includes(relativePath)) {
        analyzeFile(fullPath)
      } else {
        console.log('Skipping blacklisted file:', relativePath)
      }
    }
  })
}

// 入口函数
function analyze() {
  // 初始化动态引用存储
  dynamicIconUsage.svg = {}
  dynamicIconUsage.ekbIcon = {}

  // 先解析 iconfont 文件
  const iconFontFiles = [
    path.resolve(__dirname, '../../src/file/iconfont.js'),
    path.resolve(__dirname, '../../src/file/iconfont7.js'),
    path.resolve(__dirname, '../../src/file/iconfontFlowPlans.js'),
    // path.resolve(__dirname, '../../src/file/iconPark.js'),
    path.resolve(__dirname, '../../src/file/iconPark1.js')
  ]

  iconFontFiles.forEach(parseIconFontFile)

  // 然后分析使用情况
  const srcDir = path.resolve(__dirname, '../../src')
  analyzeDir(srcDir)

  // 生成分析报告
  const report = {
    // 统计每个文件中的图标定义
    iconDefinitions: Array.from(iconDefs).reduce((acc, id) => {
      acc[id] = iconUsage[id].source
      return acc
    }, {}),

    // 统计使用情况
    iconUsage: Object.entries(iconUsage).reduce((acc, [id, info]) => {
      if (info.files.length > 0) {
        acc[id] = {
          source: info.source,
          usedIn: info.files
        }
      }
      return acc
    }, {}),

    // 统计未使用的图标，要一起去除动态引用筛选出来的
    unusedIcons: Array.from(iconDefs).filter(id => iconUsage[id].files.length === 0 && !dynamicList.includes(id)),

    // 动态引用的统计
    dynamicUsage: dynamicIconUsage,

    // 解析失败的文件
    parseErrors: parseErrors
  }

  fs.writeFileSync('icon-analysis.json', JSON.stringify(report, null, 2))

  // 生成优化后的图标文件
  // generateOptimizedIconFiles()
}

function generateOptimizedIconFiles() {
  const iconFontFiles = [
    {
      src: path.resolve(__dirname, '../../../src/file/iconfont.js'),
      dest: path.resolve(__dirname, '../../../src/file/iconfont_new.js')
    },
    {
      src: path.resolve(__dirname, '../../../src/file/iconfont7.js'),
      dest: path.resolve(__dirname, '../../../src/file/iconfont7_new.js')
    },
    {
      src: path.resolve(__dirname, '../../../src/file/iconpark.js'),
      dest: path.resolve(__dirname, '../../../src/file/iconpark_new.js')
    }
  ]

  // 获取所有使用的图标ID
  const usedIcons = new Set(
    Array.from(iconDefs).filter(id => iconUsage[id].files.length > 0 || dynamicList.includes(id))
  )

  iconFontFiles.forEach(({ src, dest }) => {
    const content = fs.readFileSync(src, 'utf-8')
    const fileName = path.basename(src)

    // 提取 SVG 字符串
    let svgString = ''
    let prefix = ''
    let suffix = ''

    if (content.includes('_iconfont_svg_string_')) {
      // iconfont.js 和 iconfont7.js 格式
      const varNameMatch = content.match(/_iconfont_svg_string_[0-9]+/)
      if (varNameMatch) {
        const varName = varNameMatch[0]
        // 找到变量声明的开始
        const startMatch = content.match(new RegExp(`window\\.${varName}\\s*=\\s*'`))
        if (startMatch) {
          const startIndex = startMatch.index
          const endIndex = content.indexOf("',function", startIndex)
          if (endIndex !== -1) {
            prefix = content.slice(0, startIndex + startMatch[0].length)
            svgString = content.slice(startIndex + startMatch[0].length, endIndex)
            suffix = content.slice(endIndex)
          }
        }
      }
    } else {
      // iconpark.js 格式
      const match = content.match(/!function\(e\)\{[^}]*r='([^']+)'/)
      if (match) {
        const startIndex = match.index
        const svgStartIndex = content.indexOf("r='") + 3
        const svgEndIndex = content.indexOf("'", svgStartIndex)

        prefix = content.slice(0, svgStartIndex)
        svgString = content.slice(svgStartIndex, svgEndIndex)
        suffix = content.slice(svgEndIndex)
      }
    }

    if (!svgString) {
      console.warn(`No SVG string found in ${fileName}`)
      return
    }

    // 解析 SVG 字符串为 DOM
    const parser = new (require('jsdom').JSDOM)()
    const doc = parser.window.document
    const container = doc.createElement('div')
    container.innerHTML = svgString

    // 移除未使用的图标
    const symbols = container.getElementsByTagName('symbol')
    let removedCount = 0
    Array.from(symbols).forEach(symbol => {
      const id = symbol.getAttribute('id')
      if (!usedIcons.has(id)) {
        symbol.remove()
        removedCount++
      }
    })

    // 生成新的文件内容
    const optimizedSvgString = container.innerHTML
    const newContent = `${prefix}${optimizedSvgString}${suffix}`

    // 写入新文件
    fs.writeFileSync(dest, newContent)
    console.log(`Generated optimized file: ${dest}`)
    console.log(`Removed ${removedCount} unused icons from ${fileName}`)
  })
}

analyze()
