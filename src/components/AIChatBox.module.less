.chatWrapper {
  border-radius: 8px;
  height: 100%;
  margin-left: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #E1EFFF 0%, #FFF 10.71%, #FFF 80.27%, #E6F1FF 96.98%);
}

.chatHeader {
  flex: 0 0 60px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  border-radius: 8px 8px 0 0;
  justify-content: space-between;
  color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
  font: var(--eui-font-head-b1);
  border: 2px solid var(--eui-bg-body, #FFF);
  border-bottom: none;
  background: linear-gradient(rgb(226, 240, 255) 0%, rgb(246, 250, 255) 100%);
}

.closeIcon {
  cursor: pointer;
  margin-left: 10px;
}