import { ButtonProps, message as EUIMessage } from "@hose/eui";
import { get } from "lodash";
import { BillActionBase } from "./base-action";
import { BillActionBaseProps } from "./base-action";
import { setFormMultiplePayeesMode } from "../../util/billUtils";

export class SaveAction extends BillActionBase<BillActionBaseProps> {
  label = i18n.get('存为草稿')

  constructor(props: BillActionBaseProps, buttonProps: ButtonProps) {
    super(props, buttonProps)
  }

  onAction = async () => {
    return this.handleSave()
  }

  handleSave = async () => {
    const { backlog = {}, bus, layer } = this.props
    return bus.getValueWithValidate(1)
      .then(async (formValue) => {
        await this.setButtonStatue(true)
        setFormMultiplePayeesMode(formValue, get(backlog, 'flowId'))
        return bus.invoke('save:bill:click', formValue)
      })
      .then(_ => {
        this.reloadData()
        layer?.emitCancel()
      })
      .catch(e => {
        //校验失败
        if (e && (e['details'] || e['trips'])) {
          let { errors = [] } = e['details'] || e['trips']
          let { message } = errors[0]
          EUIMessage.error(message)
        }
      })
      .finally(() => {
        this.buttonProps.loading = false
        this.buttonProps.disabled = false
      })
  }

  setButtonStatue = async (active: boolean) => {
    this.buttonProps.loading = active
    this.buttonProps.disabled = active
    await this.delayForRender()
  }

  delayForRender = async () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true)
      }, 0)
    })
  }
}