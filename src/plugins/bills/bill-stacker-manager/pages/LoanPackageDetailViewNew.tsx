import styles from './LoanPackageDetailViewNew.module.less'
import { ButtonGroup } from '@ekuaibao/eui'
import React from 'react'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { get } from 'lodash'
import classnames from 'classnames'
import moment from 'moment'
import { showModal, showMessage } from '@ekuaibao/show-util'
import {
  getLoanPackageList,
  getloanpackageDetailInfo,
  getRepayInfo,
  applyRepayment,
  hideRepayInfo,
  getFlowInfoById,
  delayLoanInfo,
  shareLoan,
  getloanpackageDetailLogs,
  shiftConfirmLoan,
  hasPermissionLoan,
  fixRepaymentRecord,
  getLoanConfigRule,
  shareLoanConfirm,
  updateRepaymentMoney,
  getLoanConfigParam
} from '../../bills.action'
import MessageCenter from '@ekuaibao/messagecenter'
import { handleBillDetailClickById } from '../util'
import { connect } from '@ekuaibao/mobx-store'
import { toJS } from 'mobx'
import { observer } from 'mobx-react'
import { inject } from '@ekuaibao/react-ioc'
import { IMG_REG } from '@ekuaibao/lib/lib/enums'
import Chart from './loan-package/chartView'
import LoanLogsView from './loan-package/loan-logs'
const { thousandBitSeparator } = api.require('@components/utils/fnThousandBitSeparator')
import { Modal, Input } from 'antd'
import { enableCustomExtendButton, getBoolVariation } from '../../../../lib/featbit'
import { MY_LOAN_PAGE_NAME } from './loan-package/helper'
const download = api.invokeServiceAsLazyValue('@bills:file:download')
const preview = api.invokeServiceAsLazyValue('@bills:file:preview')
const EKBIcon = api.require<any>('@elements/ekbIcon')
const NamePopover = api.require<any>('@elements/namePopover/PopoverWrapper')
@connect(store => ({
  isInImmersive: store.states['@layout5'].isInImmersive
}))
@EnhanceConnect(
  state => ({
    repayInfoTemp: state['@bills'].repayInfoTemp,
    userInfo: state['@common'].userinfo.data,
    MULTICURRENCYWRITTENOFF: state['@common'].powers.MULTICURRENCYWRITTENOFF, // 多币种核销charge
    legalEntityCurrencyPower: state['@common'].powers.legalEntityCurrency,
    CROSSCURRENCYWRITTENOFF: state['@common'].powers.CROSSCURRENCYWRITTENOFF
  }),
  {
    getLoanPackageList,
    getloanpackageDetailInfo,
    getRepayInfo,
    applyRepayment,
    hideRepayInfo,
    getFlowInfoById,
    shareLoan,
    getloanpackageDetailLogs
  }
)
@observer
class LoanPackageDetail extends React.Component {
  @inject('permission') permission: any
  isInImmersive = false
  constructor(props) {
    super(props)
    this.bus = new MessageCenter()
    this.state = {
      repayInfo: props.repayInfo,
      loanDetail: props.loanDetail || {},
      isNeedCashierNode: undefined,
      billDetails: {},
      autoDelayTimes: get(props.loanDetail, 'delayCount') || 0,
      loanLogs: {},
      permissionLoan: false,
      refreshLoading: false,
      isShowUpdateMoneyBtn: false,
      updateMoneyVisible: false,
      updateMoney: '',
      updateReason: ''
    }
    this.isInImmersive = props?.isInImmersive
    this.isUpdateRepaymentMoney = false // 是否正在更新还款金额
  }

  componentDidMount() {
    const {
      loanDetail: { flowId }
    } = this.state
    const { showCloseLoanButton } = this.props
    const { showRepaymentButton, getFlowInfoById, privilegeId = '', loanDetail } = this.props
    showRepaymentButton && !loanDetail && this.handleGetRepayInfo()
    if (showCloseLoanButton) {
      flowId &&
        getFlowInfoById({ id: flowId, privilegeId }).then(result => {
          const nodes = get(result, 'payload.value.plan.nodes', [])
          const node = nodes.find(line => line?.config?.isNeedCashierNode === false)
          const isNeedCashierNode = node && node?.config?.isNeedCashierNode === false
          this.setState({ isNeedCashierNode, billDetails: get(result, 'payload.value', {}) })
        })
      // 获取配置
      getLoanConfigParam().then(res => {
        if (res) {
          this.setState({
            isShowUpdateMoneyBtn: res.value
          })
        }
      })
    }
    this.getLoanLogs()
    window.addEventListener('resize', this.setClass)
    this.setClass()
    hasPermissionLoan().then(res => {
      this.setState({
        permissionLoan: res.value
      })
    })
  }

  componentWillUnmount() {
    api.invokeService('@bills:update:dimention:currency', null)
    window.removeEventListener('resize', this.setClass)
  }
  componentWillReceiveProps(nextProps: any) {
    if (this.props?.isInImmersive !== nextProps?.isInImmersive) {
      this.isInImmersive = nextProps?.isInImmersive
      this.setClass()
    }
  }

  setClass = () => {
    const warper = document.getElementById('loanDetail')
    let width = warper.clientWidth || warper.offsetWidth
    width = this.isInImmersive ? width - 64 : width - 260
    this.setState({
      className: width > 1200 ? 'detail-con' : 'detail-con small-box'
    })
  }

  // 审核中logs
  getLoanLogs = () => {
    const { loanDetail } = this.state
    const flowId = loanDetail.flowId
    const { getloanpackageDetailLogs, flowId: relateFlowId } = this.props
    getloanpackageDetailLogs &&
      getloanpackageDetailLogs(flowId, relateFlowId).then(action => {
        const logs = action.payload.value
        this.setState({
          loanLogs: logs
        })
      })
  }

  handleExpenseClick = id => {
    const { layer, flowId, privilegeId = '', showAllFeeTypeInManage: showAllFeeType, scene, sourcePage, owner } = this.props
    let checkPermissions = true
    if ((sourcePage === 'fromWrittenOff' || !!owner) && getBoolVariation('hailiang_loan_permission')) {
      checkPermissions = false
    }
    // 增加flowId后台用来判断是否有权限查看
    const params = flowId ? { id, flowId, privilegeId, showAllFeeType, scene, needConfigButton: true, checkPermissions } : { id, privilegeId, showAllFeeType, scene, needConfigButton: true, checkPermissions }
    handleBillDetailClickById.call(this, params, layer)
  }

  handleLoanClick = id => {
    const { layer, flowId, privilegeId = '', sourcePage, owner } = this.props
    let checkPermissions = true
    if ((sourcePage === 'fromWrittenOff' || !!owner) && getBoolVariation('hailiang_loan_permission')) {
      checkPermissions = false
    }
    const params = flowId ? { id, flowId, privilegeId, checkPermissions, needHideLoanDetail: !checkPermissions } : { id, privilegeId, sourcePage, checkPermissions, needHideLoanDetail: !checkPermissions }
    handleBillDetailClickById.call(this, enableCustomExtendButton() ? { ...params, fromPageV2: MY_LOAN_PAGE_NAME } : params, layer)
  }

  handleRepaymentButton = async () => {
    const { loanDetail } = this.state
    const { legalEntityCurrencyPower, getLoanPackageList, applyRepayment } = this.props
    const params = loanDetail.foreignCurrencyLoan
      ? { ...loanDetail, currencySelAble: false, selectCurrencyDisable: true, isManualRepayment: true }
      : { ...loanDetail }
    if (loanDetail.foreignCurrencyLoan) {
      params.foreignCurrencyLoan.standard = null
    }
    params.EBUSSCARD = loanDetail.infoType === 'EBUSSCARD'
    params.applyRepayment = applyRepayment
    params.loanInfoId = loanDetail.id
    if (legalEntityCurrencyPower && loanDetail?.totalMoneyNode?.standardNumCode) {
      const { value: currency } = await api.invokeService('@currency-manage:get:currency:info:by:id', {
        id: loanDetail.totalMoneyNode.standardNumCode
      })
      const { items: rates } = await api.invokeService(
        '@currency-manage:get:currency:rates:by:Id',
        loanDetail.totalMoneyNode.standardNumCode
      )
      const dimentionCurrency = { rates, currency }
      api.invokeService('@bills:update:dimention:currency', dimentionCurrency)
    }

    setTimeout(() => {
      api.open('@bills:RepaymentModal', { ...params }).then(resp => {
        if (resp) {
          // 更新借款包列表
          getLoanPackageList && getLoanPackageList()
          // 更新审核中logs
          this.getLoanLogs()
          // 更新详情页面
          this.handleGetRepayInfo()
        }
      })
    }, 300)
  }
  // 共享按钮
  handleShare = async () => {
    const { loanDetail } = this.state
    const result = await api.dispatch(getLoanConfigRule())
    const shareText = () => {
      const isShareConfirm = result?.items?.find(item => item.type === 'SHARE_CONFIRM')?.forbid
      let text = i18n.get('共享，即将该借款包共享给他人，共享后他人也可核销该借款包或手动还款')
      if (isShareConfirm) {
        text = i18n.get('共享，即将该借款包共享给他人，他人确认接收共享后也可核销该借款包或手动还款')
      }
      return text
    }
    api
      .open('@bills:ApplyShareModal', {
        remindText: shareText(),
        detail: toJS(loanDetail),
        isVisibilityStaffs: false,
        type: 'loan'
      })
      .then(({ sharedOwnerIds: data, userRemark } = {}) => {
        if (data && data.length > 150) {
          showMessage.error(i18n.get('同时共享人数不能超过150人'))
          return
        }
        const id = loanDetail?.ownerId?.id || loanDetail?.ownerId
        data = [...data, id]
        api
          .invokeService('@bills:share:loan', {
            loanInfoId: loanDetail.id,
            staffList: data,
            userRemark,
            privilegeId: this.props.privilegeId || ''
          })
          .then(() => {
            this.updateSuccessData()
          })
          .catch(err => showMessage.error(err.msg))
      })
  }

  handleGetRepayInfo = (id = '') => {
    const { getloanpackageDetailInfo, getRepayInfo, flowId } = this.props
    id = id === '' ? this.state.loanDetail.id : id
    const qs = flowId ? { id, flowId } : { id }
    getloanpackageDetailInfo &&
      getloanpackageDetailInfo(qs).then(action => {
        if (action.error) {
          return
        }
        const loanInfo = action.payload
        const param = { loanInfoId: this.state.loanDetail.id }
        // 更新还款显示状态
        getRepayInfo &&
          getRepayInfo(param).then(actionRepayInfo => {
            if (action.error) {
              return
            }
            const data = actionRepayInfo.payload
            this.setState({
              repayInfo: data.items[0],
              loanDetail: loanInfo.value
            })
          })
      })
  }

  handleHideRepayInfo = () => {
    const { loanDetail } = this.state
    this.props.hideRepayInfo({
      loanInfoId: loanDetail.id
    })
  }

  handleCloseLoanPackage = () => {
    const { layer, closeLoanPackageFn } = this.props
    const { loanDetail } = this.state
    closeLoanPackageFn(loanDetail).then(result => {
      if (result) {
        layer.emitCancel()
      }
    })
  }

  handleWithdraw = async () => {
    const { loanDetail } = this.state
    const { closeLoanWithdrawFn } = this.props
    const { value: flowInfo } = await api.invokeService('@bills:get:flow-info', { id: loanDetail.flowId })
    // 找出纳的会签节点
    const cashierCountersign = flowInfo?.plan?.nodes?.find(
      node => node?.name === '出纳支付' && node?.type === 'countersign'
    )
    const getApproverPromise = cashierCountersign?.counterSigners?.length
      ? Promise.resolve({})
      : api.invokeService('@bills:withdraw:approvers', { id: loanDetail.flowId })
    getApproverPromise
      .then(approver => {
        return api.open('@bills:BillWithdrawModal', { flow: approver, cashierCountersign })
      })
      .then(params => {
        const { flowId } = loanDetail
        params = { ...params, flowId: flowId }
        return api.invokeService('@bills:withdraw:save', params)
      })
      .then(_ => {
        closeLoanWithdrawFn()
        this.props.layer.emitCancel()
        api.invokeService('@common:get:backlog:count:payment')
      })
  }
  handleTransferLoanPackage = () => {
    const { transferLoanPackageFn } = this.props
    const { loanDetail } = this.state
    transferLoanPackageFn(loanDetail).then(result => {
      // 更新详情页面
      this.handleGetRepayInfo(result.id)
    })
  }

  handleRemindRepayMent = () => {
    const { loanDetail } = this.state
    const { closeLoanWithdrawFn } = this.props
    const ids = [loanDetail.id]
    showModal.confirm({
      className: 'confirm-modal-wapper',
      title: i18n.get('确定发送提醒?'),
      iconType: 'warning',
      content: i18n.get(
        '这些借款的还款提醒状态将会变为「已提醒」。你可以在列表展示里找到「还款提醒状态」字段，并以此进行筛选。'
      ),
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        api.invokeService('@loan-manage:remind:repay:ment', { ids }).then(res => {
          if (!!res && res.count) {
            showMessage.success(i18n.get('提醒成功'))
            closeLoanWithdrawFn && closeLoanWithdrawFn()
          }
        })
      }
    })
  }

  handleUpdateMoneyVisible = () => {
    const { updateMoneyVisible } = this.state
    this.setState({
      updateMoneyVisible: !updateMoneyVisible
    })
  }

  fnDelayAuto = loan => {
    const { privilegeId = '' } = this.props
    const { loanDetail } = this.state
    const autoDelayTimes = get(loan, 'allowModifyRepaymentDateConfig.autoDelayTimes')
    const autoDelayDays = get(loan, 'allowModifyRepaymentDateConfig.autoDelayDays')
    if (this.state.autoDelayTimes >= autoDelayTimes) {
      showMessage.info(i18n.get('最多只能顺延{count}次', { count: autoDelayTimes }))
      return
    }
    const repaymentDate = loanDetail.repaymentDate
    const date = moment(repaymentDate).add(autoDelayDays, 'd')
    const { setLastDay, lastRepayment } = this.fnSetLastDay(loan, date)
    let content = `${i18n.get('默认顺延')}${autoDelayDays}${i18n.get('天，')}${i18n.get(
      '「还款日期」将顺延至'
    )}${moment(date).format('YYYY-MM-DD')}${i18n.get(',是否顺延？')}`
    if (setLastDay) {
      content = `${i18n.get('默认顺延')}${autoDelayDays}${i18n.get('天，')}${i18n.get(
        '将超出「还款日期」的最大可选范围。「还款日期」仅能顺延至'
      )}${moment(lastRepayment).format('YYYY-MM-DD')}${i18n.get(',是否顺延？')}`
    }
    showModal.confirm({
      content: i18n.get(content),
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消'),
      onOk: () => {
        delayLoanInfo({
          loanInfoId: loanDetail.id,
          ...loan.allowModifyRepaymentDateConfig,
          setLastDay: setLastDay,
          privilegeId
        })
          .then(result => {
            if (result && result.value) {
              const autoDelayTimes = this.state.autoDelayTimes + 1
              this.setState({ autoDelayTimes })
              this.fnGetLoanInfo(loanDetail.id)
              showMessage.info(i18n.get('设置成功'))
            }
          })
          .catch(e => {
            return showMessage.error(e.msg || e.message)
          })
      }
    })
  }

  fnGetLoanInfo = id => {
    api.invokeService('@bills:get:loanpackage:detail:info', { id }).then(res => {
      this.setState({ loanDetail: res.value })
    })
  }

  fnRepaymentYear = repaymentDate => {
    const year = moment(repaymentDate).year()
    return moment([year, 11, 31]).valueOf()
  }
  handleDelay = loan => {
    const { privilegeId = '' } = this.props
    const { loanDetail } = this.state
    const select = get(loan, 'allowModifyRepaymentDateConfig.selected')
    if (select === 'auto') {
      this.fnDelayAuto(loan)
    } else {
      api.open('@bills:SelectDateModal', { billDetails: this.state.billDetails }).then(result => {
        const delayDate = result.date
        const { setLastDay } = this.fnSetLastDay(loan, delayDate)
        delayLoanInfo({
          loanInfoId: loanDetail.id,
          ...loan.allowModifyRepaymentDateConfig,
          delayDate,
          setLastDay,
          privilegeId
        })
          .then(res => {
            if (res && res.value) {
              this.fnGetLoanInfo(loanDetail.id)
              showMessage.info(i18n.get('设置成功'))
            }
          })
          .catch(e => showMessage.error(e.msg || e.message))
      })
    }
  }

  fnSetLastDay = (loan, delayDate) => {
    const { loanDetail } = this.state
    const overDate = get(loan, 'limitRepaymentDateRange.overDate')
    const repaymentDate = loanDetail.repaymentDate
    const select = get(loan, 'limitRepaymentDateRange.selected')
    const lastRepayment = select === 'year' ? this.fnRepaymentYear(repaymentDate) : overDate
    const setLastDay = Number(moment(delayDate).valueOf()) > Number(lastRepayment)
    return { setLastDay, lastRepayment }
  }

  fnGetRecordFilterShift = (repaymentRecords = []) => {
    return repaymentRecords.filter(
      record =>
        record.repaymentType !== 'SHIFT' &&
        record.repaymentType !== 'DELAY' &&
        record.repaymentType !== 'TRANSFER' &&
        record.repaymentType !== 'REFUSE' &&
        record.repaymentType !== 'CONFIRM'
    )
  }

  renderCloseLoanButton = () => {
    const { isNeedCashierNode, billDetails, loanDetail, isShowUpdateMoneyBtn } = this.state
    let { showCloseLoanButton, privilegeId, privilegeAcitons = [] } = this.props
    const { transfer } = loanDetail
    if (showCloseLoanButton) {
      const record = this.fnGetRecordFilterShift(loanDetail.repaymentRecords)
      let buttons = []

      if (!privilegeId || privilegeAcitons.includes('freeflow.shared')) {
        buttons.push({ label: i18n.get('共享'), main: true, onClick: this.handleShare })
      }
      if (!privilegeId || privilegeAcitons.includes('freeflow.closeLoan')) {
        buttons.push({ label: i18n.get('关闭借款'), onClick: this.handleCloseLoanPackage })
      }
      if (!transfer && (!privilegeId || privilegeAcitons.includes('freeflow.shift'))) {
        buttons.unshift({ label: i18n.get('转交'), main: true, onClick: this.handleTransferLoanPackage })
      }
      if (
        !isNeedCashierNode &&
        !!record &&
        record.length === 1 &&
        (!privilegeId || privilegeAcitons.includes('freeflow.cancelPayment'))
      ) {
        buttons.push({ label: i18n.get('撤销支付'), onClick: this.handleWithdraw })
      }
      if (!privilegeId || privilegeAcitons.includes('freeflow.remindRepayment')) {
        buttons.push({ label: i18n.get('提醒还款'), onClick: this.handleRemindRepayMent })
      }
      const configs = get(billDetails, 'form.specificationId.configs', [])
      const loan = configs.find(line => line.ability === 'loan')
      const select = get(loan, 'allowModifyRepaymentDateConfig.selected')
      if (select && (!privilegeId || privilegeAcitons.includes('freeflow.deferredRepayment'))) {
        buttons.push({ label: i18n.get('延期还款'), onClick: this.handleDelay.bind(this, loan) })
      }
      return (
        <>
          <ButtonGroup buttons={buttons} />
          {isShowUpdateMoneyBtn && (!privilegeId || privilegeAcitons.includes('freeflow.changeRemain')) && (
            <ButtonGroup buttons={[{ label: i18n.get('修改待还款金额'), onClick: this.handleUpdateMoneyVisible }]} />
          )}
        </>
      )
    }
    return null
  }

  renderRepayButton() {
    if (!this.props.showRepaymentButton) return ''
    const {
      userInfo: {
        staff: { id }
      }
    } = this.props
    const {
      loanDetail: { transfer, ownerId, sharedOwnerIds },
      permissionLoan
    } = this.state

    if ((!transfer && ownerId?.id === id) || permissionLoan || !!sharedOwnerIds?.find(i => i?.id == id)) {
      let btnDisabled =
        !this.state.loanDetail.remain ||
        (this.state.repayInfo && this.state.repayInfo.state === 'APPROVE') ||
        this.state.loanDetail?.flowSpecificationId?.configs?.some(
          config => config.ability === 'manualRepayment' && config.allowManualRepayment
        )
      return (
        <>
          <ButtonGroup
            buttons={[
              { label: i18n.get('还款'), main: true, onClick: this.handleRepaymentButton, disabled: btnDisabled }
            ]}
          />
          {<ButtonGroup buttons={[{ label: i18n.get('共享'), main: true, onClick: this.handleShare }]} />}
        </>
      )
    }
  }

  getIsOwner = () => {
    const { loanDetail } = this.state
    const { userInfo } = this.props
    return loanDetail.ownerId === userInfo.staff.id
  }

  handleGetResult = () => {
    const {
      loanDetail: { id }
    } = this.state
    const ids = [id]
    this.setState({
      refreshLoading: true
    })
    fixRepaymentRecord(ids)
      .then(result => {
        // 更新借款包列表
        getLoanPackageList && getLoanPackageList()
        // 更新审核中logs
        this.getLoanLogs()
        // 更新详情页面
        this.handleGetRepayInfo()
        api.invokeService('@layout5:refresh:menu:data')
        this.setState({
          refreshLoading: false
        })
      })
      .catch(e => {
        showMessage.error(e.msg || e.message)
        this.setState({
          refreshLoading: false
        })
      })
  }

  renderRefreshButton = refreshLoading => {
    return (
      <div className="refresh-btn">
        {refreshLoading ? (
          <>
            <EKBIcon name={'#EDico-refresh2'} />
            {i18n.get('数据刷新中...')}
          </>
        ) : (
          <div onClick={() => this.handleGetResult()}>
            {i18n.get('没找到记录？试试：')}
            <EKBIcon name={'#EDico-refresh2'} />
            {i18n.get('刷新数据')}
          </div>
        )}
      </div>
    )
  }

  renderLog = () => {
    const { loanDetail, refreshLoading } = this.state
    const { repaymentRecords = [], totalMoneyNode, foreignCurrencyLoan } = loanDetail
    return (
      <div className="log-box">
        <div className="refresh-btn-wrapper">
          <h5>{i18n.get('操作历史')}</h5>
          {loanDetail?.state === 'REPAID' && this.renderRefreshButton(refreshLoading)}
        </div>
        <div className="log-box-con">
          <LoanLogsView
            loanInfo={loanDetail}
            list={repaymentRecords}
            code={totalMoneyNode?.standardStrCode || 'CNY'}
            foreignCurrency={foreignCurrencyLoan}
            onClickExpense={this.handleExpenseClick}
            onClickLoan={this.handleLoanClick}
            onDownload={this.fnFileDownload}
            onPreview={this.fnFilePreview}
          />
        </div>
      </div>
    )
  }

  fnFilePreview = ({ all: attachmentsData, line }) => {
    const newLine = {
      ...line,
      url: line?.fileId?.url ?? line?.url,
      fileId: line?.fileId?.id ?? line?.fileId,
      thumbUrl: line?.fileId?.thumbUrl ?? "",
    }
    preview()({ value: attachmentsData, line: newLine })
  }

  fnFileDownload = ({ line }) => {
    const newLine = {
      ...line,
      url: line?.fileId?.url ?? line?.url,
      fileId: line?.fileId?.id ?? line?.fileId,
      thumbUrl: line?.fileId?.thumbUrl ?? "",
    }
    download()(newLine)
  }

  renderRight = () => {
    const { loanLogs = {} } = this.state
    const { repaymentRecords = [], totalMoneyNode, foreignCurrencyLoan } = loanLogs

    return (
      <div className="log-box">
        <h5>{i18n.get('还款审核')}</h5>
        <div className="log-box-con">
          <LoanLogsView
            list={repaymentRecords}
            code={totalMoneyNode?.standardStrCode || 'CNY'}
            foreignCurrency={foreignCurrencyLoan}
            onClickExpense={this.handleExpenseClick}
            onClickLoan={this.handleLoanClick}
            onDownload={this.fnFileDownload}
            onPreview={this.fnFilePreview}
          />
        </div>
      </div>
    )
  }

  handleCommonOperate = mark => {
    const {
      loanDetail: { id }
    } = this.state
    const params = { id, mark }
    let { getLoanPackageList } = this.props
    shiftConfirmLoan(params)
      .then(result => {
        if (mark) {
          // 更新借款包列表
          getLoanPackageList && getLoanPackageList()
          // 更新审核中logs
          this.getLoanLogs()
          //更新详情页面
          this.handleGetRepayInfo()
          api.invokeService('@layout5:refresh:menu:data')
        } else {
          this.goBack()
        }
      })
      .catch(e => {
        this.goBack()
        return showMessage.error(e.msg || e.message)
      })
  }
  handleShareOperate = async (type: 'REJECT_SHARE' | 'CONFIRM_SHARE') => {
    try {
      const params = { id: this.state.loanDetail?.id, type }
      await shareLoanConfirm(params)
      this.props.getLoanPackageList?.()
      this.getLoanLogs()
      this.handleGetRepayInfo()
    } catch (e) {
      this.goBack()
      return showMessage.error(e.msg || e.message)
    }
  }
  goBack = () => {
    api.invokeService('@layout5:refresh:menu:data')
    let { stackerManager, keel, layer } = this.props
    stackerManager && stackerManager.clear()
    keel && keel.closeTo(0)
    layer && layer.emitCancel()
  }
  /**
   * @description 接收此借款
   */
  handleConfirm = () => {
    showModal.confirm({
      content: i18n.get('是否确认接收此借款'),
      onOk: () => {
        this.handleCommonOperate(true)
      },
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消')
    })
  }
  /**
   * @description 拒绝此借款
   */
  handleReject = () => {
    showModal.confirm({
      content: i18n.get('是否拒绝接收此借款'),
      onOk: () => {
        this.handleCommonOperate(false)
      },
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消')
    })
  }
  rejectShare = () => {
    showModal.confirm({
      content: i18n.get('是否拒绝接收此借款'),
      onOk: async () => {
        await this.handleShareOperate('REJECT_SHARE')
        this.goBack()
      },
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消')
    })
  }
  confirmShare = () => {
    showModal.confirm({
      content: i18n.get('是否确认接收此借款'),
      onOk: () => {
        this.handleShareOperate('CONFIRM_SHARE')
      },
      okText: i18n.get('确定'),
      cancelText: i18n.get('取消')
    })
  }
  get isShowBottom() {
    return !this.isShowConfirmShare && !this.isShowConfirmTransfer
  }
  get isShowConfirmTransfer() {
    return !!this.state.loanDetail?.transfer && this.state.loanDetail?.transferId === this.props.userInfo?.staff?.id
  }
  get isShowConfirmShare() {
    return this.state.loanDetail?.notConfirmOwnerIds?.map(item => item.id)?.includes(this.props.userInfo?.staff?.id)
  }
  getConfirmInfo = () => {
    let ownerInfo = {}
    let confirmText = ''
    let confirmAction = () => { }
    let rejectAction = () => { }
    if (this.isShowConfirmShare) {
      const logs = this.state.loanDetail?.repaymentRecords?.filter(item => item.repaymentType === 'SHARE')
      // 共享发起人取的的是ownerId
      const ownerInfo = logs?.[logs?.length - 1]?.ownerId
      confirmText = i18n.get('共享')
      confirmAction = this.confirmShare
      rejectAction = this.rejectShare
      return [ownerInfo, confirmText, confirmAction, rejectAction]
    }
    if (this.isShowConfirmTransfer) {
      const logs = this.state.loanDetail?.repaymentRecords?.filter(item => item.repaymentType === 'TRANSFER')
      // 转交发起人取的的是shiftStaffId
      ownerInfo = logs?.[logs?.length - 1]?.shiftStaffId
      confirmText = i18n.get('转交')
      confirmAction = this.handleConfirm
      rejectAction = this.handleReject
      return [ownerInfo, confirmText, confirmAction, rejectAction]
    }
  }
  renderConfirm = () => {
    if (!this.isShowConfirmShare && !this.isShowConfirmTransfer) {
      return <></>
    }
    const [ownerInfo, confirmText, confirmAction, rejectAction] = this.getConfirmInfo()
    return (
      <div className="share-confirm-remind">
        <div>
          <EKBIcon className="remind-icon" name="#EDico-plaint-default" />
          <NamePopover className="operator-name" info={ownerInfo} name={ownerInfo?.name} />
          <span style={{ marginLeft: 8 }}>
            {i18n.get('向您{__k0}了此借款，是否接收？接收后可核销此借款包或手动还款', { __k0: confirmText })}
          </span>
        </div>
        <div className="share-confirm-buttons">
          <div className="button" onClick={rejectAction}>
            {i18n.get('拒绝')}
          </div>
          <div className="button button-confirm" onClick={confirmAction}>
            {i18n.get('接收')}
          </div>
        </div>
      </div>
    )
  }

  updateSuccessData() {
    let { getLoanPackageList } = this.props
    // 更新借款包列表
    getLoanPackageList && getLoanPackageList()
    // 更新详情页面
    this.handleGetRepayInfo()

    showMessage.success(i18n.get('操作成功'))
  }

  handelClick = () => {
    if (this.isUpdateRepaymentMoney) {
      return
    }
    const { updateReason, updateMoney, loanDetail } = this.state
    const { privilegeId = '' } = this.props
    const money = loanDetail.foreignCurrencyLoan ? loanDetail.foreignRemain : loanDetail.remain
    const reg = /^[0-9.]*$/g
    const floatNumber = updateMoney.split('.')[1]

    if (updateMoney === '') {
      showMessage.error(i18n.get('金额不能为空'))
      return
    } else if (!reg.test(updateMoney)) {
      showMessage.error(i18n.get('金额不符合格式'))
      return
    } else if (floatNumber && floatNumber.length > 2) {
      showMessage.error(i18n.get('金额小数点不能超过两位小数'))
      return
    } else if (money < updateMoney) {
      showMessage.error(i18n.get('待还款金额修改时只可小于当前值，请修改后重试！'))
      return
    }

    if (!updateReason) {
      showMessage.error(i18n.get('修改原因不能为空'))
      return
    }
    this.isUpdateRepaymentMoney = true
    updateRepaymentMoney({
      loanInfoId: loanDetail.id,
      privilegeId,
      revise: { amount: updateMoney, reason: updateReason }
    })
      .then(data => {
        this.isUpdateRepaymentMoney = false
        if (!data) {
          return
        }
        this.setState({
          updateMoney: '',
          updateReason: ''
        })
        this.updateSuccessData()
        this.handleUpdateMoneyVisible()
      })
      .catch(err => {
        this.isUpdateRepaymentMoney = false
        showMessage.error(err.msg)
      })
  }

  handleReason = e => {
    this.setState({
      updateReason: e.target.value
    })
  }

  handelMoney = e => {
    this.setState({
      updateMoney: e.target.value
    })
  }

  render() {
    const { loanDetail, repayInfo, loanLogs, className, updateMoneyVisible, updateReason, updateMoney } = this.state
    const { flowId, privilegeId = '', applyRepayment, hideRepayStatus = false } = this.props
    const isOwner = this.getIsOwner()
    const remainMoney = loanDetail.foreignCurrencyLoan ? loanDetail.foreignRemain : loanDetail.remain
    const isEnableMC = !this?.permission?.isMCDisabled()
    return (
      <>
        <div
          className={classnames(styles['detail-wrap'], window.isNewHome && styles['layout5-content'])}
          id="loanDetail"
        >
          {this.renderConfirm()}
          <div className={className}>
            <div className="detail-left">
              <Chart
                loanDetail={loanDetail}
                loanLogs={loanLogs}
                isShowRepayInfo={!!repayInfo}
                repayInfo={repayInfo}
                hideRepayInfo={this.handleHideRepayInfo}
                onExpenseClick={this.handleExpenseClick.bind(this)}
                onLoanClick={this.handleLoanClick.bind(this)}
                width={this.props.size?.x}
                flowId={flowId}
                privilegeId={privilegeId}
                applyRepayment={applyRepayment}
                handleGetRepayInfo={this.handleGetRepayInfo}
                loanType={'fromLoan'}
                isOwner={isOwner}
                hideRepayStatus={hideRepayStatus}
              />
              {this.renderLog()}
            </div>
            <div className="detail-right">{this.renderRight()}</div>
          </div>
          {this.isShowBottom && (
            <div className="detail-footer">
              {isEnableMC && this.renderRepayButton()}
              {isEnableMC && this.renderCloseLoanButton()}
            </div>
          )}
        </div>

        <Modal
          title={i18n.get('修改待还款金额')}
          className={classnames(styles['m-update-money'])}
          visible={updateMoneyVisible}
          onOk={this.handelClick}
          onCancel={this.handleUpdateMoneyVisible}
          width={400}
        >
          <div className="item">
            <span>
              <label>*</label>
              {i18n.get('将待还款金额从')}【{thousandBitSeparator(new Big(remainMoney))}】{i18n.get('修改为')}{' '}
            </span>
            <Input placeholder={i18n.get('修改金额')} onChange={this.handelMoney} value={updateMoney} maxLength={10} />
          </div>
          <div className="item">
            <span>
              <label>*</label>
              {i18n.get('修改原因')}{' '}
            </span>
            <Input
              placeholder={i18n.get('请输入修改原因')}
              onChange={this.handleReason}
              value={updateReason}
              maxLength={50}
            />
          </div>
        </Modal>
      </>
    )
  }
}

export default LoanPackageDetail
