@import '~@ekuaibao/web-theme-variables/styles/colors';

.active-wrapper {
  width: 100%;
  &:last-child{
    margin-bottom: 0;

    :global {
      .item-wrapper {
        margin-bottom: 0;
      }
    }
  }

  :global {

    .item-wrapper {
      position: relative;
      cursor: move;
      box-sizing: border-box;
      border: 1px solid transparent;
      z-index: 0;

      &:not(.readonly) {
        &:hover {
          background-color: transparent !important;
          box-shadow: var(--eui-shadow-down-3);
        }
      }

      &.readonly {
        cursor: pointer;
        background: var(--eui-bg-body-overlay);
        border-color: transparent !important;
        &:hover {
          border: 1px solid var(--eui-line-divider-default) !important;
          background: var(--eui-bg-base);
        }
      }

      &:hover {
        z-index: 1;

        //border  : 1px dashed @primary-6;
        .close-button {
          visibility: visible;
          cursor: pointer;
        }
      }

      .close-button {
        position: absolute;
        right: 0;
        top: 0;
        width: 16px;
        height: 16px;
        background: @primary-6;
        visibility: hidden;

        &.custom-button {
          background: var(--eui-fill-hover);
          color: var(--eui-icon-n2);
          width: 22px;
          height: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0px 6px;
        }
      }
    }
  }
}