import { get } from 'lodash'
import { standardValueMoney } from './../../../../../lib/misc'
import InvoiceTaxInfo from './../../../../../elements/CarouselInvoiceReviewer/utils/InvoiceTaxInfo'
import { Fetch } from '@ekuaibao/fetch'
import { isElectronicAirAndTrain } from './../../../../../lib/InvoiceUtil'
export { isElectronicAirAndTrain }

export const ELECTRONIC_AIRCRAFT_INVOICE = 'ELECTRONIC_AIRCRAFT_INVOICE'
export const ELECTRONIC_TRAIN_INVOICE = 'ELECTRONIC_TRAIN_INVOICE'

export function fnGetValidList(invoiceList) {
  return invoiceList.filter(line => {
    let dataSource = get(line, 'dataSource') || []
    return dataSource.find(v => {
      const {
        sourceData: {
          master: { entityId, form },
          details,
          status
        },
        active,
        ischeck
      } = v
      if (entityId === 'system_发票主体' && status !== 'UNEXAMINED') {
        if (isElectronicAirAndTrain(v?.sourceData?.master?.form)) {
          return active && !!ischeck
        }
        //@i18n-ignore
        const isActive = details.filter(item => !!item.active && !!ischeck)
        return isActive.length
      } else {
        return active && !!ischeck
      }
    })
  })
}

export function fnGetDetailsNumMap(list) {
  const detailsNumMap = {}
  list.length > 0 &&
    list.forEach(item => {
      const { dataSource } = item
      dataSource.forEach(v => {
        detailsNumMap[v.sourceData.master.id] = v.sourceData.details.length
      })
    })
  return detailsNumMap
}

export function checkListName(list = []) {
  const data = { show: false }
  for (let i = 0; i < list.length; i++) {
    const it = list[i]
    if (it?.master?.entityId === 'system_航空运输电子客票行程单') {
      if (!it?.master?.form?.E_system_航空运输电子客票行程单_乘机人姓名) {
        const title = `${it?.master?.form[i18n.get('E_system_航空运输电子客票行程单_出发站')] || ''} - ${it?.master
          ?.form[i18n.get('E_system_航空运输电子客票行程单_到达站')] || ''}`
        const tagName = i18n.get('机票行程单')
        const name = i18n.get('乘机人姓名')
        const empty = i18n.get('不能为空')
        data.show = true
        data.title = `${title},${tagName}${name}${Fetch.defaultLanguage === 'en-US' ? ' ' : ''}${empty}`
        break
      }
    }
  }
  return data
}

export function fnChangeInvoiceActive(sourceDataDetails, lineDetails) {
  return sourceDataDetails.map(item => {
    const { id } = item
    lineDetails.forEach(line => {
      if (line.id === id) {
        item.active = false
      }
    })
    return item
  })
}

export function fnFilterInvoiceListData(invoiceListData, billsInvoiceMap) {
  return invoiceListData.map((item, index) => {
    let { dataSource } = item
    dataSource.forEach(line => {
      let {
        id,
        sourceData: { details = [], master }
      } = line
      if (
        IS_ZJZY &&
        line?.sourceData?.message?.indexOf('该发票在单据') != -1 &&
        line?.sourceData?.message?.indexOf('中已经关联') != -1 &&
        line?.sourceData?.message?.indexOf('请先保存单据再上传') === -1
      ) {
        const bill_code = window.localStorage.getItem('bill_code')
        const tipText = line.sourceData.message + '，请先保存单据再上传'
        if (line?.sourceData?.message?.indexOf(bill_code) !== -1) {
          line.message = tipText
          line.sourceData.message = tipText
        }
      }
      if (billsInvoiceMap.hasOwnProperty(id)) {
        const { details: dataList, detailKeys } = billsInvoiceMap[id]
        //增值税发票
        if (!!dataList.length) {
          let flag = 0
          details.forEach(oo => {
            if (detailKeys.includes(oo.index)) {
              oo.active = false
              flag++
            }
          })

          if (flag === details.length) {
            line.active = false
            master.active = false
            line.ischeck = false
            master.ischeck = false
            line.status = 'NO_RESULT'
            line.message = i18n.get('发票已导入，不可重复导入')
            if (IS_ZJZY) line.message = i18n.get('发票已导入，不可重复导入，请先保存费用明细再上传')
            line.sourceData.status = 'NO_RESULT'
            line.sourceData.message = i18n.get('发票已导入，不可重复导入')
            if (IS_ZJZY) line.sourceData.message = i18n.get('发票已导入，不可重复导入，请先保存费用明细再上传')
          }
        } else {
          line.active = false
          master.active = false
          line.ischeck = false
          line.status = 'NO_RESULT'
          line.message = i18n.get('票据已导入，不可重复导入')
          line.sourceData.status = 'NO_RESULT'
          line.sourceData.message = i18n.get('票据已导入，不可重复导入')
        }
      }
    })
    item.dataSource = dataSource
    return item
  })
}

export function fnReInitInvoiceList(line, cInvoiceList) {
  const { master } = line
  const newInvoiceList = cInvoiceList
    .map(item => {
      const { dataSource = [], bus } = item
      bus.emit('invoice:delete:checkedChildMap')
      const data = dataSource.filter(v => v.id !== master.id)
      if (!!data.length) {
        item.dataSource = data
        return item
      } else {
        delete item.dataSource
        return undefined
      }
    })
    .filter(oo => !!oo)
  return newInvoiceList
}

export function fnGetLastValidInvoiceList(invoiceList) {
  return invoiceList.find(item => {
    const dataSource = get(item, 'dataSource') || []
    return !!dataSource.find(line => {
      const {
        ischeck,
        active,
        sourceData: { details, master }
      } = line
      const { entityId } = master
      if (entityId === 'system_发票主体' && !isElectronicAirAndTrain(master.form)) {
        // @i18n-ignore
        return !!details.find(oo => {
          return oo.active && ischeck
        })
      } else {
        return line.ischeck && active
      }
    })
  })
}

const fnAddTaxInvoiceMaster = item => {
  const invoiceTaxInfo = new InvoiceTaxInfo()
  let taxAmount = invoiceTaxInfo.getTaxAmount(item)
  item['taxAmount'] = typeof taxAmount === 'object' ? taxAmount : standardValueMoney(taxAmount)
  return item
}

const fnAddTaxTicket = item => {
  let { master, taxAmount, taxRate } = item
  const { form } = master
  if (taxAmount == void 0) {
    taxAmount = form[i18n.get('E_税额')] != void 0 ? form[i18n.get('E_税额')] : standardValueMoney(0)
  }
  if (taxRate == void 0) {
    taxRate =
      form[i18n.get('E_税率')] && form[i18n.get('E_税率')] !== i18n.get('免税') ? form[i18n.get('E_税率')] : '0.00'
  }
  item['taxAmount'] = typeof taxAmount === 'object' ? taxAmount : standardValueMoney(taxAmount)
  item['taxRate'] = taxRate
  return item
}

export function fnAddTaxInfo(data) {
  return data.map(item => {
    const {
      master: { entityId }
    } = item
    return entityId === i18n.get('system_发票主体') ? fnAddTaxInvoiceMaster(item) : fnAddTaxTicket(item)
  })
}

export function fnUniqObjInArray(objarray, key) {
  if (!objarray || !Array.isArray(objarray)) {
    return objarray
  }
  let object = {}
  return objarray.reduce((item, next) => {
    console.log('item is ', item)
    if (!object[next[key]]) {
      object[next[key]] = true && item.push(next)
    }
    return item
  }, [])
}

/**
 * 格式化发票标题，处理空值情况
 * @param {string} title - 原始标题
 * @returns {string} 格式化后的标题
 */
export function getFormattedInvoiceTitle(title) {
  return title || i18n.get('暂未获取到开票方信息')
}

export function isElectronicTitle(form) {
  let title = ''
  if (form?.E_system_发票主体_发票类别 === ELECTRONIC_AIRCRAFT_INVOICE) {
    const start = form?.E_system_发票主体_出发站
    const end = form?.E_system_发票主体_到达站
    if (!start && !end) {
      title = null
    } else {
      title = `${start || ''} - ${end || ''}`
    }
  } else if (form?.E_system_发票主体_发票类别 === ELECTRONIC_TRAIN_INVOICE) {
    const start = form?.E_system_发票主体_上车车站
    const end = form?.E_system_发票主体_下车车站
    if (!start && !end) {
      title = null
    } else {
      title = `${start || ''} - ${end || ''}`
    }
  }
  return getFormattedInvoiceTitle(title)
}
