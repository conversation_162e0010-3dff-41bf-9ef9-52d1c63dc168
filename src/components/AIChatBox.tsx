import React from 'react'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { getBoolVariation } from '../lib/featbit'
import classNames from 'classnames'
import AIChat from '../plugins/bills/parts/right-part/billMore/AIChat'
import styles from './AIChatBox.module.less'

interface AIChatBoxProps {
  style?: React.CSSProperties
  onClose?: () => void
  className?: string
  bus?: any
  chatId?:string
}

export const AIChatBox: React.FC<AIChatBoxProps> = (props) => {
  const { style, onClose, className, bus , chatId} = props
  if (getBoolVariation('aprd-5394-ai-chat')) {
    return <div className={classNames(styles.chatWrapper, className)} style={style}>
      <div className={styles.chatHeader}>
        <span>AI 助理</span>
        {typeof onClose === 'function' &&
          <OutlinedTipsClose
            fontSize={16}
            color="var(--eui-icon-n1)"
            className={styles.closeIcon}
            onClick={() => {
              typeof onClose === 'function' && onClose()
            }}
          />
        }
      </div>
      <AIChat bus={bus} chatId={chatId}/>
    </div>
  }
  return null
}

export default AIChatBox