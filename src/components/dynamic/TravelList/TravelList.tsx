/*
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2022-03-04 16:31:45
 * @Last Modified by: zhang<PERSON><PERSON>
 * @Last Modified time: 2022-04-13 15:39:50
 */

import React, { PureComponent } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import styles from './TravelList.module.less'
import { wrapper } from '../../layout/FormWrapper'
import { app as api } from '@ekuaibao/whispered'
import { Button } from '@hose/eui'
import TravelItem from '../../../elements/new-travel-plan-item'
import { standardValueMoney } from '../../../lib/misc'
import AddTravelPlanForm from '../../../plugins/bills/layers/add-travel-plan-modal/AddTravelPlanForm'
import TravelClosedLoop from '../TravelClosedLoop'
import { getPrice, getTravelerId, sortList, getInitialTemplate, getTmcStatus } from './travel-util'
import { isEqual, cloneDeep, isString } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import moment from 'moment'
import { message } from '@hose/eui'
import { OutlinedDirectionSwitch, OutlinedTipsAdd } from '@hose/eui-icons'

interface valueObj {
  startDate: string
  endDate: string
  travelFromCity: string
  travelToCity: string
  money?: number
}

@EnhanceConnect(state => ({
  travelBlackListV3: state['@itinerary-manage'].travelBlackListV3
}))
// @ts-ignore
@EnhanceField({
  descriptor: {
    type: 'travel'
  },
  wrapper: wrapper(),
  validator: (field, props) => (rule, value, callback) => {
    if (!props.isDetail && (!value || value.length === 0)) {
      if (!field.optional) {
        let errorMsg = i18n.get('not-empty', { label: i18n.get(field.label) })
        return callback(errorMsg)
      }
    }
    return callback()
  }
})
export default class TravelList extends PureComponent<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      templateList: [],
      config: {},
      loading: true,
      travelerId: '',
      filterTravel: null,
      isDetailChange: false,
      travelerList: []
    }
  }

  private child?: any

  preGetValue = async validate => {
    const { isDetail, bus } = this.props
    if (isDetail && bus?.getValidateLevel) {
      const level = bus?.getValidateLevel()
      const value = await this.child?.validatorForm(level)
      return this.child?.formatValues2Detail(value)
    } else {
      return this.props.value
    }
  }
  componentDidMount = async () => {
    const { bus, field } = this.props
    const limitTravelTypeIds = field?.limitTravelTypeIds || []
    bus.on('set:tripdatalink:value', this.setTripDataLinkValue)
    bus.on('set:tripdatalink:traveler:change', this.updateMoney)

    bus.setValidateLevel(1)
    const initialTemplate = await getInitialTemplate(limitTravelTypeIds)
    const travelerId = await getTravelerId(bus)
    if (bus.has('save:travelTemplate')) {
      bus.emit('save:travelTemplate', initialTemplate)
    }
    this.initDefaultTravel(this.props)
    this.setState({
      templateList: initialTemplate,
      loading: false,
      travelerId
    })
    setTimeout(() => {
      this.getTravelerList()
    }, 100)
  }

  componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any): void {
    if (!isEqual(this.props.value, nextProps.value)) {
      this.initDefaultTravel(nextProps)
    }
  }

  getTravelerList = async (isChanged?: boolean) => {
    const { bus, isDetail } = this.props
    if (!isDetail) {
      const value = await bus.getFieldsValue()
      const submitter = value?.submitterId || {}
      const submitterId = isString(submitter) ? submitter : submitter?.id
      const travelerList = value?.travelers?.map(v => isString(v) ? v : v?.id).filter(i => i) || []
      if (submitterId && !travelerList.length) {
        travelerList.push(submitterId)
      }
      this.setState({ travelerList })
      if (isChanged && this.props.value?.length) {
        message.warning('更新出行人后，需要重新选择行程的场景')
      }
    } else {
      const billsValue = await api.invoke('get:bills:value')
      const value = await bus.getFieldsValue()
      const submitter = billsValue?.values?.submitterId || {}
      const submitterId = isString(submitter) ? submitter : submitter?.id
      const travelers = value?.travelers || []
      let travelerList = travelers?.map(v => isString(v) ? v : v?.id).filter(i => i) || []
      if (!travelerList.length) {
        const billTravelers = billsValue?.values?.travelers
        travelerList = billTravelers?.map(v => isString(v) ? v : v?.id).filter(i => i) || []
      }
      if (submitterId && !travelerList.length) {
        travelerList.push(submitterId)
      }
      this.setState({ travelerList })
    }
  }


  initDefaultTravel = props => {
    const { value, details, isDetail } = props
    let filterTravel = null
    if (isDetail && details?.length) {
      const index = details.findLastIndex(item => item.feeTypeForm?.travelPlanning?.length)
      if (index >= 0) {
        filterTravel = details[index].feeTypeForm.travelPlanning[0]
      }
    } else if (value?.length) {
      filterTravel = value[value.length - 1]
    }
    this.setState({ filterTravel })
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus.un('set:tripdatalink:value', this.setTripDataLinkValue)
    bus.un('set:tripdatalink:traveler:change', this.updateMoney)
  }

  setTripDataLinkValue = () => {
    const { bus, isDetail, onChange } = this.props
    if (isDetail) {
      bus.emit('set:trip:value')
    } else {
      onChange && onChange([])
    }
  }

  updateMoney = users => {
    setTimeout(() => {
      this.getTravelerList(true)
    }, 100)
    this.setMoney()
  }

  setMoney = async () => {
    let { template = [], value } = this.props
    let tripLists = cloneDeep(value)
    let amount = 0

    tripLists?.forEach(trip => {
      if (trip.type == '酒店' && trip.endDate && trip.startDate && trip.referencePrice) {
        const daysNumber = moment(trip.endDate).diff(trip.startDate, 'days') || 0
        amount += daysNumber * trip.referencePrice
      } else if (trip.referencePrice) {
        amount += trip.referencePrice
      } else {
        amount += 0
      }
    })

    if (api.has('get:bills:value')) {
      let value = await this.props.bus.getFieldsValue()
      if (value && value.travelers && value.travelers.length) {
        amount *= value.travelers.length
      }
    }

    if (template?.find(i => i.name == 'requisitionMoney') && Number(amount) > 0) {
      this.props?.form?.setFieldsValue({
        requisitionMoney: standardValueMoney(amount)
      })
    } else if (template?.find(i => i.name == 'amount') && Number(amount) > 0) {
      this.props?.form?.setFieldsValue({
        amount: standardValueMoney(amount)
      })
    }
  }

  handleOpen = async (trip, index) => {
    const price = await getPrice(trip)
    if (price) {
      const value = this.props.value.slice(0)
      value[index].referencePrice = price
      this.handleChange(value)
    }
  }

  handleGetPrice = async trip => {
    const price = await getPrice(trip)
    return price
  }

  handleAdd = async isSingle => {
    const { value = [], onChange, submitterId, billSpecification } = this.props
    const { templateList, travelerId, filterTravel, travelerList } = this.state
    api
      .open('@bills:AddTravelPlanModal', {
        templateList: templateList,
        editable: true,
        isSingle,
        travelerId,
        filterTravel,
        submitterId,
        billSpecification: billSpecification,
        travelerList: travelerList
      })
      .then((result: valueObj[]) => {
        // @ts-ignore
        const newList = sortList([...value, ...result])
        onChange && onChange(newList)
        this.handleAutoAssignExpenseTypeFields(newList)
      })
  }

  handleDelete = index => {
    const { value = [] } = this.props
    value.splice(index, 1)
    this.handleChange(value)
    this.initDefaultTravel(this.props)
  }

  handleChange = list => {
    let { onChange, isDetail } = this.props
    let { isDetailChange } = this.state
    const result = sortList(list)
    if (isDetail) {
      if (isDetailChange == false) {
        this.setState({
          isDetailChange: true
        })
      } else {
        setTimeout(() => {
          this.setMoney()
        }, 300)
      }
    } else {
      setTimeout(() => {
        this.setMoney()
      }, 300)
    }
    onChange && onChange(result)
    this.handleAutoAssignExpenseTypeFields(result)
  }

  handleShowDetail = async (line, index, editable) => {
    const { value = [], submitterId, billSpecification } = this.props
    const { templateList, travelerId, travelerList } = this.state
    let rangeData = templateList?.find(template => template?.name === line?.type)?.dateTypeField?.dateType === 'multiple'

    api
      .open('@bills:AddTravelPlanModal', {
        bus: this.props.bus,
        templateList: templateList,
        editable: editable,
        value: line,
        isSingle: true,
        travelerId,
        submitterId,
        billSpecification: billSpecification,
        relatedOrder: !(editable || rangeData),
        travelerList
      })
      .then((result: valueObj[]) => {
        const rObj = result[0]
        if (index === value?.length - 1) {
          this.setState({ filterTravel: rObj })
        }
        value.splice(index, 1, rObj)
        this.handleChange(value)
      })
  }

  onValueChange = values => {
    const cur_tripsList: any[] = [
      {
        startDate: values?.date?.[0],
        endDate: values?.date?.[1],
        travelFromCity: values?.city?.[0],
        travelToCity: values?.city?.[1]
      }
    ]
    this.handleAutoAssignExpenseTypeFields(cur_tripsList)
  }

  handleAutoAssignExpenseTypeFields = (cur_tripsList: any[]) => {
    let params = {
      travelData: {
        travelPlanning: cur_tripsList
      }
    }
    api.invokeService('@bills:get:travel:date:range', params).then(result => {
      const { startTime, endTime, accommodation, departure, destination } = result?.value
      this.props?.bus.emit('assign:date:value', startTime == 0 ? +new Date() : startTime)
      this.props?.bus.emit('assign:date:range:value', [
        startTime == 0 ? +new Date() : startTime,
        endTime == 0 ? +new Date() : endTime
      ])

      this.props?.bus.emit('assign:city:value', {
        accommodation: this.handleCityValue(accommodation),
        departure: this.handleCityValue(departure),
        destination: this.handleCityValue(destination)
      })
    })
  }

  handleCityValue = (cityValue: any) => {
    let handle_cityValue = ''
    if (cityValue) {
      if (cityValue.split('},')?.length > 1) {
        handle_cityValue = cityValue.split('},')[0] + '}]'
      } else {
        handle_cityValue = cityValue
      }
    }
    return handle_cityValue
  }

  render() {
    const { value, isDetail, submitterId, billSpecification, travelBlackListV3, field } = this.props
    const { templateList, loading, travelerId, filterTravel, travelerList } = this.state
    const tmsState = getTmcStatus('cargo_travel_management')
    if (!tmsState) {
      return <div className={styles['data-link-red']}>{i18n.get('企业购买的授权已过期')}</div>
    }
    const hoseState = getTmcStatus('120209')
    if (loading) return null
    let prevItem = null
    return (
      <div className={`${styles.travelList} ${styles.mt}`}>
        <TravelClosedLoop value={value} />
        {!isDetail &&
          value?.map((line, index) => {
            const isNewGroup = prevItem?.startDate !== line.startDate
            const editable = !travelBlackListV3?.includes(line.travelId)
            const comp = (
              <TravelItem
                key={index}
                isNewGroup={isNewGroup}
                onlyOne={value.length === 1}
                line={line}
                onShowDetail={() => this.handleShowDetail(line, index, editable)}
                onDeleteTrip={() => this.handleDelete(index)}
                onOpen={() => this.handleOpen(line, index)}
                showButton={tmsState && hoseState}
                editable={editable}
                travelerList={travelerList}
              />
            )
            prevItem = line
            return comp
          })}
        <div id="map-container"></div>
        <div className="trip-select-wrapper">
          {isDetail ? (
            (() => {
              const editable = value?.length > 0 ? !travelBlackListV3?.includes(value[0]?.travelId) : true
              let rangeData = templateList?.find(template => template?.name === value?.[0]?.type)?.dateTypeField?.dateType === 'multiple'
              return (
                <>
                  <AddTravelPlanForm
                    ref={ref => {
                      this.child = ref
                    }}
                    value={value?.length > 0 && value[0]}
                    isSingle={true}
                    templateList={templateList}
                    editable={editable}
                    onDetailChange={this.handleChange}
                    isDetail={true}
                    showButton={hoseState}
                    getPrice={this.handleGetPrice}
                    travelerId={travelerId}
                    filterTravel={!value?.length && filterTravel}
                    submitter={submitterId}
                    onValueChange={this.onValueChange}
                    billSpecification={billSpecification}
                    relatedOrder={!(editable || rangeData)}
                    field={field}
                    travelerList={travelerList}
                  />
                </>
              )
            })()
          ) : (
            <div className="add-button-wrapper">
              <Button
                size="small"
                className='mr-8'
                data-testid="add-single-trip"
                category="secondary"
                icon={<OutlinedTipsAdd />}
                onClick={() => this.handleAdd(true)}
              >
                {i18n.get('添加单程')}
              </Button>
              <Button
                size="small"
                data-testid="add-round-trip"
                category="secondary"
                icon={<OutlinedDirectionSwitch />}
                onClick={() => this.handleAdd(false)}
              >
                {i18n.get('添加往返')}
              </Button>
            </div>
          )}
        </div>
      </div>
    )
  }
}
