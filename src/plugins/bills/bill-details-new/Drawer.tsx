import React, { useEffect } from 'react'
import { EnhanceDrawer } from '@ekuaibao/enhance-layer-manager'
import style from './index.module.less'
import { listenerToDrawerResize, billDrawerConfig } from '../../../elements/configure/bill-drawer'
import BillDetails, { BillDetailsPropsWrapper } from './BillDetailsWrapper'
import useDrawerCloseEvent from './useCloseEvent'

interface BillDetailsOfDrawerProps extends BillDetailsPropsWrapper {}

const BillDetailsInDrawer = (props: BillDetailsOfDrawerProps) => {

  useDrawerCloseEvent(props.layer);

  useEffect(() => {
    const _removeListenerToDrawerResize = listenerToDrawerResize()
    return () => {
      _removeListenerToDrawerResize()
    }
  }, [])

  return <BillDetails {...props} />
}

export default EnhanceDrawer({
  get width() {
    return billDrawerConfig.minWidth
  },
  isEUI: true,
  className: `${style['bill-no-modal-header']} ${style['bill-no-modal-mask']}`
})(BillDetailsInDrawer)
