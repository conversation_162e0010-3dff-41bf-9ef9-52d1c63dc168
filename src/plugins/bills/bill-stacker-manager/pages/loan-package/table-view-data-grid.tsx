import { FC, forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'

import { app as api } from '@ekuaibao/whispered'
const filtersFixer = api.require('@lib/filtersFixer')

import { Resource, Fetch } from '@ekuaibao/fetch'
import parseQuery2Select from '@ekuaibao/lib/lib/parseQuery2Select'
import parseSelectUsePropertySet from '@ekuaibao/lib/lib/parseSelectUsePropertySet'

const Money = api.require('@elements/puppet/Money')

const LoaderWithLegacyData = api.require('@elements/data-grid-v2/LoaderWithLegacyData')
const { searchOptions } = api.require('@lib/data-grid-v2/CustomSearchUtil')

import { columnLoan } from './column-helper'

interface IProps {
  size: any
  baseDataProperties: any
  tabKey: 'applying' | 'pending-confirmation' | 'pending-repayment' | 'completed'
  bus: any
}

const flowResource = new Resource('/api/flow/v1/flows')
const myLoadResource = new Resource('/api/v2/loan/loanInfo')
const filterResource = new Resource('/api/flow/v2/filter')

const BLACK_LIST = ['writtenOff', 'expense', 'feeDetail', 'apportion', 'requisition', 'apply']

function getProperties(Properties: any) {
  return Properties.filter(({ ability }) => !BLACK_LIST.includes(ability))
}

export interface LoanDataGridRef {
  getCurrentDataSource: () => any[]
}

const LoanDataGrid = forwardRef<LoanDataGridRef, IProps>((props, ref) => {
  const { size, baseDataProperties, tabKey, bus } = props

  const scenes = [{ text: i18n.get('全部'), scene: 'all', active: true, sceneIndex: 'all' }]
  const all = getAllColumns(tabKey)

  const mapping = { all, exclude: ['pendingFlag', 'remain'] }

  const properties = getProperties(baseDataProperties)

  const [scenesSetting, setScenesSetting] = useState<any>(null)
  const [readyGetSence, setReadyGetSence] = useState(false)
  const dataSource = useRef<any[]>([])

  const scenesType = useMemo(() => `MY_LOAN_${tabKey.toUpperCase()}`, [tabKey])

  useImperativeHandle(ref, () => ({
    getCurrentDataSource: () => {
      return dataSource.current
    }
  }))


  const initScenes = () => {
    filterResource.GET('/$type', { type: scenesType }).then(({value = null}) => {
      if (!value?.filter) {
        return
      }
      const {filter} = value
      const scenes = filter.map((item: any) => JSON.parse(item))
      setScenesSetting(scenes)
    }).then(()=>{
      setReadyGetSence(true)
    })
  }

   // 获取场景配置
   useEffect(() => {
    initScenes()
    bus.un('initScenes:action', initScenes)
    bus.on('initScenes:action', initScenes)

    return () => {
      bus.un('initScenes:action', initScenes)
    }
  }, [])


  const prefixColumns =
    tabKey === 'applying'
      ? {
          state: '$',
          '*': 'form'
        }
      : {
          remain: '',
          pendingFlag: '',
          state: 'flowId',
          '*': 'flowId.form'
        }

  const applyFetch = (params = {}) => {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage === 'en-US'

    const findScene = scenesSetting?.find(item => item.text === params.scene)

    const _params = {...params}
    delete _params.scene

    const query = parseQuery2Select(_params, undefined, 'flow', lang)

    const content = parseSelectUsePropertySet(properties, params.options)

    const sceneFiltersQuery = findScene ? filtersFixer(findScene, 'flow', baseDataProperties) : ''

    query
      .filterBy('formType == "loan"')
      .filterBy(sceneFiltersQuery)
      .filterBy(`state.in("approving","paying","sending","receiving","rejected","nullify")`)
      .select(`form(${content ? content + ',' : ''}...),...`)

    const queryString = query.value()

    return flowResource.POST('/my', queryString).then(res => {
      const { count, items } = res
      dataSource.current = items
      return { dataSource: items, total: count }
    })
  }

  const fetchFn = (params = {}) => {
    const lang = Fetch.staffSetting ? Fetch.staffSetting.language : Fetch.defaultLanguage === 'en-US'

    const findScene = scenesSetting?.find(item => item.text === params.scene)

    const _params = {...params}
    delete _params.scene

    const query = parseQuery2Select(_params, undefined, undefined, lang)

    const content = parseSelectUsePropertySet(properties, params.options)
    const sceneFiltersQuery =  findScene ? filtersFixer(findScene, undefined, baseDataProperties) : ''

    const state = { completed: 'PAID', 'pending-repayment': 'REPAID', 'pending-confirmation': 'REPAID' }[tabKey]

    query
      .filterBy(`state.in("${state}")`)
      .filterBy('active==true')
      .filterBy(sceneFiltersQuery)
      .select(`ownerId(id,name,enName),flowId(ownerId(id,name,enName),form(${content ? content + ',' : ''}...),...),...`)

    const queryString = query.value()
    const isPendingConfirm = tabKey === 'pending-confirmation'

    return myLoadResource.POST('/mine', queryString, { isPendingConfirm }).then(res => {
      const { count, items } = res
      items.forEach((v: any) => {
        if(v?.loanDate && !v?.flowId?.form?.loanDate) {
          v.flowId.form.loanDate = v.loanDate
        }
      })
      dataSource.current = items
      return { dataSource: items, total: count }
    })
  }
  //按可视列查询列表时需等待筛选场景获取可见列
  if (!readyGetSence && api.getState()['@common'].toggleManage?.['tg_flowlist_filed_filter']) return ''
  return (
    <LoaderWithLegacyData
      newSearch={true}
      searchOptions={searchOptions(tabKey === 'applying' ? 'form' : 'flowId.form')}
      bus={bus}
      baseDataProperties={baseDataProperties}
      fetch={tabKey === 'applying' ? applyFetch : fetchFn}
      size={size}
      scenes={scenesSetting ?? scenes}
      prefixColumns={prefixColumns}
      columnMinWidth={160}
      mapping={mapping}
      selectAllBtnStyles={{ color: 'var(--brand-base)' }}
      groupPanel={{ visible: true }}
      activeSceneIndex="all"
      scenesType={scenesType}
      resource={filterResource}
      specialColumn={columnLoan(tabKey)}
      otherColumns={otherColumn}
      isMultiSelect={false}
      onEditScenes={(scenes: any) => { console.log(scenes) }}
    />
  )
})

export default LoanDataGrid

const otherColumn = () => [
  {
    title: '待确认类型',
    dataIndex: 'pendingFlag',
    sorter: false,
    width: 200,
    dataType: 'text',
    key: 'pendingFlag',
    className: 'fs-14',
    label: '待确认类型',
    value: 'pendingFlag',
    render: val => {
      const map = ['', '转交待确认', '共享待确认', '转交和共享都待确认']
      return map?.[val] || ''
    }
  },
  {
    title: i18n.get('剩余待还（元）'),
    dataIndex: 'remain',
    sorter: true,
    width: 200,
    dataType: 'money',
    filterType: 'money',
    key: 'remain',
    className: 'fs-14',
    label: i18n.get('剩余待还（元）'),
    value: 'remain',
    render: (val, data) => {
      const currencySymbol = data?.totalMoneyNode?.standardSymbol
      return (
        <div className="fw-500">
          <Money value={val} currencySymbol={currencySymbol} />
        </div>
      )
    }
  }
]

function getAllColumns(type) {
  if (type === 'pending-confirmation') {
    return [
      'code',
      'title',
      'pendingFlag',
      'submitterId',
      'ownerId',
      'loanDate',
      'repaymentDate',
      'loanMoney',
      'remain'
    ]
  }

  if (type === 'pending-repayment') {
    return ['code', 'title', 'submitterId', 'ownerId', 'loanDate', 'repaymentDate', 'loanMoney', 'remain']
  }

  return ['code', 'title', 'submitterId', 'ownerId', 'loanDate', 'repaymentDate', 'loanMoney']
}
