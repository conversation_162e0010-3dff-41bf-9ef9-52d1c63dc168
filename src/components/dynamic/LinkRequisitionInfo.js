/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/21.
 */
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { EnhanceConnect } from '@ekuaibao/store'
import { isDisable } from '../utils/fnDisableComponent'
import styles from './ExpenseLink.module.less'
import EKBIcon from '../../elements/ekbIcon'
import RequisitionInfo from '../../elements/puppet/RequisitionInfo'
import { app as api } from '@ekuaibao/whispered'
import { get } from 'lodash'
import { getBoolVariation } from '../../lib/featbit'
import { Button } from '@hose/eui'
import { OutlinedTipsAdd } from '@hose/eui-icons'

@EnhanceField({
  descriptor: {
    type: 'linkRequisitionInfo'
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => {
  return {
    applyList: state['@bills'].applyList,
    requisitionDataSource: state['@bills'].requisitionDataSource,
    dimentionCurrencyInfo: state['@bills'].dimentionCurrencyInfo
  }
})
export default class LinkRequisitionInfo extends PureComponent {
  constructor(props) {
    super(props)
    this.state = { applyList: [] }
  }
  setList = (applyList = []) => {
    let { value, billSpecification } = this.props
    //当前单据的类型
    let applyContentRule = billSpecification.configs.filter(v => v.ability === 'requisition')[0].applyContentRule
    //筛选申请事项
    let applyListFilter = applyList.filter(
      v => v.specificationId.configs.filter(v => v.ability === 'requisition')[0]?.applyContentRule === applyContentRule
    )
    //value为已补充申请事项的单据  将事项关闭的处理显示为 已关闭
    if (typeof value === 'object' && value) {
      if (value.state === 'PROCESS') {
        value.label = value.label || value.name
      } else {
        value.label = value.label || i18n.get(`{__k0}（已关闭）`, { __k0: value.name })
      }
      //当前单据补充申请的类型
      let linkRequisitionInfo = null
      if (typeof value.specificationId === 'object') {
        linkRequisitionInfo = value.specificationId.configs.filter(v => v.ability === 'requisition')[0].applyContentRule
      }
      let isUnshift = true
      applyListFilter.forEach((v, i) => {
        if (v.id === value.id) {
          isUnshift = false
          applyContentRule === linkRequisitionInfo && applyListFilter.splice(i, 1, value)
        }
      })
      isUnshift && applyContentRule === linkRequisitionInfo && applyListFilter.unshift(value)
    }
    this.setState({ applyList: applyListFilter }, () => {
      const dataSource = this.fnGetDataSource(applyListFilter)
      api.invokeService('@bills:get:apply:data:source', dataSource)
    })
  }
  async componentDidMount() {
    await this.updateApplyFromExpense()
    this.props.bus.on('set:LinkRequisitionInfo', this.handleChangeSubmitterId)
    this.props.bus.on('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
  }
  componentWillUnmount() {
    this.props.bus.un('set:LinkRequisitionInfo', this.handleChangeSubmitterId)
    this.props.bus.un('dimention:multi:currency:change', this.handleDimentionMultiCurrencyChange)
  }

  updateApplyFromExpense = async (orderBy = []) => {
    let { applyList } = this.props,
      sortType = undefined,
      sortFieldName = undefined

    if (Array.isArray(orderBy) && orderBy.length) {
      sortType = orderBy[0].order
      sortFieldName = orderBy[0].value
    }

    const obj = await api.invokeService(
      '@bills:get:apply:event:orderBy',
      'PROCESS',
      undefined,
      '',
      sortFieldName,
      sortType
    )
    applyList = obj.items
    this.setList(applyList || [])
  }

  handleChangeSubmitterId = (newUser, oldUser) => {
    let { value, onChange } = this.props
    if (oldUser && newUser.id !== oldUser.id) {
      onChange && onChange()
      api.invokeService('@bills:get:DetailSubmitterIdList', { id: newUser.id }).then(action => {
        this.setList(action.items)
      })
    }
  }

  handleDimentionMultiCurrencyChange = () => {
    this.handleRemove()
  }

  fnGetDataSource = applyList => {
    const { dimentionCurrencyInfo } = this.props
    let data = []
    const dimentionCurrencyId = get(dimentionCurrencyInfo, 'dimention.id')
    if (dimentionCurrencyId) {
      data = applyList.filter(item => {
        const legalEntityMultiCurrency = get(item, 'flowId.form.legalEntityMultiCurrency')
        return dimentionCurrencyId === legalEntityMultiCurrency
      })
    } else {
      data = applyList.filter(item => {
        const legalEntityMultiCurrency = get(item, 'flowId.form.legalEntityMultiCurrency')
        return !legalEntityMultiCurrency
      })
    }
    return data
  }

  handleAddExpenseLink = async () => {
    const { value, onChange, bus, template, isModify, billSpecification } = this.props
    const id = await api.open('@bills:BillStackerModal', {
      viewKey: 'ExpenseLinkListView',
      updateApplyFromExpense: this.updateApplyFromExpense,
      viewTitle: i18n.get('补充申请'),
      value: value?.id ?? value,
      isSupplement: true //是否是补充申请,
    })
    const { applyList } = this.state
    const valueInObj = applyList.find(v => v.id === id)
    onChange?.(valueInObj)
    if (!id) return
    api.invokeService('@bills:get:flow-info', { id }).then(async res => {
      // TODO:  如果有人提 【选择补充申请后单据值呗覆盖掉 看到如下代码 直接驳回hotfix】总觉得这儿会有人提bug
      const linkValue = res.value?.form || {}
      const dataSource = {}
      template.forEach(oo => {
        const value = linkValue[oo.name]
        const hasKey = linkValue.hasOwnProperty(oo.name)
        if (hasKey) {
          dataSource[oo.name] = value
        }
      })
      // 处理提交人
      if (getBoolVariation('cyxq-75367-link-requisitionInfo-validate-submitter') && dataSource.submitterId?.id) {
        const expenseLinkSubmitterId = dataSource.submitterId.id
        const currentSubmitterId = this.props.submitterId?.id
        const delegatorList = await api.dataLoader('@common.delegators').load()
        const canDelegatorList = delegatorList?.filter(de => {
          return (
            de.id == currentSubmitterId &&
            de.delegateType === billSpecification?.type &&
            (!de.specIds ||
              de.specIds.length === 0 ||
              de.specIds.indexOf(billSpecification?.originalId?.id || billSpecification?.originalId) > -1)
          )
        })
        if (
          currentSubmitterId !== expenseLinkSubmitterId &&
          canDelegatorList.findIndex(de => de.id === expenseLinkSubmitterId) === -1
        ) {
          delete dataSource.submitterId
        }
      }
      if (isModify) {
        delete dataSource['u_行程规划']
        delete dataSource['details']
      } else {
        dataSource['u_行程规划'] = []
        dataSource['details'] = []
        dataSource.trips?.forEach(v => {
          const { tripForm = {} } = v || {}
          delete tripForm.tripId
        })
      }
      bus.setFieldsValue(dataSource)
      api?.logger?.info('补充申请赋值', {
        submitter: this.props.submitterId,
        specificationId: billSpecification?.id,
        specificationName: billSpecification?.name,
        linkValue: linkValue,
        resultForm: dataSource
      })
    })
  }

  handleRemove = () => {
    let { onChange } = this.props
    onChange()
  }
  handleDetailClick = () => {
    let { value = {}, bus, flowId } = this.props
    bus && bus.emit('check:requisition:detail', { detail: value, flowId })
  }
  render() {
    let { value, alterFlag } = this.props
    let { applyList } = this.state
    let valueInObj = typeof value === 'string' ? applyList.find(v => v.id === value) : value
    let disabled = alterFlag ? alterFlag : isDisable(this.props)
    return (
      <div id="expense-link-select" className={styles['expenselink-wrapper']}>
        <Button
          onClick={disabled ? undefined : this.handleAddExpenseLink}
          disabled={disabled}
          category='secondary'
          size='small'
          icon={<OutlinedTipsAdd />}
          data-testid="field-linkRequisition-select"
        >
          {i18n.get('补充申请')}
        </Button>
        <div className="import-select">
          {valueInObj && (
            <RequisitionInfo
              onDetailClick={this.handleDetailClick}
              editable={!disabled}
              value={valueInObj}
              onRemove={this.handleRemove}
              alterFlag={alterFlag}
            />
          )}
        </div>
      </div>
    )
  }
}
