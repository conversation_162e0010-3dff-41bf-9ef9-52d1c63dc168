@import '~@ekuaibao/eui-styles/less/token.less';
.currency-money {
  display: flex;
  flex-direction: column;
  &.hiddenRateLocal {
    :global {
      .money-foreign{
        .ant-col-11{
          width: 100%;
        }
        .ant-col-13{
          display: none;
        }
      }
      .money-local{
        display: none!important;
      }
    }
  }
  :global {
    .currency-money-label {
      margin-right: 8px;
      white-space: pre;
      color: rgba(29,43,61,0.50);
      font-size: 14px;
    }
    .ant-input-lg {
      font-weight: 500;
      min-width: 40px;
    }
    .ekb-auto-disabled{
      color: @color-black-2;
    }
    .ant-input-group-addon {
      background-color: #fcfcfc;
    }
    .chinese-money {
      font-weight: 400;
      color: rgba(29, 43, 61, 0.5);
    }
    .eui-chinese-money {
      margin-top: -8px;
      font: var(--eui-font-body-r1);
      color: var(--eui-text-caption);
    }
    .input-border {
      border-radius: 0 @radius-1 @radius-1 0;
    }
    .reset-rate-wrap {
      width: 32px;
      height: 32px;
      border: 1px solid #e6e6e6;
      border-radius: 4px;
      display: flex;
      align-items: center;
      &:hover {
        color: @color-brand-2;
        border-color: @color-brand-2;
      }
      .reset-rate {
        cursor: pointer;
        width: 18px;
        height: 18px;
        margin: 8px;
      } 
    }
    .selected-rate {
      color: @color-brand-2;
      border-color: @color-brand-2;
    }
    .reset-rate-disabled {
      .reset-rate {
        cursor: not-allowed;
        color: @color-black-3;
      }
      :hover {
        color: @color-black-3;
      }
    }
    .rate-input{
      .eui-input-group-addon{
        width: 96px;
      }
    }
    .eui-input-group-addon {
      padding: 0;
      text-align: left;
      .eui-icon {
        margin: 0 0 1px 4px;
      }
    }
  }
}

.eui-input-group-addon-title {
  padding: 5px 10px;
}

.currency-money-currency-title {
  height: 32px;
  min-width: 67px;
  font-size: 14px;
  padding: 0 12px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  color: rgba(29,43,61,0.50);
  background-color: #fcfcfc;
  border-bottom: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
  border-radius: @radius-1 0 0 @radius-1;
  :global {
    .currency-title {
      font-size: 14px;
      text-align: center;
      color: rgba(29,43,61,0.50);
    }

    .rate {
      font-size: 14px;
      text-align: center;
      color: rgba(29,43,61,0.50);
    }
    .img {
      width: 16px;
      height: 16px;
      margin-left: 4px;
      :hover {
        color: @color-brand-2;
      }
    }
  }
}

.currency-money-rate-refresh {
  position: absolute;
  top: 9px;
  right: -24px;
  width: 14px;
  height: 14px;
  cursor: pointer;
}

.currency-money-rate-dropdown {
  position: absolute;
  right: 0;
  top: -28px;

  :global {
    .eui-page-header {
      padding: 0;
    }
    .eui-page-header-heading-title {
      color: var(--eui-text-title);
      font: var(--eui-font-body-r1);
    }
  }
}
