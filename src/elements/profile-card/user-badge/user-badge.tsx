import React, { ReactNode, useMemo } from 'react'
import cx from 'classnames'
import { Avatar, Tag } from '@hose/eui'
import { getDisplayName } from '../../utilFn'
import { Labels, User } from './types'
import { XSAvatar } from './xs-avatar'

import styles from './user-badge.module.less'

export interface UserBadgeProps {
  /**
   * 是否显示头像，默认为true，如果UserInfo内没有头像
   */
  showAvatar?: boolean
  /**
   * 展示模式，tag为标签模式，text为文本模式，默认为 tag
   */
  mode: 'tag' | 'text'
  /**
   * 用户信息
   */
  user: User
  /**
   * 额外字段，如果需要展示额外字段，需要传入字段Id
   */
  extraField?: string
  /**
   * 中英文标签
   */
  extra?: ReactNode
  /**
   * 基础字段，默认为name字段，可传入其他字段，比如nickName
   */
  baseFiled?: string
  labels: Labels
  className?: string
}

export const UserBadge$ = ({
  showAvatar = true,
  user,
  className,
  mode = 'tag',
  baseFiled = 'name',
  extraField,
  labels,
  extra = null
}: UserBadgeProps) => {
  const curLabel = useMemo(() => {
    if (extraField) {
      return getDisplayName(labels)
    }
    return null
  }, [extraField, labels])

  const renderUserExtra = () => {
    if (user?.active === false) {
      return <span className={styles['name-plus__inactive']}>（{i18n.get('已离职')}）</span>
    }
    if (user?.authState !== undefined && user?.external !== undefined && !user.authState && !user.external) {
      return <span className={styles['name-plus__inAuthorized']}>（{i18n.get('已停用')}）</span>
    }
    return null
  }

  const nameOfConfig = useMemo(() => {
    if (baseFiled === 'name') {
      return getDisplayName(user)
    }
    return user[baseFiled] || '-'
  }, [baseFiled, user])

  const renderTextMode = () => (
    <span className={`${styles['name-plus']} translate-ignore-class`}>
      {nameOfConfig}
      {curLabel && <>（{curLabel}）</>}
      {renderUserExtra()}
      {extra}
    </span>
  )

  const renderTagMode = () => {
    return (
      <Tag icon={showAvatar ? <XSAvatar avatar={user.avatar} /> : null} className={cx(styles.tag, className)}>
        {renderTextMode()}
      </Tag>
    )
  }

  if (!user) {
    return null
  }

  return <>{mode === 'tag' ? renderTagMode() : renderTextMode()}</>
}
