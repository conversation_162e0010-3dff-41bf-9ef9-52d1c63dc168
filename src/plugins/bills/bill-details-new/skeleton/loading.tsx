import React, { useMemo } from 'react'
import { Skeleton, SkeletonParagraph, SkeletonNormalList } from '@hose/eui'
import LocalStore from '../../../../lib/local-store'
import { flowPartDrawerConfig, billDrawerConfig } from '../../../../elements/configure/bill-drawer'
import styles from './loading.module.less'

const localStore = new LocalStore('billMoreInfoWidthExpand')

export default function LoadingSkeleton({ mode }: { mode: 'list' | 'table' }) {

  const isExpand = useMemo(() => localStore.get() ?? true, [])

  // 同步 BillMore/MoreInfo.tsx 中的宽度信息
  const getMoreInfoWidth = () => {
    const isZh = i18n.currentLocale === 'zh-CN'

    return !isExpand
      ? (isZh ? 48 : 72)
      : mode === 'list' || (billDrawerConfig.width <= (400 + 320) ) ? flowPartDrawerConfig.listModeDefaultWidth : flowPartDrawerConfig.tableModeDefaultWidth
  }

  return <div className={styles['loading-skeleton']}>
    <header className={styles['header']}>
      <Skeleton animated type='button' style={{ backgroundColor: 'var(--eui-bg-body)' }} />
      <Skeleton animated type='button' style={{ backgroundColor: 'var(--eui-bg-body)' }} />
    </header>
    <div className={styles['content']}>
      <div className={styles['bill-details']}>
        <SkeletonNormalList showTitle showText lineCount={40} />
      </div>
      <div className={styles['more-info']} style={{ width: getMoreInfoWidth() }}>
        {
          isExpand
          ? <SkeletonNormalList showTitle showText lineCount={40} />
          : <>
            <Skeleton animated type='button' style={{ width: '100%' }} />
            <Skeleton animated type='button' style={{ marginTop: 8, width: '100%' }} />
          </>
        }
      </div>
    </div>
  </div>
}