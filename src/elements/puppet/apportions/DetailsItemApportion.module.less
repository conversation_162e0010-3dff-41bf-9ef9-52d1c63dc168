.cost-share {
  flex: 1;
  :global {
    .title {
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      .text {
        color: var(--eui-text-title);
        font: var(--eui-font-body-b1);
      }
      .total {
        color: var(--eui-text-title);
        font: var(--eui-font-body-r1);
      }

    }
    .line-top {
      border-top: 1px dashed var(--eui-line-divider-default);
      margin-bottom: 12px;
    }
    .cost-share-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      color: var(--eui-text-caption);
      font: var(--eui-font-note-r2);
      .cost-share-item-content {
        position: relative;
        padding-left: 8px;
      }
      .money-risk-warning {
        padding-left: 12px;
        display: flex;
        .exceed-standard-risk-wrapper {
          display: flex;
        }
        .expand-btn {
          margin-left: 20px;
          color: var(--brand-base);
        }
        .base-currency, .quote-currency {
          color: var(--eui-text-title);
          font: var(--eui-num-note-b2);
        }
        .exchanged-base-currency {
          color: var(--eui-text-placeholder);
          font: var(--eui-num-note-b2);
        }
      }
    }
    .item-remark {
      margin: 4px 0 0 8px;
      padding: 4px 8px;
      border-radius: 4px;
      background: var(--eui-transparent-n900-5);
      color: var(--eui-text-caption);
      font: var(--eui-font-note-r2);
      white-space: pre-wrap;
    }
    .point {
      display: inline-block;
      width: 4px;
      height: 4px;
      background-color: var(--eui-decorative-neu-400);
      border-radius: 50%;
      position: absolute;
      left: 0;
      top: 7px;
    }
    .more {
      display: flex;
      align-items: center;
      color: var(--eui-primary-pri-500);
      font: var(--eui-font-note-r2);
      .text {
        margin-right: 4px;
      }
    }
  }
}

.cost-share-padding {
  padding: 12px 0 0px 38px;
}
