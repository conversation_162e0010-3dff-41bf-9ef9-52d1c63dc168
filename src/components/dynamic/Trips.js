/**
 * <AUTHOR> <<EMAIL>>
 * @date 2018-02-05
 * @description 差旅行程
 */

import styles from './Trips.module.less'
import React, { PureComponent } from 'react'
import moment from 'moment'
import { EnhanceField } from '@ekuaibao/template'
import { debounce } from '@ekuaibao/lib/lib/lib-util'
import { fnCheckCompleted } from '../validator/validator'
import { cloneDeep, get, uniqBy } from 'lodash'
import { EnhanceConnect } from '@ekuaibao/store'
import { isDisable } from '../utils/fnDisableComponent'
import { app as api } from '@ekuaibao/whispered'
import { showMessage } from '@ekuaibao/show-util'
import { MoneyMath } from '@ekuaibao/money-math'
import { standardValueMoney } from '../../lib/misc'
const OPENLIST = ['FLIGHT', 'HOTEL', 'TRAIN']
const POWER_CODE = '120209' //合思商城订购管理
import TripItem from './dataLinkEdit/TripItem'
import { getDays, getWeek } from '../../lib/lib-util'
import { formatDateTime, timeConvert } from '../utils/fnPredefine4Date'
import { Button } from '@hose/eui'
import { OutlinedTipsAdd } from '@hose/eui-icons'
const TripsExtendsField = api.require('@bills/elements/TripsExtendsField')

const formatMap = {
  Date: (v, f = {}) => {
    return timeConvert(f.withTime, f.dateTimeType, v)
  },
  DatePeriod: (v, f = {}) => {
    const format = formatDateTime(f.withTime, f.dateTimeType)
    return i18n.get(`{__k0} 至 {__k1}`, { __k0: moment(v.start).format(format), __k1: moment(v.end).format(format) })
  },
  City: v => {
    if (v && JSON.parse(v)[0]) {
      return JSON.parse(v)[0].label
    } else {
      return i18n.get('空')
    }
  },
  Cities: (f, t) => {
    const from = f && JSON.parse(f)[0]
    const to = t && JSON.parse(t)[0]
    return i18n.get(`{__k0} —— {__k1}`, {
      __k0: from ? from.label : i18n.get('空'),
      __k1: to ? to.label : i18n.get('空')
    })
  }
}
@EnhanceField({
  descriptor: { type: 'trips' },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) return callback()
    let error = fnCheckCompleted({ value, isTrip: true })
    if (error) return callback(error)
    return callback()
  }
})
@EnhanceConnect(state => ({
  powersList: state['@common'].powers.powersList || [],
  tripTypeFullList: state['@common'].fullTripTypes
}))
export default class Trips extends PureComponent {
  constructor(props) {
    super(props)
    this.shouldUpdate = true
    this.hosePower = false
    this.bus = props.bus
    this.tripList = []
    this.intents = []
    this.token = ''
  }

  componentDidMount() {
    this.hosePower = this.getTmcStatus()
    this.handleCheckDetails(this.props.billSpecification)
    this.bus.on('check:details:validator', this.handleCheckDetails)
  }

  componentWillUnmount() {
    this.bus.un('check:details:validator', this.handleCheckDetails)
  }
  handleCheckDetails = billSpecification => {
    if (!billSpecification) return
    let { value = [] } = this.props
    let { name, configs = [] } = billSpecification
    let config = configs.find(v => v.ability === 'requisition')
    let tripType = {}
    if (config) {
      tripType = config.tripType
    }
    let visibleIds = tripType.ids || []

    value.forEach(v => {
      v.errorMsg = v.errorMsg || {}
      let id = typeof v.tripTypeId === 'object' ? v.tripTypeId.id : v.tripTypeId
      if (!tripType.isAll && !~visibleIds.indexOf(id)) {
        v.errorMsg = i18n.get(`该类型的行程不能使用在{__k0}里`, { __k0: name })
      } else {
        v.errorMsg = ''
      }
    })
  }


  handleAddTrips = (canEdit, currentTrips) => {
    const { onChange, bus, value, external } = this.props
    bus.invoke('element:trips:addTrip', { value, external, shouldUpdate: this.shouldUpdate, canEdit, currentTrips }).then(data => {
      bus.emit('trip:change', data)
      this.shouldUpdate = false
      onChange && onChange(data)
    })
  }
  getTripType = id => {
    if (id?.endsWith(':train')) {
      return 'TRAIN'
    } else if (id?.endsWith(':hotel')) {
      return 'HOTEL'
    } else if (id?.endsWith(':flight')) {
      return 'FLIGHT'
    }
  }
  getIntent = () => {
    const sources = [
      'BUTTON_HOTEL_MALL_PRICE_IN_TIME',
      'BUTTON_FLIGHT_MALL_PRICE_IN_TIME',
      'BUTTON_TRAIN_MALL_PRICE_IN_TIME'
    ]
    return Promise.all([
      api.invokeService('@custom-triptype:get:all:travel:intent', { sources }),
      api.invokeService('@custom-triptype:get:travel:intent:jwt', { type: this.TK })
    ]).then(result => {
      if (result[0]?.items) {
        let items = (result[0].items || []).map(i => {
          return i
        })
        let token = result[1] ? result[1].id : ''
        this.token = token
        this.intents = items
      }
    })
  }
  handleOpen = async (e, trip, index) => {
    e && e.stopPropagation && e.stopPropagation()
    e && e.preventDefault && e.preventDefault()
    const {
      tripForm: { tripDate, tripToCity, tripFromCity, tripDatePeriod, tripCity },
      tripTypeId
    } = trip
    const type = this.getTripType(tripTypeId?.id)
    this.sensorTrack(type)
    this.TK = `BUTTON_${type}_MALL_PRICE_IN_TIME`
    if (!this.token) {
      await this.getIntent()
    }
    let cityData
    let data = cloneDeep(trip)
    if (type === 'HOTEL') {
      data.startTime = tripDatePeriod.start
      data.endTime = tripDatePeriod.end
      cityData = await this.checkCanOffer(type, tripCity, tripCity)
    } else {
      data.startTime = tripDate
      data.endTime = tripDate
      cityData = await this.checkCanOffer(type, tripFromCity, tripToCity)
    }
    if (!cityData) {
      return
    }
    data.isPc = true
    data.tripType = type
    data.startCity = cityData.fromCityData
    data.endCity = cityData.toCityData
    const { token, intents } = this
    const items = cloneDeep(intents)
    items.forEach(i => {
      i.data = data
      let type = /\?/.test(i.source) ? '&' : '?'
      if (!i.source.includes('token')) {
        i.source = i.source + `${type}token=${token}`
      }
    })
    api.thirdResources.deleteByType(this.TK)
    api.thirdResources.add(items)
    api.request({ type: this.TK }).then(res => {
      this.handleChange(this.tripList, index, res)
    })
  }
  fnGetMallSensorParams = () => {
    const staff = api.getState()['@common'].userinfo?.staff
    const isTraveler = api.getState()['@common'].mallRole?.mallRole === '0'
    const param = {
      category: isTraveler ? i18n.get('散客') : i18n.get('企业'),
      staffId: staff?.userId,
      staffName: staff?.name,
      corName: staff?.corporation?.name,
      corpId: staff?.corporation?.id
    }
    return param
  }
  fnMallSensorTrack = trackName => {
    window.TRACK && window.TRACK(trackName, this.fnGetMallSensorParams())
  }
  sensorTrack = async type => {
    switch (type) {
      case 'FLIGHT':
        this.fnMallSensorTrack('pcTravel_planeApplication_price_click')
        return
      case 'TRAIN':
        this.fnMallSensorTrack('pcTravel_trainApplication_price_click')
        return
      case 'HOTEL':
        this.fnMallSensorTrack('pcTravel_hotelApplication_price_click')
        return
      default:
        return
    }
  }
  exposureSensorTrack = async type => {
    switch (type) {
      case 'FLIGHT':
        this.fnMallSensorTrack('pcTravel_planeApplication_price_view')
        return
      case 'TRAIN':
        this.fnMallSensorTrack('pcTravel_trainApplication_price_view')
        return
      case 'HOTEL':
        this.fnMallSensorTrack('pcTravel_hotelApplication_price_view')
        return
      default:
        return
    }
  }
  handleChange(tripList, index, result) {
    const money = standardValueMoney(new MoneyMath(result.money).value)
    tripList[index].tripForm.tripMoney = money
    let { onChange } = this.props
    onChange && onChange(tripList)
  }
  checkCanOffer = async (type, fromCity, toCity) => {
    if ((type === 'FLIGHT' || type === 'TRAIN') && (!fromCity || !toCity)) {
      showMessage.error(i18n.get('请选择城市'))
      return null
    }
    if (type === 'HOTEL' && !fromCity) {
      showMessage.error(i18n.get('请选择城市'))
      return null
    }
    let fromCityData
    let toCityData
    if (fromCity) {
      fromCityData = await this.getCity(JSON.parse(fromCity), type)
    }
    if (toCity) {
      toCityData = await this.getCity(JSON.parse(toCity), type)
    }
    return { fromCityData, toCityData }
  }
  getCity = async (city, type) => {
    if (city.length === 1 && city[0].type === 'cityGroup') {
      const { items = [] } = await api.invokeService('@custom-triptype:get:getCityGroupId', {
        id: city[0].key,
        type
      })
      let citys = uniqBy(items, 'code')
      let cityName = city[0].label
      return {
        cityName,
        citys
      }
    } else {
      const { items = [] } = await api.invokeService('@custom-triptype:get:getCityParentId', {
        ids: city.map(i => i.key),
        type
      })
      let citys = []
      if (type !== 'HOTEL' && type !== 'TRAIN') {
        citys = uniqBy(items, 'code').filter(i => i.isRuleNode)
      } else {
        citys = uniqBy(items, 'code')
      }
      return {
        citys
      }
    }
  }

  getTmcStatus = () => {
    let { powersList = [] } = this.props
    let tmcStatus = powersList.find(i => i.powerCode == POWER_CODE)
    return get(tmcStatus, 'state') === 'using'
  }
  renderTripList(trips) {
    if (!trips || !trips.length) {
      return null
    }
    const { tag = {}, flowId, external, bus, isForbid } = this.props
    const isReadOnly = tag.readOnly
    this.tripList = trips
    const tripList = trips.map((t, index) => {
      const { specificationId, tripTypeId, tripForm } = t
      const newSpec = cloneDeep(specificationId)
      const { components = [] } = newSpec
      const defaultList = ['tripDate', 'tripDatePeriod', 'tripCity', 'tripFromCity', 'tripToCity']
      const otherComponents = components.filter(c => !~defaultList.indexOf(c.field) && !c.hide)
      newSpec.components = otherComponents
      const { tripDate, tripDatePeriod, tripCity, tripFromCity, tripToCity, ...others } = tripForm
      if (!tripTypeId.label) {
        tripTypeId.label = tripTypeId.active ? tripTypeId.name : tripTypeId.name + i18n.get('（已停用）')
      }
      const dateStr = tripDate
        ? formatMap['Date'](
            tripDate,
            components.find(c => c.field === 'tripDate')
          )
        : formatMap['DatePeriod'](
            tripDatePeriod,
            components.find(c => c.field === 'tripDatePeriod')
          )
      const cityStr = tripCity
        ? formatMap['City'](tripCity)
        : tripFromCity
        ? formatMap['Cities'](tripFromCity, tripToCity)
        : ''
      let dateTitle, city, dateString

      if (tripDate) {
        dateTitle = moment(tripDate).format('YYYY-MM-DD')
        dateString = `${moment(tripDate).format('MM月DD日')} ${getWeek(tripDate)}`
      } else {
        dateTitle = `${moment(tripDatePeriod.start).format('YYYY-MM-DD')} ${i18n.get('至')} ${moment(tripDatePeriod.end).format('YYYY-MM-DD')}`
        const days = getDays(tripDatePeriod.start, tripDatePeriod.end)
        dateString = i18n.get(`{__k0}~{__k1} 共{__k2}天{__k3}晚`, {
          __k0: moment(tripDatePeriod.start).format(i18n.get('MM月DD日')),
          __k1: moment(tripDatePeriod.end).format(i18n.get('MM月DD日')),
          __k2: days + 1,
          __k3: days
        })
      }
      if (tripTypeId?.type === 'HOTEL') {
        const tripCity = tripForm.tripCity && JSON.parse(tripForm.tripCity)
        city = tripCity ? (
          tripCity[0].label
        ) : (
          <>{i18n.get('住宿地')}</>
        )
      } else {
        const tripFromCity = tripForm.tripFromCity && JSON.parse(tripForm.tripFromCity)
        const tripToCity = tripForm.tripToCity && JSON.parse(tripForm.tripToCity)
        city = (
          <>
            {tripFromCity ? tripFromCity[0].label : i18n.get('出发地')}
            {i18n.get(' - ')}
            {tripToCity ? tripToCity[0].label : i18n.get('目的地')}
          </>
        )
      }
      const showButton = OPENLIST.includes(this.getTripType(t.tripTypeId?.id))
      const money = t?.tripForm?.tripMoney?.standard
      const type = this.getTripType(t?.tripTypeId?.id)
      const itemExternal = external && external[tripForm.tripId]
      if (this.hosePower) {
        this.exposureSensorTrack(type)
      }
      return (
        <div className='trips-list-wrapper' key={index}>
          <h5 style={{margin: '14px 0'}}>{`${i18n.get('第{__k0}程', { __k0: index + 1 })}:${dateStr}`}</h5>
          <div className='trips-item'>
            <TripItem
              tripType={tripTypeId}
              cityStr={city}
              readonly={this.hosePower}
              showButton={showButton}
              dateString={dateString}
              editable={false}
              money={money}
              type={type}
              onOpen={(e) => this.handleOpen(e, t, index)}
              // onShowDetail={() => this.handleAddTrips(false, t)}
              customField={ <TripsExtendsField
                labelNoWrap={true}
                isReadOnly={isReadOnly}
                specification={newSpec}
                value={others}
                external={itemExternal}
                bus={bus}
                shouldUpdate={this.shouldUpdate}
                isForbid={isForbid}
                flowId={flowId}
                tripTypeId={tripTypeId}
              />}
            />
          </div>
        </div>
      )
    })

    return <div className='trip-list'>{tripList}</div>
  }

  render() {
    const { value = [], tag = {} } = this.props
    const isReadOnly = tag.readOnly
    const hasTrip = value && value.length
    const btnStr = hasTrip ? i18n.get('编辑行程') : i18n.get('添加行程')
    let disabled = isDisable(this.props)
    let style = disabled ? { pointerEvents: 'none', opacity: 0.4 } : null
    return (
      <div className={styles.trips} style={style}>
        <h3>{i18n.get('差旅行程')}</h3>
        {!isReadOnly && (
          <Button icon={<OutlinedTipsAdd />} data-testid="field-trips-add" category='secondary' size='small' onClick={() => debounce(() => this.handleAddTrips(true), 20, false)()}>
            {btnStr}
          </Button>
        )}
        {this.renderTripList(value)}
      </div>
    )
  }
}
