/**************************************
 * Created By LinK On 2021/11/11 10:31.
 **************************************/
import React from 'react'
import styles from './DataLinkUrlText.module.less'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
import { app as api } from '@ekuaibao/whispered'

const DataLinkCode = (sourceId, code) => {
  const handleClick = async () => {

    if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
      api.open('@bills:BillInfoDrawerV2', {
        currentId: sourceId,
        flows: [],
        showUpDown: false,
        scene: 'OWNER',
        forceMode: 'readonly',
        billDetailsProps: {
          fromDataLink: true,
        }
      })
      return
    }

    const resp = await api.invokeService('@bills:get:flow-info',
      { id: sourceId, checkPermissions: false })
    const flow = resp.value
    const title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    const params = {
      fromDataLink: true,
      title,
      invokeService: '@bills:get:flow-info',
      params: { id: flow.id },
      backlog: { id: -1, flowId: flow },
      reload: () => {
      }
    }
    // @ts-ignore
    api.open('@bills:BillInfoPopup', params)
  }
  return (<span className={styles['dataLinkUrlText-wrap']} onClick={handleClick}>
          {code}
        </span>)
}

export default DataLinkCode