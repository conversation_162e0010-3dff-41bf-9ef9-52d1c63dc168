import React from 'react'
import { app } from '@ekuaibao/whispered'
import { parseAIResultValue, extractIdsFromArray } from '../../../../../components/dynamic/parseAIResult'
import { Modal, message } from '@hose/eui'
import { isFunction } from '@ekuaibao/helpers'
import { TwoToneGeneralAutofill, FilledTipsWarning } from '@hose/eui-icons'
import { uuid } from '@ekuaibao/helpers'
import { fetchAttachment } from '../../../../../components/utils/FetchAttachment'
import { fetchInvoice } from '../../../../../lib/attachment-fetch'
import { parseAsMeta } from '../../../util/parse'

const isDetailsField = (type) => {
  return ['details', "requisitionDetails"].includes(type)
}

// TypeScript 类型定义
interface AIFilledFields {
  [fieldName: string]: any
}

interface ComponentConfig {
  label: string
  field: string
  type: string
  subTypeId?: string
  behaviour?: string
  showTemplateId?: string
  importMode?: string
  referenceData?: {
    id?: string
    fields: Array<{
      label: string
      name?: string
      type: string
      formula?: boolean
    }>
  }
}

interface AIResult {
  [key: string]: any
}

interface FormBus {
  getFieldsValue: () => Promise<Record<string, any>>
  setFieldsValue: (values: Record<string, any>) => void
  setValidateLevel: (level: number) => void
  getValueWithValidate?: (fields: string[]) => Promise<any>
}

interface HandleApplyAIResultParams {
  bus?: FormBus
  aiFilledFields?: AIFilledFields
  setAiFilledFields?: (fields: AIFilledFields) => void
  result?: AIResult
  components?: ComponentConfig[]
  noApply?: boolean
}

// 统一的字段过滤条件
export const isValidField = (item) =>
  item.hide === false &&
  item.editable === true &&
  item.defaultValue?.type !== 'formula'
// TODO：是否全量字段

export const isValidFieldDataLink = item =>
  item.dataType?.type === 'list'
  && item.type === 'dataLinkEdits'
  && ["INSERT", "MORE"].includes(item.behaviour)  // 支持添加业务对象
  && item.hide === false  // 隐藏的
  && item.editable === true  // 系统计算的
  && item.defaultValue?.type !== 'formula'   // 自动计算的
// TODO：是否全量字段

// 构建字段映射函数
export const buildFieldInfo = (item, type, isArray = false) => ({
  label: item.label,
  type,
  isArray
})

export const buileFieldFilter = item => {
  // 基础字段类型
  if (['text', 'date', 'dateRange', 'number', 'money'].includes(item.dataType?.type)) {
    return buildFieldInfo(item, item.dataType?.type)
  }

  // 自定义档案字段 (单选)
  if (item.type?.startsWith('ref:basedata.Dimension')) {
    return buildFieldInfo(item, item.type.replace(/^ref:/, ''), false)
  }

  // 自定义档案字段 (多选)
  if (item.type?.startsWith('list:ref:basedata.Dimension')) {
    return buildFieldInfo(item, item.type.replace(/^list:ref:/, '').replace(/:select$/, ''), true)
  }

  // 员工字段 (单选)
  if (item.type === 'ref:organization.Staff') {
    return buildFieldInfo(item, 'organization.Staff', false)
  }

  // 员工字段 (多选)
  if (item.type === 'list:ref:organization.Staff') {
    return buildFieldInfo(item, 'organization.Staff', true)
  }

  // 枚举字段
  if (item.type?.startsWith('ref:basedata.Enum')) {
    return buildFieldInfo(item, item.type.replace('ref:', ''), false)
  }

  // 部门字段
  if (item.type === 'ref:organization.Department') {
    return buildFieldInfo(item, 'organization.Department', false)
  }

  // 城市字段
  if (item.type === 'city') {
    return buildFieldInfo(item, 'basedata.city', item.field?.multiple || false)
  }

  // 收款信息字段
  if (item.type === 'payeeInfo') {
    return buildFieldInfo(item, 'payeeInfo', false)
  }

  if (isDetailsField(item.type)) {
    return buildFieldInfo(item, item.type, true)
  }

  return null
}


export const buileFieldFilterDataLink = item => {
  // 基础字段类型
  if (['text', 'date', 'dateRange', 'number', 'money'].includes(item.type)) {
    return buildFieldInfo(item, item.type)
  }

  // 自定义档案字段 (单选)
  if (item.type === 'ref' && item.entity?.startsWith('basedata.Dimension')) {
    return buildFieldInfo(item, item.entity, false)
  }

  // // 自定义档案字段 (多选)
  // if (item.type === 'ref' && item.entity?.startsWith('basedata.Dimension')) {
  //   return buildFieldInfo(item, item.entity, true)
  // }

  // 员工字段 (单选)
  if (item.type === 'ref' && item.entity === 'organization.Staff') {
    return buildFieldInfo(item, 'organization.Staff', false)
  }

  // 员工字段 (多选)
  if (item.type === 'list' && item?.elemType?.type === 'ref' && item?.elemType?.entity === 'organization.Staff') {
    return buildFieldInfo(item, 'organization.Staff', true)
  }

  // 枚举字段
  if (item.type === 'ref' && item.entity?.startsWith('basedata.Enum')) {
    return buildFieldInfo(item, item.entity, false)
  }

  // 部门字段
  if (item.type === 'ref' && item.entity === 'organization.Department') {
    return buildFieldInfo(item, 'organization.Department', false)
  }

  // 城市字段
  if (item.type === 'ref' && item.entity === 'basedata.city') {
    return buildFieldInfo(item, 'basedata.city', true)  // TODO: 没有多选的概念
  }

  // 业务对象
  if (item.type === 'ref' && item.entity?.startsWith('datalink.DataLinkEntity')) {
    return buildFieldInfo(item, item.entity, false)
  }

  // 收款信息字段
  if (item.type === 'ref' && item.entity?.startsWith('pay.PayeeInfo')) {
    return buildFieldInfo(item, item.entity, false)
  }

  return null
}


// 添加AI样式表
export const addAiStyles = () => {
  if (!document.getElementById('ai-styles')) {
    const styleEl = document.createElement('style');
    styleEl.id = 'ai-styles';
    styleEl.textContent = `
      .ai-filled-field input, 
      .ai-filled-field .ant-select-selector,
      .ai-filled-field .ant-picker,
      .ai-filled-field textarea {
        border: 1px solid var(--eui-primary-pri-500) !important;
        transition: all 0.3s;
      }
      
      .ai-icon {
        display: inline-flex;
        margin-left: 4px;
        vertical-align: middle;
        color: var(--eui-primary-pri-500);
        position: relative;
        top: -1px;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      
      .ai-icon svg {
        animation: fadeIn 0.5s;
      }
    `;
    document.head.appendChild(styleEl);
  }
}

// 渲染React组件到DOM元素
export const renderIconToElement = (element: HTMLElement): void => {
  try {
    // 使用React DOM渲染TwoToneGeneralAiSummary组件
    const ReactDOM = require('react-dom');
    ReactDOM.render(
      <div style={{
        display: 'flex',
        height: '20px',
        padding: '1px 4px',
        alignItems: 'center',
        gap: '4px',
        borderRadius: '4px',
        background: 'linear-gradient(270deg, #D7CAFF -12.77%, #D7CAFF -12.75%, #E2FCFF 100%), linear-gradient(90deg, #FF8A00 0%, #FF3D00 100%)',
        color: '#4E77FE',
        font: 'var(--eui-font-note-b2)',
      }}>
        <TwoToneGeneralAutofill fontSize={16} />
        <span>AI填写</span>
      </div>,
      element
    );
  } catch (error) {
    // 如果无法使用React DOM渲染，回退到简单的HTML，使用相同的样式
    element.innerHTML = `<div style="
      display: flex;
      height: 20px;
      padding: 1px 4px;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      background: linear-gradient(270deg, #D7CAFF -12.77%, #D7CAFF -12.75%, #E2FCFF 100%), linear-gradient(90deg, #FF8A00 0%, #FF3D00 100%);
      color: #4E77FE;
      font: var(--eui-font-note-b2);
    ">
      <svg id="outlined-general/signature" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" width="16" height="16" focusable="false" aria-hidden="true" style="max-width: 100%; max-height: 100%;"><path d="M8.57155 31.7219C8.67172 31.0677 8.93264 30.4487 9.33091 29.9202L27.1302 6.29967C28.4597 4.53537 30.9677 4.18289 32.732 5.51239L34.7651 7.04441C36.5294 8.37391 36.8819 10.8819 35.5524 12.6462L17.7531 36.2667C17.3548 36.7952 16.8316 37.2167 16.2304 37.4933L10.4155 40.1687C8.96831 40.8345 7.36146 39.6236 7.60261 38.049L8.57155 31.7219Z" stroke="url(#paint0_linear_35_53505)" stroke-width="4" stroke-linecap="round" stroke-dasharray="0 0 0 95.33"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,95.3301773071289;            0,47.66508865356445,47.66508865356445,0;            95.3301773071289,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></path><path d="M35.1234 36.5242L31.4038 35.0762C31.2844 35.0295 31.1823 34.9496 31.1103 34.8467C31.0384 34.7439 31 34.6228 31 34.4988C31 34.3749 31.0384 34.2538 31.1103 34.1509C31.1823 34.0481 31.2844 33.9682 31.4038 33.9215L35.1234 32.4724C35.471 32.3373 35.7414 32.0628 35.8623 31.7196L37.3872 27.4209C37.431 27.2981 37.5136 27.1915 37.6235 27.116C37.7334 27.0406 37.865 27 38 27C38.135 27 38.2666 27.0406 38.3765 27.116C38.4864 27.1915 38.569 27.2981 38.6128 27.4209L40.1366 31.7207C40.2586 32.0628 40.5279 32.3373 40.8766 32.4735L44.5962 33.9215C44.7156 33.9682 44.8177 34.0481 44.8897 34.1509C44.9616 34.2538 45 34.3749 45 34.4988C45 34.6228 44.9616 34.7439 44.8897 34.8467C44.8177 34.9496 44.7156 35.0295 44.5962 35.0762L40.8766 36.5242C40.5279 36.6604 40.2586 36.9338 40.1366 37.277L38.614 41.5768C38.5707 41.7001 38.4883 41.8073 38.3784 41.8832C38.2684 41.9592 38.1365 42 38.0012 42C37.8658 42 37.7339 41.9592 37.6239 41.8832C37.514 41.8073 37.4316 41.7001 37.3883 41.5768L35.8634 37.277C35.8036 37.1079 35.7066 36.9531 35.5792 36.8232C35.4517 36.6933 35.2966 36.5914 35.1246 36.5242H35.1234Z" stroke="url(#paint1_linear_35_53505)" stroke-width="3" stroke-linecap="round" stroke-dasharray="0 0 0 44.734"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,44.734230041503906;            0,22.367115020751953,22.367115020751953,0;            44.734230041503906,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></path><path d="M5.52955 16.0798L4.52494 15.7089C4.36991 15.6521 4.23709 15.5544 4.14354 15.4283C4.04999 15.3022 4 15.1535 4 15.0013C4 14.8491 4.04999 14.7004 4.14354 14.5743C4.23709 14.4482 4.36991 14.3505 4.52494 14.2937L5.52955 13.9245C6.11157 13.7094 6.56104 13.2755 6.76465 12.7349L7.22182 11.5103C7.27633 11.3617 7.38079 11.2324 7.5204 11.1409C7.66001 11.0493 7.82771 11 7.99976 11C8.17182 11 8.33952 11.0493 8.47913 11.1409C8.61874 11.2324 8.72319 11.3617 8.77771 11.5103L9.23487 12.7349C9.43656 13.2755 9.88796 13.7094 10.47 13.9245L11.4727 14.2937C11.6283 14.3502 11.7617 14.4479 11.8557 14.5742C11.9498 14.7005 12 14.8496 12 15.0022C12 15.1548 11.9498 15.3038 11.8557 15.4302C11.7617 15.5565 11.6283 15.6542 11.4727 15.7107L10.47 16.0781C9.88796 16.2933 9.43656 16.7289 9.23487 17.2677L8.77579 18.4923C8.72084 18.6403 8.61632 18.7689 8.47694 18.8599C8.33756 18.951 8.17034 19 7.9988 19C7.82726 19 7.66004 18.951 7.52066 18.8599C7.38128 18.7689 7.27677 18.6403 7.22182 18.4923L6.76465 17.2677C6.66459 17.0003 6.50249 16.7556 6.28937 16.5504C6.07625 16.3451 5.8171 16.184 5.52955 16.0781V16.0798Z" fill="url(#paint2_linear_35_53505)" stroke-dasharray="0 0 0 24.296"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,24.29570960998535;            0,12.147854804992676,12.147854804992676,0;            24.29570960998535,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></path><circle cx="25" cy="41" r="2" fill="url(#paint3_linear_35_53505)" stroke-dasharray="0 0 0 11.314"><animate attributeType="XML" attributeName="stroke-dasharray" repeatCount="1" dur="1s" values="0,0,0,11.313708305358887;            0,5.656854152679443,5.656854152679443,0;            11.313708305358887,0,0,0" keyTimes="0; 0.5; 1" fill="freeze"></animate></circle><defs><linearGradient id="paint0_linear_35_53505" x1="5.80318" y1="41.9975" x2="42.8162" y2="18.7391" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient><linearGradient id="paint1_linear_35_53505" x1="30.6957" y1="33.331" x2="45.3479" y2="34.569" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient><linearGradient id="paint2_linear_35_53505" x1="3.82609" y1="14.3765" x2="12.19" y2="15.1337" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient><linearGradient id="paint3_linear_35_53505" x1="22.913" y1="40.6883" x2="27.095" y2="41.0668" gradientUnits="userSpaceOnUse"><stop stop-color="#9E80FF"></stop><stop offset="1" stop-color="#48DCDC"></stop></linearGradient></defs></svg>
      <span>AI填写</span>
    </div>`;
  }
}

// 为字段添加AI图标和高亮
export const addAiIconAndHighlight = (fieldName, feeDetailId?: string) => {
  try {
    const domkey = feeDetailId ? `#FeeDetailView #${fieldName}` : `#${fieldName}`
    const iconContainerId = feeDetailId ? `FeeDetailView-${fieldName}-ai-icon` : `${fieldName}-ai-icon`
    // 查找字段元素
    const fieldElement = document.querySelector(domkey);
    if (!fieldElement) return;

    // 查找标签元素
    const labelElement = fieldElement.querySelector('.ant-form-item-label label');
    const dataLinkFieldElement = fieldElement.querySelector('.risk-warning-label')
    if (!labelElement && !dataLinkFieldElement) return;

    // 检查是否已存在AI图标
    if (!document.getElementById(iconContainerId)) {
      // 创建AI图标元素
      const iconContainer = document.createElement('span');
      iconContainer.id = iconContainerId;
      iconContainer.className = 'ai-icon';

      // 渲染React组件到DOM元素
      renderIconToElement(iconContainer);

      // 添加到标签后面
      labelElement && labelElement.appendChild(iconContainer);
      dataLinkFieldElement && dataLinkFieldElement.appendChild(iconContainer);
    }
  } catch (error) {
    console.error('添加AI图标失败:', error);
  }
}

export const parseDataLinkForm = async (
  result: any[],
  typeMap: Record<string, string> = {},
  template: ComponentConfig
): Promise<any[]> => {
  const { fields, id } = template.referenceData
  const { subTypeId, behaviour, showTemplateId, importMode } = template
  console.log('parseDataLinkForm result', result, typeMap, fields)
  const res = await app.invokeService('@bills:get:getDataLinkEditTemplate', {
    id,
    type: ["INSERT", "MORE"].includes(behaviour),
    tableTemplateId: showTemplateId
  })
  const items = subTypeId ? res.items.filter(i => i?.entity?.id === subTypeId) : res.items
  if (!Array.isArray(result)) {
    if (typeof result === 'object' && result !== null) {
      result = [result]
    } else {
      return []
    }
  }
  const valueArray = result.map(item => {
    const valueItem = {
      dataLinkForm: {},
      dataLinkId: null,
      dataLinkTemplateId: items[0]?.templateId
    }
    const fieldsMapArray = Object.keys(item).map(key => {
      const field = fields.find(item => item.label === key)
      if (field && field.name) {
        const val = item[key]
        const type = typeMap[key]
        if (!val) return null
        if (type === 'dateRange' && (!val?.start && !val?.end)) {
          return null
        }

        return {
          key: field.name,
          value: parseAIResultValue(val, type),
        }
      }
      return null
    }).filter(Boolean)
    fieldsMapArray.forEach(item => {
      valueItem.dataLinkForm[item.key] = item.value
    })
    return valueItem
  })
  if (importMode === "SINGLE") {
    return valueArray?.slice(0, 1)
  }
  return valueArray
}

// 处理费用明细数据的函数
export const parseDetailsForm = async (
  detailsData: any[],
  feeTypeMap: any = {}
): Promise<any[]> => {
  console.log('parseDetailsForm detailsData', detailsData, feeTypeMap)

  if (!Array.isArray(detailsData) || detailsData.length === 0) {
    return []
  }
  // 先把发票处理了
  const newDetailsData = await fetchInvoice(detailsData, '', true)
  const processedDetails = await Promise.all(
    newDetailsData.map(async (item, index) => {
      try {
        // 获取费用类型信息
        let feeType = { id: item.feeTypeId }
        if (feeTypeMap?.[item.feeTypeId]) {
          feeType = feeTypeMap?.[item.feeTypeId]
        }

        // 获取规范信息
        let specificationId = item.specificationId
        if (typeof specificationId === 'string') {
          try {
            const spec = await app.invokeService('@bills:get:Specifications', [specificationId])
            specificationId = spec?.items?.[0] || { id: specificationId, components: [] }
          } catch (err) {
            console.warn('获取规范信息失败:', err)
            specificationId = { id: specificationId, components: [] }
          }
        }

        // 处理日期 - 转换为时间戳
        let feeDate = item.feeTypeForm?.feeDate
        if (typeof feeDate === 'string') {
          feeDate = new Date(feeDate).getTime()
        } else if (!feeDate) {
          feeDate = Date.now()
        }

        const attachmentsFromInvoiceForm = item.feeTypeForm?.invoiceForm?.attachments ? await fetchAttachment(item.feeTypeForm?.invoiceForm?.attachments) : undefined

        // 构建完整的明细对象
        const detail = {
          fromAIChat: true,
          idx: index, // 临时索引，实际添加时会重新设置
          feeTypeId: feeType,
          specificationId: specificationId,
          feeTypeForm: {
            ...item.feeTypeForm,
            detailId: uuid(14), // 生成唯一ID
            feeDate: feeDate,
            // 确保金额格式正确
            amount: item.feeTypeForm?.amount ? {
              ...item.feeTypeForm.amount,
              standard: String(item.feeTypeForm.amount.standard || '0.00')
            } : {
              standard: '0.00',
              standardUnit: '元',
              standardScale: 2,
              standardSymbol: '¥',
              standardNumCode: '156',
              standardStrCode: 'CNY'
            }
          },
          errorMsg: {},
        }

        if (attachmentsFromInvoiceForm) {
          detail.feeTypeForm.invoiceForm.attachments = attachmentsFromInvoiceForm
        }

        return detail
      } catch (error) {
        console.error('处理费用明细失败:', error, item)
        return null
      }
    })
  )

  return processedDetails.filter(Boolean)
}

// 添加费用明细到表单的函数
export const addDetailsToForm = async (
  bus: FormBus,
  detailsData: any[],
  feeTypeMap: any = {}
): Promise<boolean> => {
  try {
    // 获取当前表单值
    const currentFormValues = await bus.getFieldsValue()
    const currentDetails = currentFormValues.details || []

    // 处理新的费用明细数据
    const newDetails = await parseDetailsForm(detailsData, feeTypeMap)

    if (newDetails.length === 0) {
      console.warn('没有有效的费用明细数据')
      return false
    }

    // 重新设置索引
    const allDetails = [...currentDetails, ...newDetails.map((detail, index) => ({
      ...detail,
      idx: currentDetails.length + index
    }))]

    // 更新表单
    bus.setFieldsValue({
      details: allDetails
    })

    // // 触发details变更事件
    // if (window.billBus) {
    //   window.billBus.emit('details:change', allDetails)
    // }

    console.log('费用明细添加成功:', allDetails)
    return true
  } catch (error) {
    console.error('添加费用明细失败:', error)
    return false
  }
}



export const handleApplyAIResult = async ({
  bus,
  aiFilledFields = {},
  setAiFilledFields,
  result = {},
  components = [],
  noApply = false
}: HandleApplyAIResultParams): Promise<AIFilledFields> => {
  const fields = components
    .filter(isValidField)
    .map(buileFieldFilter)
    .filter(Boolean)

  let datalinkFieldMap = {}
  components
    .filter(isValidFieldDataLink)
    .forEach(item => {
      if (item.referenceData && typeof item.referenceData === 'object') {
        datalinkFieldMap[item.label.trim()] = item.referenceData.fields
          .filter(item => !item.formula)
          .map(buileFieldFilterDataLink)
          .filter(Boolean)
      }
    })

  const dataLinkTypeMap = {}
  Object.keys(datalinkFieldMap).forEach(key => {
    dataLinkTypeMap[key] = 'list:dataLinkEdits'
  })

  const typeMap = fields.reduce((acc, item) => {
    acc[item.label] = item.type
    return acc
  }, dataLinkTypeMap)


  const arrayMapForLabel = fields?.reduce?.((acc, item) => {
    acc[item.label] = item.isArray
    return acc
  }, {})


  const fieldsValue = bus ? (await bus.getFieldsValue()) : {}
  // 首先收集所有需要处理的字段
  const fieldsToProcess = Object.keys(result).map(key => {
    const component = components.filter(isValidField).find(item => item.label.trim() === key)
    if (!component || !component.field) return null

    const fieldValue = fieldsValue?.[component.field]

    const type = typeMap?.[key]
    let noValue = !fieldValue

    if (type === 'list:dataLinkEdits' && Array.isArray(fieldValue)) {
      noValue = !(fieldValue?.filter(item => {
        const formValues = Object.values(item?.dataLinkForm || {});
        // 检查是否所有值都为空
        return formValues.some(value => {
          // 检查值是否为空
          if (value === null || value === undefined) return false;
          if (typeof value === 'boolean') return value; // 布尔值为true时不为空
          if (Array.isArray(value)) return value.length > 0; // 数组不为空
          if (typeof value === 'object') return Object.keys(value).length > 0; // 对象不为空
          return value !== ''; // 字符串不为空
        });
      })?.length > 0);
    }

    if (type === 'organization.Staff' && arrayMapForLabel?.[key]) {
      noValue = !fieldValue?.length
    }

    if (type?.startsWith('basedata.Dimension') && arrayMapForLabel?.[key]) {
      noValue = !fieldValue?.length
    }

    if (type === 'payeeInfo') {
      noValue = !fieldValue?.id
    }

    if (isDetailsField(type)) {
      noValue = true
    }


    // 只处理未填写的字段
    if (!noValue) return null

    let value = result[key]
    if (!value || value?.length === 0) return null
    if (type === 'dateRange' && (!value?.start && !value?.end)) {
      return null
    }

    if (type === 'list:dataLinkEdits' && Array.isArray(value)) {
      value = value.filter(item => Object.values(item).filter((valueItem => {
        // 检查值是否为空
        if (valueItem === null || valueItem === undefined) return false;
        if (typeof valueItem === 'boolean') return valueItem; // 布尔值为true时不为空
        if (Array.isArray(valueItem)) return valueItem.length > 0; // 数组不为空
        if (typeof valueItem === 'object') return Object.values(valueItem)?.filter(Boolean)?.length > 0; // 对象不为空
        return valueItem !== ''; // 字符串不为空
      })).filter(Boolean).length > 0)
      value = value.map(item => {
        const res = { ...item }
        Object.keys(res).forEach(resKey => {
          const resValue = res[resKey]
          // 处理空数组
          if (Array.isArray(resValue)) {
            const finalVal = resValue.filter(item => !(item && typeof item === 'object' && Object.keys(item).length === 0))?.map(item => item?.name)?.filter(Boolean)
            if (finalVal.length === 0) {
              delete res[resKey]
            }
          }
          // 处理空对象
          if (resValue && typeof resValue === 'object' && !Array.isArray(resValue) && Object.values(resValue).filter(Boolean).length === 0) {
            delete res[resKey]
          }
        })
        return res
      })
      if (value.length === 0) return null
    }

    if (!arrayMapForLabel?.[key] && type === 'basedata.city') {
      if (typeof value === 'string') {
        try {
          const valueJSON = JSON.parse(value)?.filter(item => item.key);
          if (valueJSON.length === 0) return null
          value = JSON.stringify(valueJSON)
        } catch {
          return null;
        }
      }
    }

    // 单选字段
    if (!arrayMapForLabel?.[key] && (
      ['basedata.Dimension', 'organization.Staff', 'organization.Department', 'payeeInfo'].includes(type) ||
      type?.startsWith('basedata.Enum') || type?.startsWith('basedata.Dimension')
    )) {
      value = value?.id
    }

    // 多选字段
    if (arrayMapForLabel?.[key] && (
      type?.startsWith('basedata.Dimension') ||
      ['organization.Staff', 'basedata.Dimension', 'basedata.city'].includes(type)
    )) {
      value = extractIdsFromArray(value)
    }

    if (!value || value?.length === 0) return null

    return {
      field: component.field,
      value,
      type,
      component,
      key
    }
  }).filter(Boolean)

  // 处理业务对象字段
  const processedFields = []
  // 使用for...of循环处理异步操作
  for (const fieldInfo of fieldsToProcess) {
    try {
      let finalValue = fieldInfo.value

      // 如果是业务对象类型，需要异步处理
      if (fieldInfo.type === 'list:dataLinkEdits') {
        const childMap = {}
        // 业务对象字段类型
        datalinkFieldMap[fieldInfo.key]?.forEach(item => childMap[item.label] = item.type)

        console.log(finalValue, result[fieldInfo.key])
        // 异步处理业务对象数据
        finalValue = await parseDataLinkForm(
          finalValue,
          childMap,
          fieldInfo.component
        )
      }

      processedFields.push({
        field: fieldInfo.field,
        value: finalValue,
        type: fieldInfo.type
      })
    } catch (error) {
      console.error(`处理字段 ${fieldInfo.field} 时出错:`, error)
    }
  }

  console.log('handleApplyAIResult processedFields', processedFields)

  if (noApply) {
    const aiResult: Record<string, any> = {}
    for (let item of processedFields) {
      let aiValue
      if (!item.type) {
        continue;
      }
      if (isDetailsField(item.type)) {
        aiValue = item.value
      } else {
        aiValue = parseAIResultValue(item.value, item.type)
      }
      addAiIconAndHighlight(item.field)
      aiResult[item.field] = aiValue;
    }
    return Promise.resolve(aiResult)
  }

  if (processedFields.length === 0) {
    Modal.warning({
      title: i18n.get('请重试'),
      content: i18n.get('当前AI 提取字段已被填写，请删除字段内容后重试'),
      okText: i18n.get('知道了'),
    })
    return aiFilledFields
  }

  // 添加AI样式表
  addAiStyles();

  // 记录已填充的字段
  const newAiFilledFields = { ...aiFilledFields };

  const detailsField = processedFields.find(item => isDetailsField(item.type))

  if (detailsField?.value?.length > 0) {
    // 获取费用类型数据
    const feeTypesData = await app.dataLoader('@common.feetypes').load()
    const feeTypeMap = feeTypesData?.map
    // 获取当前表单值
    const currentFormValues = bus ? (await bus.getFieldsValue()) : {}
    const currentDetails = currentFormValues.details || []
    // 处理新的费用明细数据
    const newDetails = await parseDetailsForm(detailsField.value, feeTypeMap)
    const detailRepeated = newDetails.filter(it => {
      return currentDetails.some(item => item.feeTypeId?.id === it.feeTypeId?.id && item.feeTypeForm?.amount?.standard === it.feeTypeForm?.amount?.standard)
    }).map(it => {
      return {
        name: it.feeTypeId?.name,
        amount: it.feeTypeForm?.amount
      }
    })

    const onOk = async () => {
      try {
        // 添加费用明细
        const success = await addDetailsToForm(bus, detailsField.value, feeTypeMap)
        if (success) {
          console.log('费用明细添加成功')
          // 为费用明细字段添加AI标记
          addAiIconAndHighlight('details')
        }
      } catch (error) {
        console.error('处理费用明细失败:', error)
      }

    }
    if (detailRepeated.length > 0) {
      const detailRepeatedText = detailRepeated.reduce?.((acc, item) => {
        acc += `「${item.name}${item.amount?.standard}${item.amount?.standardUnit}」`
        return acc
      }, '')
      await new Promise((resolve) => {
        Modal.confirm({
          title: '是否创建新的费用明细数据？',
          icon: <FilledTipsWarning />,
          content: `当前单据已有${detailRepeatedText}的费用明细，是否创建新的费用明细数据？`,
          okText: '重新创建',
          cancelText: '取消创建',
          onOk: async () => {
            await onOk()
            resolve(true)
          },
          onCancel() {
            resolve(false)
          }
        });
      })
    } else {
      await onOk()
    }
  }

  // 返回Promise以便调用方可以获取更新后的值
  return new Promise((resolve) => {
    // 使用setTimeout并确保表单已加载
    setTimeout(() => {
      // 逐个设置字段值而不是一次性设置所有
      processedFields.forEach(item => {
        // 跳过,外面已经处理了
        if (isDetailsField(item.type)) {
          return
        }
        try {
          const aiValue = parseAIResultValue(item.value, item.type)

          // 设置字段值
          bus.setFieldsValue({
            [item.field]: aiValue
          });

          // 标记字段为AI填充
          newAiFilledFields[item.field] = aiValue;

          // 添加高亮和图标
          if (aiValue) {
            addAiIconAndHighlight(item.field);
          }
        } catch (error) {
          console.error(`设置字段 ${item.field} 时出错:`, error)
        }
      });

      // 设置报错level，然后只校验AI填单字段
      bus.setValidateLevel(0);
      isFunction(bus?.getValueWithValidate) && bus?.getValueWithValidate(processedFields.map(item => item.field)).then(res => {
        console.log('handleApplyAIResult Validate', res)
      }).catch(err => {
        console.log('handleApplyAIResult err', err)
      })

      message.success(i18n.get('AI填写成功，请查看'))

      // 如果提供了setter函数，则更新状态
      if (setAiFilledFields) {
        setAiFilledFields(newAiFilledFields)
      }

      // 解析Promise，返回更新后的字段信息
      resolve(newAiFilledFields)
    }, 100)
  })
}

// 移除AI标记和高亮
export const removeAiMarkForField = (fieldName: string, feeDetailId?: string) => {
  try {
    // 移除AI图标
    const iconElement = document.querySelector(`#${feeDetailId ? `FeeDetailView-${fieldName}-ai-icon` : `${fieldName}-ai-icon`} `);
    if (iconElement) {
      iconElement.remove();
    }
  } catch (error) {
    console.error('移除AI标记失败:', error);
  }
}

// 独立的添加费用明细函数 - 可以从外部调用
export const addCustomDetailsToForm = async (
  detailsData: any[],
  bus?: FormBus
): Promise<boolean> => {
  try {
    // 如果没有传入bus，尝试从全局获取
    let formBus = bus
    if (!formBus && window.billBus) {
      // 构造一个简单的FormBus接口
      formBus = {
        getFieldsValue: () => window.billBus.getFieldsValue(),
        setFieldsValue: (values) => window.billBus.setFieldsValue(values),
        setValidateLevel: (level) => window.billBus.setValidateLevel?.(level)
      }
    }

    if (!formBus) {
      console.error('无法获取表单总线')
      return false
    }

    // 获取费用类型数据
    const feeTypesData = await app.dataLoader('@common.feetypes').load()
    const feeTypeMap = feeTypesData?.map

    // 添加费用明细
    const success = await addDetailsToForm(formBus, detailsData, feeTypeMap)

    if (success) {
      message.success('费用明细添加成功')
      // 添加AI标记
      addAiIconAndHighlight('details')
      return true
    } else {
      message.error('费用明细添加失败')
      return false
    }
  } catch (error) {
    console.error('添加自定义费用明细失败:', error)
    message.error('添加费用明细失败')
    return false
  }
}

// 简化的添加费用明细函数 - 直接使用你提供的数据格式
export const addDetailsFromAIResult = async (detailsData: any[]): Promise<boolean> => {
  return addCustomDetailsToForm(detailsData)
}



export const formatDataForCreateBills = async (datas: AIResult, specifications: { id: string }[]) => {
  if (!datas || !Array.isArray(datas)) return []
  let finalDatas = []
  const baseDataProperties = app.getState('@common.globalFields.data')
  const userinfo = app.getState()['@common'].userinfo
  const submitterId = userinfo?.staff?.id
  for (let data of datas) {
    const specification = specifications.find(item => item.id === data.specificationId)
    const components = parseAsMeta(specification, baseDataProperties)
    const form = await handleApplyAIResult({
      result: data,
      components,
      noApply: true
    })
    finalDatas.push({
      name: "freeflow.edit",
      form: {
        submitterId,
        specificationId: data.specificationId,
        ...form
      },
      aiFilledFields: form
    })
  }
  return finalDatas
}