import { get } from "lodash";
import { app as api } from "@ekuaibao/whispered";
import { BillActionBase } from "./base-action";
import { Modal } from "@hose/eui";
import { Resource } from "@ekuaibao/fetch";

const backlogs = new Resource('/api/flow/v2/backlogs')

export class WithdrawAction extends BillActionBase {
  label = i18n.get('撤回审批')

  onAction = () => {
    return this.handleApproveWithdraw()
  }

  getRejectNode = () => {
    const { backlog } = this.props
    const nodes = get(backlog, 'flowId.plan.nodes', [])
    const currentId = get(backlog, 'flowId.plan.taskId', '')
    const index = nodes.findIndex((_node: any) => _node.id === currentId)
    const prevNode = index > 0 ? nodes[index - 1] : {}
    return prevNode.id
  }

  handleApproveWithdraw = () => {
    const { backlog } = this.props
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: i18n.get('确认发起撤回吗?'),
        content: '',
        onOk: async () => {
          const nextId = get(backlog, 'flowId.plan.taskId', '')
          const rejectTo = this.getRejectNode()
          const id = get(backlog, 'flowId.plan.id', '')
          const name = 'freeflow.withdraw'
          try {
            const messageCode = await api.invoke('@vendor:dd:message:code')
            const res = await backlogs.POST('/withdraw/$id', { id, name, rejectTo, nextId }, { messageCode })
            if (res.code === '0') {
              Modal.success({
                title: res.message
              })
              resolve(true)
            } else {
              Modal.error({
                title: res.message
              })
              reject(res.message)
            }
          } catch (e) {
            Modal.error(e.message)
            console.error(e)
            reject(e.message)
          }
        }
      })
    })
  }
}