/**************************************************
 * Created by nanyuanting<PERSON> on 11/08/2017 17:41.
 **************************************************/
import moment from 'moment'
import { ENUM_DATE_TYPE } from '../consts'

export default (field = {}) => {
  const { defaultValue, withTime, optionalDefaultValue = false, dateTimeType } = field
  if (optionalDefaultValue) return
  if (!defaultValue) {
    return formatDateTime(withTime, dateTimeType) === 'YYYY-MM-DD HH:mm'
      ? Date.parse(new Date())
      : formatWithTime(withTime, undefined, dateTimeType)
  }
  let { type, value } = defaultValue

  if (type === 'predefine' && value === 'repayment.date') {
    return formatWithTime(withTime, moment().add(1, 'months'), dateTimeType)
  }

  if (type === 'predefine' && !!~['submit.date', 'lastSubmit.date', 'firstSubmit.date'].indexOf(value)) {
    return formatWithTime(withTime, undefined, dateTimeType)
  }
}

export function formatWithTime(withTime, date = moment(), dateTimeType, type) {
  if (moment.isMoment(date)) {
    let dateTime
    if (type === 'settlementChecking') {
      dateTime = formatDateTimeST(withTime, dateTimeType)
    } else {
      dateTime = formatDateTime(withTime, dateTimeType)
    }
    return moment(date.format(dateTime)).valueOf()
  }
  return date
}

export function timeConvert(withTime, dateTimeType, value, noReplace = false) {
  if (!value) {
    return i18n.get('无')
  }
  let dateTime = formatDateTime(withTime, dateTimeType)
  if (!noReplace) {
    dateTime = dateTime.replace(/-/g, '/')
  }
  const inp = Number(value) || value
  return moment.utc(inp).utcOffset(8).format(dateTime)
}

export function formatDateTime(withTime, dateTimeType) {
  if (dateTimeType) {
    switch (dateTimeType) {
      case ENUM_DATE_TYPE.YEAR_MONTH:
        return 'YYYY-MM'
      case ENUM_DATE_TYPE.YEAR_MONTH_DAY:
        return 'YYYY-MM-DD'
      case ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME:
        return 'YYYY-MM-DD HH:mm'
    }
  }
  return withTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD'
}

export function formatDateTimeST(withTime, dateTimeType) {
  if (dateTimeType) {
    switch (dateTimeType) {
      case ENUM_DATE_TYPE.YEAR_MONTH:
        return 'YYYY-MM'
      case ENUM_DATE_TYPE.YEAR_MONTH_DAY:
        return 'YYYY-MM-DD'
      case ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME:
        return 'YYYY-MM-DD HH:mm:ss'
    }
  }
  return withTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
}

export function getShowTime(withTime, dateTimeType) {
  const format = { format: 'HH:mm' }
  if (dateTimeType) {
    return dateTimeType === ENUM_DATE_TYPE.YEAR_MONTH_DAY_TIME && format
  }
  return withTime && format
}

export const formatDateTimeByZone = dateValue => {
  return moment
    .utc(dateValue)
    .utcOffset(8)
    .valueOf()
}
