import React, { PureComponent } from 'react'
import styles from './currency-money.module.less'
import classNames from 'classnames'
import { Input as AntdInput, Col } from 'antd'
import { Input as EUIInputNumber, Tooltip, Button, PageHeader, Space } from '@hose/eui'
import {
  OutlinedTipsInfo,
  OutlinedDirectionRefresh,
  OutlinedEditPaCalculation,
  OutlinedDirectionDown,
  FilledDirectionExpandDown,
  OutlinedTipsMaybe,
} from '@hose/eui-icons'
import MoneyNzh from '../../elements/puppet/MoneyNzh'
import CurrencyDownImage from './images/currency-down.svg'
import CurrencyDropdown from './currency-dropdown'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep, isEqual, get, isEmpty, compact } from 'lodash'
import {
  hasForeign,
  BudgetStrCode,
  hasBudget,
  initValue,
  initDimentionCurrencyValue,
  updateCurrencyValueByDimention,
  updateValueCurrency,
  updateValueMoney,
  updateValueRate,
  getExchangeRate,
  getExchangeRateDifferential
} from '../../components/utils/fnCurrencyObj'
import { DefaultBudgetRateTip, DefaultRateTip, DefaultRecoveryRateTip } from './DefaultRateTip'
import { getBoolVariation } from '../../lib/featbit'
const EKBIcon = api.require('@elements/ekbIcon')
let useEUI
@EnhanceConnect(state => ({
  currencyConfig: state['@common'].currencyConfig || {},
  allowModifiable: state['@currency-manage'].allowModifiable,
  MULTICURRENCYWRITTENOFF: state['@common'].powers.MULTICURRENCYWRITTENOFF,
  CROSSCURRENCYWRITTENOFF: state['@common'].powers.CROSSCURRENCYWRITTENOFF
}))
export default class CurrencyMoney extends PureComponent {
  constructor(props) {
    super(props)
    const { value, dimentionCurrency, standardCurrency, field } = props
    this.expectCurrency = 'foreign'
    this.isReceivingAmount = field?.field === 'receivingAmount'
    this.selectCurrency = dimentionCurrency ? dimentionCurrency?.currency : standardCurrency
    this.initCurrencyConfig()
    this.state = {
      dataSource: initValue(value),
      allCurrencyRates: [],
      canSelectCurrency: false,
      isSelectedAutoRate: false,
      isSelectedAutoBudgetRate: false,
      foreignDisabled: false,
      rateModifiable: false
    }
    // 记录输入状态，正在输入中时不处理金额联动
    this.inputting = false
    this.__CONTINUE_ADD__ = false
  }

  /**
   * 取币种设置中的字段currencyUnchangedWhenSwitch
   * OCCUPY_MONEY: 切换币种时，本位币金额保持不变
   * ORIGINAL_AMOUNT: 切换币种时，原币金额保持不变
   */
  initCurrencyConfig = async () => {
    const currencyConfig = await api.dataLoader('@common.currencyConfig').load()
    const currencyUnchangedWhenSwitch = get(currencyConfig, 'currencyUnchangedWhenSwitch', 'OCCUPY_MONEY')
    if (currencyUnchangedWhenSwitch === 'ORIGINAL_AMOUNT') {
      this.constantForeignMoney = 'foreign'
    }
  }

  componentWillMount() {
    this._initCurrencyList(this.props)
    const { bus } = this.props
    bus && bus.on('continue:add:detail', this.handleContinueAdd)
    bus && bus.on('dimention:currency:change', this.handleDimentionCurrencyChange)
    bus && bus.on('set:default:currency', this.setDefaultCurrency)
    bus && bus.on('first:set:default:currency', this.initFinish)
    bus && bus.on('original:currency', this.handleOriginalCurrency)
    bus && bus.watch('set:history:currency:rate', this.setHistoryCurrencyAndChangeRate)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus && bus.un('continue:add:detail', this.handleContinueAdd)
    bus && bus.un('dimention:currency:change', this.handleDimentionCurrencyChange)
    bus && bus.un('set:default:currency', this.setDefaultCurrency)
    bus && bus.un('first:set:default:currency', this.initFinish)
    bus && bus.un('original:currency', this.handleOriginalCurrency)
    bus && bus.un('set:history:currency:rate', this.setHistoryCurrencyAndChangeRate)
    app.GlobalDefaultCurrencyMoney = null
  }

  setHistoryCurrencyAndChangeRate = rates => {
    const { standardCurrency, dimentionCurrency } = this.props
    const { dataSource, rateModifiable } = this.state
    const standard = dimentionCurrency?.currency || standardCurrency
    this.fnInitCurrencyInfo(standard, rates, rateModifiable, this.props, true)
    // 系统计算时，自动调整汇率
    const targetRate = rates.find(el => el.numCode === dataSource.foreignNumCode)
    const targetBudgetRate = rates.find(el => el.numCode === dataSource.budgetNumCode)
    const changeTargetRate = targetRate && dataSource?.rate
    const changeTargetBudgetRate = targetBudgetRate && dataSource?.budgetRate
    const receivingAmount = this.isReceivingAmount && dataSource?.foreignNumCode
    // 自动字段和手动字段，触发预算汇率和核酸汇率，找到新汇率，切换汇率，触发计算
    if ((changeTargetRate && !this.isReceivingAmount) || (changeTargetRate && receivingAmount)) {
      this._handleRateChange({ target: { value: targetRate.rate }, isUpdateSysRate: true })
    }
    if (changeTargetBudgetRate) {
      this._handleBudgetRateChange({ target: { value: targetBudgetRate.budgetRate } })
    }
  }

  componentWillReceiveProps(nextProps) {
    const standardEmpt = isEmpty(this.props.value?.standard)
    const valueEmpty = isEmpty(nextProps.value)
    const receivingAmountEmpty = this.isReceivingAmount && !nextProps.value && standardEmpt == valueEmpty
    if (
      nextProps.value !== this.props.value &&
      nextProps.field?.field === this.props.field?.field &&
      !receivingAmountEmpty
    ) {
      if (nextProps.value && nextProps.field?.field === 'amount') {
        this.handleAmountFieldChange(nextProps.value)
      }
      if ((!nextProps.value || typeof nextProps.value === 'string') && nextProps.isAuto) {
        const { dataSource } = this.state
        const data = updateValueMoney({
          value: dataSource,
          money: nextProps.value,
          prefix: 'standard',
          currency: this.selectCurrency
        })
        return this.setState({ dataSource: data }, this._getResult)
      }
      let data = nextProps.value
      if (!data && this.__CONTINUE_ADD__) {
        let { dataSource } = this.state
        data = this.fnContinueAddMoney({ ...dataSource })
        return this.setState({ dataSource: data })
      } else if (!data) {
        const curValue = this.props.value
        if (!!curValue) {
          curValue.standard = data
          if (hasForeign(curValue)) {
            curValue.foreign = data
          }
          if (hasBudget(curValue)) {
            curValue.budget = data
          }
        }
        data = initValue(curValue)
        return this.setState({ dataSource: data })
      }
      data = initValue(data)
      const { dimentionCurrency } = nextProps
      const isCal = ['formula', 'costStandard'].includes(nextProps.field?.defaultValue?.type)
      const currentData = this.state.dataSource
      //说明哪个地方把budget清空了，需要重新计算预算金额
      const needCalculateBudgetAmount = getBoolVariation('cyxq-69934-need-calculate-budget-amount') && !!currentData?.budgetNumCode && !nextProps.value?.budgetNumCode
      if ((isCal || needCalculateBudgetAmount) && data.standardStrCode !== BudgetStrCode && dimentionCurrency?.rates) {
        const budgetCurrency = dimentionCurrency.rates.find(rate => rate.strCode === BudgetStrCode)
        if (data.standard !== undefined && Number(budgetCurrency?.budgetRate)) {
          const budget = new Big(data.standard).div(budgetCurrency.budgetRate).toFixed(budgetCurrency.scale)
          if (budget !== data?.budget) {
            data = updateCurrencyValueByDimention(
              data,
              dimentionCurrency?.currency,
              dimentionCurrency?.rates,
              undefined,
              { useOriginForeign: true }
            )
            this.setState({ dataSource: data }, this._getResult)
            return
          }
        }
      }
      this.setState({ dataSource: data })
    }
    if (
      !isEqual(this.props.dimentionCurrency, nextProps.dimentionCurrency) ||
      !isEqual(this.props.allowModifiable, nextProps.allowModifiable)
    ) {
      this._initCurrencyList(nextProps)
    }
  }

  componentDidMount() {
    const {
      value,
      field = {},
      form = {},
      template,
      currencySwitch,
      dimentionCurrency,
      shouldSaveFeetype,
      allCurrencyRates,
      detalsAllCurrencyRates
    } = this.props
    let rates = detalsAllCurrencyRates || dimentionCurrency?.rates || allCurrencyRates || []
    if (shouldSaveFeetype || detalsAllCurrencyRates) this.setHistoryCurrencyAndChangeRate(rates)
    if (!value && field.name === 'companyRealPay') {
      const amountValue = form.getFieldValue && form.getFieldValue('amount')
      amountValue && this.fillCompanyRealPayByforeignCurrencyPower('companyRealPay', amountValue)
    }
    let currency = template?.find(i => i?.type === 'ref:basedata.Enum.currency')
    if (currencySwitch && currency) {
      this.setState({ foreignDisabled: true })
    }
    if (field?.defaultValue?.customRuleId) {
      // 如果金额字段设置了自动计算币种自定义，则不可切换外币币种
      this.setState({ foreignDisabled: true })
    }
  }

  _initCurrencyList = props => {
    const { dimentionCurrency, standardCurrency, allCurrencyRates, allowModifiable } = props
    if (dimentionCurrency) {
      this.fnInitCurrencyInfo(dimentionCurrency?.currency, dimentionCurrency?.rates, allowModifiable, props)
    } else {
      const rates = allCurrencyRates?.filter(i => i?.originalId === standardCurrency?.numCode)
      this.fnInitCurrencyInfo(standardCurrency, rates, allowModifiable, props)
    }
  }

  fnInitCurrencyInfo = (currency, currencyRates = [], rateModifiable, props, isSetRate = false) => {
    const allCurrencyRates = currencyRates.slice()
    let {
      value,
      selectCurrencyDisable,
      foreignCurrencyLoan,
      foreignNumCode,
      dimentionCurrency,
      currencyConfig,
      field
    } = props
    foreignNumCode = foreignNumCode ? foreignNumCode : (foreignCurrencyLoan && foreignCurrencyLoan.foreignNumCode) || ''
    let canSelectCurrency = allCurrencyRates.length
    let isContainer = allCurrencyRates.find(line => line?.numCode === currency?.numCode)
    if (!isContainer) {
      allCurrencyRates.unshift({ ...currency, rate: currency?.rate || 1 })
      // 存储rate 数据
      api.invokeService('@bills:update:currencyRatesList', { items: allCurrencyRates })
    }
    if (hasForeign(value)) {
      this.selectCurrency = allCurrencyRates.find(item => item?.numCode === value.foreignNumCode)
    }
    const data = {
      allCurrencyRates,
      canSelectCurrency,
      rateModifiable: rateModifiable ?? currencyConfig?.allowEditRate
    }
    let dataSource = initValue(value, this.selectCurrency)
    // 收款金额已经转化过了 时间口径会再次触发赋值，如果本位币一致，就不初始化了
    if (
      dimentionCurrency &&
      !this.isReceivingAmount &&
      value?.standardNumCode !== dimentionCurrency?.currency.numCode
    ) {
      // 初始化 法人实体币种 为 本位币
      dataSource = initDimentionCurrencyValue(value, dimentionCurrency?.currency)
    }
    if (selectCurrencyDisable && foreignNumCode) {
      if (dataSource?.foreignNumCode !== foreignNumCode) {
        this.selectCurrency = allCurrencyRates.find(item => item?.numCode === foreignNumCode)
        dataSource = updateValueCurrency(dataSource, this.selectCurrency, this.constantForeignMoney)
      }
    }
    // 发票编辑时兼容海外发票展示数据，海外发票不再以汇率去更新数据了
    if (dimentionCurrency && !field?.name?.startsWith('E_system_海外发票_金额') && !this.isReceivingAmount) {
      dataSource = updateCurrencyValueByDimention(
        dataSource,
        dimentionCurrency?.currency,
        dimentionCurrency?.rates,
        null,
        { useOriginForeign: true }
      )
    }
    // 设置汇率并且当前value 值是空就不处理他的值，设置汇率触发了两次（antd的坑），不知道是哪里触发的
    if (!isSetRate && !value) {
      data.dataSource = dataSource
    }
    this.setState(data)
  }

  isReceivingAmountNotStandar = value => {
    const { dimentionCurrency, standardCurrency } = this.props
    const currency = dimentionCurrency?.currency ?? standardCurrency
    const isReceivingAmountUpdate =
      this.isReceivingAmount && value?.standardNumCode !== (currency?.numCode || currency?.id)

    // 收款币种和本位币一致情况下，走原来逻辑
    return isReceivingAmountUpdate
  }

  fnContinueAddMoney = (value = {}) => {
    value.standard = ''

    if (value.rate && !this.isReceivingAmountNotStandar(value)) {
      value.rate = value.sysRate
    }
    if (value.foreignNumCode) {
      value.foreign = ''
    }
    if (value.budgetNumCode) {
      value.budget = ''
    }
    return value
  }

  handleContinueAdd = () => {
    this.__CONTINUE_ADD__ = true
  }

  handleOriginalCurrency = currency => {
    this._handleCurrencyChange(currency, true)
    this.setState({
      allCurrencyRates: [currency],
      foreignDisabled: true
    })
  }
  _handleCurrencyChange = (currency, isSetDefault) => {
    const { bus, field = {}, billData } = this.props
    const { dataSource, allCurrencyRates } = this.state
    const standardCurrency = allCurrencyRates[0]?.numCode ?? allCurrencyRates[0]?.id
    let copyCurrency = currency
    if (
      this.isReceivingAmount &&
      dataSource.standardNumCode !== standardCurrency // allCurrencyRates 第一项永远是本位币，所以判断不等于本位币就赋值当前
    ) {
      const copyDataSource = setStandard(dataSource, billData, allCurrencyRates)
      const rate = getExchangeRateDifferential(copyDataSource.standardNumCode, currency.numCode)
      copyCurrency = { ...currency, rate: rate, sysRate: rate }
      this.selectCurrency = copyCurrency
    } else {
      this.selectCurrency = currency
    }

    const data = updateValueCurrency(dataSource, copyCurrency, this.constantForeignMoney)
    this.setState({ dataSource: data }, this._getResult)

    // 单独针对币种切换的事件
    !isSetDefault &&
      bus.emit('currency:money:select:change', {
        data,
        allCurrencyRates,
        currency,
        isForeign: hasForeign(data),
        field: field
      })
    if (field.name === 'amount') {
      this.fillCompanyRealPayByforeignCurrencyPower('amount', data)
    }
  }

  handleDimentionCurrencyChange = ({ currency, rates }) => {
    const { dataSource: data, rateModifiable } = this.state
    this.selectCurrency = currency
    this.fnInitCurrencyInfo(currency, rates, rateModifiable, this.props)
    const dataSource = updateCurrencyValueByDimention(data, currency, rates, true)
    this.setState({ dataSource }, this._getResult)
  }

  fillCompanyRealPayByforeignCurrencyPower = (fieldName, data) => {
    // 开了多币种核销
    const { writtenOff = {} } = this.props
    if (writtenOff.foreignCurrency) {
      const { field = {}, form = {} } = this.props
      if (field.name === fieldName) {
        const targetName = 'companyRealPay'
        const fieldInstance = form.getFieldInstance && form.getFieldInstance(targetName)
        if (fieldInstance) {
          const targetValue = (form.getFieldValue && form.getFieldValue(targetName)) || {}
          data = cloneDeep(data)
          data.standard = targetValue.standard || 0
          if (data.hasOwnProperty('foreignNumCode')) {
            data.foreign = targetValue.foreign || 0
          }
          if (
            ((!data.foreign && data.standard) || !isEqual(data.rate, targetValue.rate)) &&
            data.rate &&
            data.foreignScale &&
            data.standard
          ) {
            data.foreign = new Big(data.standard).div(data.rate).toFixed(Number(data.foreignScale))
          }
          form.setFieldsValue && form.setFieldsValue({ companyRealPay: data })
        }
      }
    }
  }
  // 组件加载完成
  initFinish = data => {
    setTimeout(this.setDefaultCurrency.bind(this, data), 400)
  }
  setDefaultCurrency = ({ currency }) => {
    const { dataSource } = this.state
    this.selectCurrency = currency
    const data = updateValueCurrency(dataSource, currency, this.constantForeignMoney)
    this.setState({ dataSource: data }, this._getResult)
    // this._handleCurrencyChange(currency, isSetDefault)
  }
  onBlur = e => {
    this.inputting = false
    this.handleAmountFieldChange(e.target.value)
  }
  onFocus = e => {
    this.inputting = true
  }
  handleAmountFieldChange = value => {
    try {
      if (!this.inputting && value) {
        const money = value?.standard ?? value
        const { dataSource, isSelectedAutoRate, isSelectedAutoBudgetRate } = this.state
        const { foreignCurrencyLoan, bus, field, isManualRepayment, MULTICURRENCYWRITTENOFF } = this.props
        const hasAuthority = isManualRepayment && (MULTICURRENCYWRITTENOFF || CROSSCURRENCYWRITTENOFF)
        if (hasAuthority && !!foreignCurrencyLoan && !isEqual(foreignCurrencyLoan.rate, dataSource.rate)) {
          dataSource.rate = foreignCurrencyLoan.rate
          dataSource.sysRate = foreignCurrencyLoan.sysRate
        }
        let data = updateValueMoney({
          value: dataSource,
          money,
          prefix: 'standard',
          isSelectedAutoRate,
          isSelectedAutoBudgetRate,
          currency: this.selectCurrency
        })
        data = { ...data, ...(data.standard || {}) } // 兼容value 为对象的情况
        bus && bus.emit('dynamic:value:blur', { [field.field]: data })
        bus && bus.emit('savebtn:state:change', { disabled: false })
      }
    } catch (e) { }
  }

  _handleForeignMoneyChange = e => {
    this.expectCurrency = 'foreign'
    this._handleMoneyChange(e, 'foreign')
  }

  _handleStandardMoneyChange = e => {
    this.expectCurrency = 'standard'
    this._handleMoneyChange(e, 'standard')
  }

  _handleBudgetMoneyChange = e => {
    this.expectCurrency = 'budget'
    this._handleMoneyChange(e, 'budget')
  }

  _handleMoneyChange = (e, prefix) => {
    const money = e.target.value
    const { dataSource, isSelectedAutoRate, isSelectedAutoBudgetRate } = this.state
    const { foreignCurrencyLoan, isManualRepayment, MULTICURRENCYWRITTENOFF } = this.props
    const hasAuthority = isManualRepayment && MULTICURRENCYWRITTENOFF
    if (hasAuthority && !!foreignCurrencyLoan && !isEqual(foreignCurrencyLoan.rate, dataSource.rate)) {
      dataSource.rate = foreignCurrencyLoan.rate
      dataSource.sysRate = foreignCurrencyLoan.sysRate
    }
    let data = updateValueMoney({
      value: dataSource,
      money,
      prefix,
      isSelectedAutoRate,
      isSelectedAutoBudgetRate,
      currency: this.selectCurrency
    })
    this.setState({ dataSource: data }, this._getResult)
    const { field = {} } = this.props
    if (field.name === 'amount') {
      this.fillCompanyRealPayByforeignCurrencyPower('amount', data)
    }
  }

  _handleRateChange = e => {
    let rate = e.target.value
    const isUpdateSysRate = e?.isUpdateSysRate
    if (isNaN(Number(rate)) || Number(rate) < 0) {
      return
    }
    const { disabled } = this.props
    const { dataSource } = this.state
    if (disabled) {
      this.expectCurrency = 'standard'
    }

    // 汇率一致不更新
    if (isEqual(rate.toString(), dataSource?.rate?.toString())) {
      return
    }

    const data = updateValueRate(
      dataSource,
      rate,
      this.constantForeignMoney || this.expectCurrency,
      'foreign',
      isUpdateSysRate
    )
    this.setState({ dataSource: data }, this._getResult)
    const { field = {} } = this.props
    if (field.name === 'amount') {
      this.fillCompanyRealPayByforeignCurrencyPower('amount', data)
    }
  }

  _handleBudgetRateChange = e => {
    let rate = e.target.value
    if (isNaN(Number(rate)) || Number(rate) < 0) {
      return
    }
    const { disabled } = this.props
    const { dataSource } = this.state
    if (disabled) {
      this.expectCurrency = 'standard'
    }
    const data = updateValueRate(dataSource, rate, this.constantForeignMoney || this.expectCurrency, 'budget')
    this.setState({ dataSource: data }, this._getResult)
    const { field = {} } = this.props
    if (field.name === 'amount') {
      this.fillCompanyRealPayByforeignCurrencyPower('amount', data)
    }
  }

  _getResult = (isValidator = true) => {
    const { valueChange } = this.props
    const { dataSource } = this.state
    const p = { isValidator, data: dataSource }
    valueChange(p)
  }

  _handleRecoveryRate = () => {
    const { dataSource } = this.state
    let rate = this.selectCurrency.rate
    if (this.isReceivingAmount && dataSource?.foreignNumCode && dataSource?.standardNumCode) {
      rate = getExchangeRateDifferential(dataSource?.standardNumCode, dataSource?.foreignNumCode)
    }
    const data = updateValueRate(dataSource, rate, this.expectCurrency, 'foreign')
    this.setState({ dataSource: data }, this._getResult)
    const { field = {} } = this.props
    if (field.name === 'amount') {
      this.fillCompanyRealPayByforeignCurrencyPower('amount', data)
    }
  }

  _handleRecoveryBudgetRate = () => {
    const { dimentionCurrency } = this.props
    if (dimentionCurrency) {
      const rate = dimentionCurrency.rates.find(rate => rate.strCode === BudgetStrCode)
      if (rate) {
        const { dataSource } = this.state
        const data = updateValueRate(dataSource, rate.budgetRate, this.expectCurrency, 'budget')
        this.setState({ dataSource: data }, this._getResult)
        const { field = {} } = this.props
        if (field.name === 'amount') {
          this.fillCompanyRealPayByforeignCurrencyPower('amount', data)
        }
      }
    }
  }

  _handleAutoRate = () => {
    const { isSelectedAutoRate } = this.state
    this.setState({ isSelectedAutoRate: !isSelectedAutoRate })
  }

  _handleAutoBudgetRate = () => {
    const { isSelectedAutoBudgetRate } = this.state
    this.setState({ isSelectedAutoBudgetRate: !isSelectedAutoBudgetRate })
  }

  _foreignSelectAble = () => {
    const { currencySelAble, selectCurrencyDisable } = this.props
    return currencySelAble && !selectCurrencyDisable
  }

  _standSelectAble = () => {
    const { dataSource, canSelectCurrency } = this.state
    const { currencySelAble, selectCurrencyDisable, form = {}, field = {}, writtenOff = {} } = this.props
    const isForeign = hasForeign(dataSource)
    const hasAmountField = form.getFieldInstance && form.getFieldInstance('amount')
    if (writtenOff.foreignCurrency && field.name === 'companyRealPay' && hasAmountField && !isForeign) {
      return false
    }
    return !isForeign && canSelectCurrency && currencySelAble && !selectCurrencyDisable
  }

  _getCustomRateTips = value => {
    const { currencyConfig, dimentionCurrency, standardCurrency } = this.props
    const isForeign = hasForeign(value)

    let text = ''
    const currency = dimentionCurrency?.currency ?? standardCurrency // 当前单据的本位币
    if (
      currencyConfig?.allowReferenceRate &&
      this.isReceivingAmount &&
      isForeign &&
      value.standardNumCode !== currency.numCode
    ) {
      text = i18n.get('（参考汇率）')
    }
    return text
  }

  _handleRateDropdownChange = key => {
    const autoRate = key === 'calculateByAmount'
    this.setState({
      isSelectedAutoRate: autoRate,
      isSelectedAutoBudgetRate: autoRate
    })
  }

  render() {
    const {
      disabled,
      placeholder,
      currencySelAble,
      foreignCurrencyLoan,
      field = {},
      writtenOff = {},
      currencyConfig,
      isManualRepayment,
      MULTICURRENCYWRITTENOFF,
      showLabel = true,
      style = {},
      noPopupContainer,
      billData,
      dimentionCurrency,
      standardCurrency,
      isFlow,
      isForeignCurrencyEdit = false
    } = this.props
    const { dataSource, allCurrencyRates } = this.state
    const { isSelectedAutoRate, isSelectedAutoBudgetRate, foreignDisabled, rateModifiable } = this.state
    const isForeign = hasForeign(dataSource)
    const isBudget = hasBudget(dataSource)
    const isOnlyCurrency = !!foreignCurrencyLoan
    const value0 = isForeign ? dataSource.foreign : dataSource.standard
    const isDisabled = writtenOff.foreignCurrency && field.name === 'companyRealPay'
    const hasAuthority = isManualRepayment && MULTICURRENCYWRITTENOFF
    useEUI = this.props.useEUI || false
    const Input = useEUI ? EUIInputNumber : AntdInput
    const euiInputNumberStyle = { width: '100%', flex: 1 }
    const size = useEUI ? 'default' : 'large'
    const label = this.isReceivingAmount ? i18n.get('收款币种') : i18n.get('本位币')
    const standardMoney = this.isReceivingAmount ? setStandard(dataSource, billData, allCurrencyRates) : dataSource
    const hiddlenRate = this.isReceivingAmount
    const customRateTips = this._getCustomRateTips(standardMoney)
    const currency = dimentionCurrency?.currency ?? standardCurrency // 当前单据的本位币
    const allowReferenceRate =
      this.isReceivingAmount && !currencyConfig?.allowReferenceRate && dataSource?.standardNumCode !== currency?.numCode
    const isRateDisable = disabled || !rateModifiable || !currencySelAble || isDisabled || isSelectedAutoRate // 汇率置灰
    const rateTips =
      this.isReceivingAmount && allowReferenceRate
        ? i18n.get('请联系管理员维护企业汇率，维护后此按钮可恢复汇率初始设定')
        : i18n.get('恢复汇率初始设定')
    const hiddenRateLocal = field.hiddenRate && field.hiddenLocal && isForeign
    const enUs = i18n.currentLocale === 'en-US'

    if (useEUI) {
      const renderBudgetRateDom = (
        <>
          <Input
            style={euiInputNumberStyle}
            size={size}
            placeholder={i18n.get('请输入汇率')}
            value={dataSource?.budgetRate}
            disabled={
              disabled ||
              !currencyConfig.allowEditBudgetRate ||
              !currencySelAble ||
              isDisabled ||
              isSelectedAutoBudgetRate
            }
            onChange={this._handleBudgetRateChange}
            addonBefore={renderRateInputBefore(dataSource, 'budget')}
          />
          {!disabled && (isDisabled ? !isDisabled : currencyConfig.allowEditBudgetRate && currencySelAble) && (
            <>
              <DefaultBudgetRateTip
                disabled={disabled}
                isDisabled={isDisabled}
                allowEditBudgetRate={currencyConfig?.allowEditBudgetRate}
                currencySelAble={currencySelAble}
                isSelectedAutoBudgetRate={isSelectedAutoBudgetRate}
                dimensionCurrency={dimentionCurrency}
                onClick={this._handleRecoveryBudgetRate}
                isFlow={isFlow}
              />
              {!isFlow && (
                <Tooltip
                  title={i18n.get('根据输入值自动计算汇率')}
                  className="ml-8"
                  placement="topRight"
                  align={{ targetOffset: [-10, 0] }}
                >
                  <Button
                    icon={<OutlinedEditPaCalculation />}
                    category="secondary"
                    size="middle"
                    onClick={this._handleAutoBudgetRate}
                    theme={isSelectedAutoRate ? 'highlight' : 'default'}
                    data-testid="auto-budget-rate-btn"
                  />
                </Tooltip>
              )}
            </>
          )}
        </>
      )

      const renderRateDom = (
        <RateDom
          Input={Input}
          enUs={enUs}
          euiInputNumberStyle={euiInputNumberStyle}
          size={size}
          isRateDisable={isRateDisable}
          hasAuthority={hasAuthority}
          isOnlyCurrency={isOnlyCurrency}
          foreignCurrencyLoan={foreignCurrencyLoan}
          dataSource={dataSource}
          disabled={disabled}
          isDisabled={isDisabled}
          rateModifiable={rateModifiable}
          currencySelAble={currencySelAble}
          isSelectedAutoRate={isSelectedAutoRate}
          allowReferenceRate={allowReferenceRate}
          selectCurrency={this.selectCurrency}
          isReceivingAmount={this.isReceivingAmount}
          _handleRateChange={this._handleRateChange}
          _handleRecoveryRate={this._handleRecoveryRate}
          _handleAutoRate={this._handleAutoRate}
          customRateTips={customRateTips}
          isFlow={isFlow}
        />
      )

      return (
        <>
          {isFlow && isForeign && !disabled && (isDisabled ? !isDisabled : rateModifiable && currencySelAble) && (
            <div className={styles['currency-money-rate-dropdown']} data-testid="currency-money-rate-dropdown">
              <PageHeader
                onTitleChange={this._handleRateDropdownChange}
                title={[
                  {
                    label: i18n.get('根据汇率计算金额'),
                    key: 'calculateByRate',
                    'data-testid': 'calculate-by-rate'
                  },
                  {
                    label: i18n.get('根据金额计算汇率'),
                    key: 'calculateByAmount',
                    'data-testid': 'calculate-by-amount'
                  }
                ]}
              />
            </div>
          )}
          <Col
            className={`${styles['currency-money']} ${hiddenRateLocal ? styles.hiddenRateLocal : ''}`}
            style={{ ...style, lineHeight: 0 }}
          >
            {isForeign && (
              <div className="mb-8 money-foreign">
                <Input
                  style={euiInputNumberStyle}
                  size={size}
                  value={value0}
                  disabled={disabled}
                  placeholder={placeholder}
                  onChange={this._handleForeignMoneyChange}
                  addonBefore={renderCurrency(
                    dataSource,
                    isDisabled ? !isDisabled || isForeignCurrencyEdit : this._foreignSelectAble() || isForeignCurrencyEdit,
                    'foreign',
                    this._handleCurrencyChange,
                    allCurrencyRates,
                    foreignDisabled ||isForeignCurrencyEdit,
                    i18n.get('原币'),
                    noPopupContainer,
                    hiddlenRate
                  )}
                />
              </div>
            )}
            <div className={!showLabel || field?.isSimple ? '' : 'mb-8 money-local'}>
              <Col span={enUs || !isForeign ? 0 : 11} style={{ display: 'flex' }}>
                <Input
                  style={euiInputNumberStyle}
                  className={!hiddenRateLocal && 'mr-8'}
                  size={size}
                  value={standMoney(dataSource.standard)} //不知道在什么时间切换模板的时候把standard变成NaN了？？？
                  disabled={(this.isReceivingAmount && dataSource?.foreignNumCode) || isOnlyCurrency || disabled}
                  placeholder={placeholder}
                  onChange={this._handleStandardMoneyChange}
                  onBlur={this.onBlur}
                  onFocus={this.onFocus}
                  addonBefore={renderCurrency(
                    standardMoney,
                    this._standSelectAble(),
                    'standard',
                    this._handleCurrencyChange,
                    allCurrencyRates,
                    foreignDisabled,
                    showLabel && label,
                    noPopupContainer,
                    hiddlenRate
                  )}
                />
              </Col>
              {!enUs && isForeign && renderRateDom}
            </div>
            {enUs && isForeign && <div className="mb-8 money-foreign">{renderRateDom}</div>}
            {isBudget && (
              <>
                <div className="mb-8">
                  <Col span={!enUs ? 11 : 0} style={{ display: 'flex' }}>
                    <Input
                      style={euiInputNumberStyle}
                      className={'mr-8'}
                      size={size}
                      value={standMoney(dataSource.budget)}
                      disabled={isOnlyCurrency || disabled}
                      placeholder={placeholder}
                      onChange={this._handleBudgetMoneyChange}
                      addonBefore={renderCurrency(dataSource, false, 'budget', null, null, null, i18n.get('预算币'))}
                    />
                  </Col>
                  {!enUs && (
                    <Col span={13} style={{ display: 'flex' }}>
                      {renderBudgetRateDom}
                    </Col>
                  )}
                </div>
                {enUs && <div className="mb-8">{renderBudgetRateDom}</div>}
              </>
            )}
            {showLabel && <div className={'eui-chinese-money'}>{formatToChineseMoney(dataSource)}</div>}
          </Col>
        </>
      )
    } else {
      return (
        <Col className={styles['currency-money']} style={style}>
          {isForeign && (
            <div className="mb-15">
              <Col span={11} style={{ display: 'flex' }}>
                <div className="currency-money-label">{i18n.get('原 币')}</div>
                {renderCurrency(
                  dataSource,
                  isDisabled ? !isDisabled  : this._foreignSelectAble(),
                  'foreign',
                  this._handleCurrencyChange,
                  allCurrencyRates,
                  foreignDisabled
                )}
                <Input
                  className={classNames('input-border mr-8', { 'ekb-auto-disabled': disabled })}
                  size={size}
                  value={value0}
                  disabled={disabled}
                  placeholder={placeholder}
                  onChange={this._handleForeignMoneyChange}
                />
              </Col>
              <Col span={13} className="dis-f">
                {renderRateInputBefore(dataSource, 'foreign')}
                <Input
                  className={classNames('input-border', { 'ekb-auto-disabled': disabled || isSelectedAutoRate })}
                  size={size}
                  placeholder={i18n.get('请输入汇率')}
                  value={
                    hasAuthority && isOnlyCurrency && !isEqual(foreignCurrencyLoan.rate, dataSource.rate)
                      ? foreignCurrencyLoan.rate
                      : dataSource.rate
                  }
                  disabled={disabled || !rateModifiable || !currencySelAble || isDisabled || isSelectedAutoRate}
                  onChange={this._handleRateChange}
                />
                {!disabled && (isDisabled ? !isDisabled : rateModifiable && currencySelAble) && (
                  <>
                    <DefaultRecoveryRateTip
                      disabled={disabled}
                      isDisabled={isDisabled}
                      rateModifiable={rateModifiable}
                      currencySelAble={currencySelAble}
                      isSelectedAutoRate={isSelectedAutoRate}
                      selectCurrency={this.selectCurrency}
                      isReceivingAmount={this.isReceivingAmount}
                      dataSource={dataSource}
                      onClick={this._handleRecoveryRate}
                    />
                    <span
                      className={classNames('reset-rate-wrap ml-8', { 'selected-rate': isSelectedAutoRate })}
                      onClick={this._handleAutoRate}
                    >
                      <EKBIcon
                        className={classNames('icon reset-rate')}
                        tooltipTitle={i18n.get('根据输入值自动计算汇率')}
                        name="#EDico-auto"
                      />
                    </span>
                  </>
                )}
              </Col>
            </div>
          )}
          <div className={!showLabel || field?.isSimple ? '' : 'mb-15'} style={{ display: 'flex' }}>
            {showLabel && <div className="currency-money-label">{i18n.get('本位币')}</div>}
            {renderCurrency(
              dataSource,
              this._standSelectAble(),
              'standard',
              this._handleCurrencyChange,
              allCurrencyRates,
              foreignDisabled
            )}
            <Input
              className={classNames('input-border', { 'ekb-auto-disabled': disabled })}
              size={size}
              value={standMoney(dataSource.standard)} //不知道在什么时间切换模板的时候把standard变成NaN了？？？
              disabled={isOnlyCurrency || disabled}
              placeholder={placeholder}
              onChange={this._handleStandardMoneyChange}
              onBlur={this.onBlur}
              onFocus={this.onFocus}
            />
          </div>
          {showLabel && <div className={'chinese-money'}>{formatToChineseMoney(dataSource)}</div>}
          {isBudget && (
            <div className="mb-15">
              <Col span={11} className="dis-f">
                <div className="currency-money-label">{i18n.get('预算币')}</div>
                {renderCurrency(dataSource, false, 'budget')}
                <Input
                  className={classNames('input-border mr-8', { 'ekb-auto-disabled': disabled })}
                  size={size}
                  value={standMoney(dataSource.budget)}
                  disabled={isOnlyCurrency || disabled}
                  placeholder={placeholder}
                  onChange={this._handleBudgetMoneyChange}
                />
              </Col>
              <Col span={13} style={{ display: 'flex' }}>
                {renderRateInputBefore(dataSource, 'budget')}
                <Input
                  className={classNames('input-border', { 'ekb-auto-disabled': disabled || isSelectedAutoBudgetRate })}
                  size={size}
                  placeholder={i18n.get('请输入汇率')}
                  value={dataSource?.budgetRate}
                  disabled={
                    disabled ||
                    !currencyConfig.allowEditBudgetRate ||
                    !currencySelAble ||
                    isDisabled ||
                    isSelectedAutoBudgetRate
                  }
                  onChange={this._handleBudgetRateChange}
                />
                {!disabled && (isDisabled ? !isDisabled : currencyConfig.allowEditBudgetRate && currencySelAble) && (
                  <>
                    <DefaultBudgetRateTip
                      disabled={disabled}
                      isDisabled={isDisabled}
                      allowEditBudgetRate={currencyConfig?.allowEditBudgetRate}
                      currencySelAble={currencySelAble}
                      isSelectedAutoBudgetRate={isSelectedAutoBudgetRate}
                      dimensionCurrency={dimentionCurrency}
                      onClick={this._handleRecoveryBudgetRate}
                    />
                    <span
                      className={classNames('reset-rate-wrap ml-8', { 'selected-rate': isSelectedAutoBudgetRate })}
                      onClick={this._handleAutoBudgetRate}
                    >
                      <EKBIcon
                        className={classNames('icon reset-rate')}
                        tooltipTitle={i18n.get('根据输入值自动计算汇率')}
                        name="#EDico-auto"
                      />
                    </span>
                  </>
                )}
              </Col>
            </div>
          )}
        </Col>
      )
    }
  }
}

function formatToChineseMoney(dataSource) {
  const money = Number(dataSource.standard)
  if (dataSource.standardStrCode === 'CNY' && !isNaN(money) && i18n.currentLocale === 'zh-CN') {
    return MoneyNzh.toMoney(money, { outSymbol: false })
  }
  return null
}

function standMoney(standdard) {
  return isNaN(standdard) && standdard !== '-' ? undefined : standdard
}

function renderCurrency(
  dataSource,
  selectAble,
  type = 'foreign',
  onChange,
  allCurrency = [],
  disabled = false,
  label = '',
  noPopupContainer,
  hiddlenRate
) {
  return selectAble
    ? renderCurrencyBeforeTitle(dataSource, onChange, allCurrency, disabled, label, noPopupContainer, hiddlenRate)
    : renderCurrentBeforeTitle(dataSource, type, label)
}

function getValueStrCode(value) {
  const isForeign = hasForeign(value)
  return isForeign ? value.foreignStrCode : value.standardStrCode
}

function setStandard(amountObj, billData, rates) {
  const numCode = billData.payeeId?.receivingCurrency || billData?.receivingCurrency
  const rate = rates?.find(rate => rate.numCode === numCode)
  if (!rate) {
    return amountObj
  }
  return {
    ...amountObj,
    standardUnit: rate.unit,
    standardStrCode: rate.strCode,
    standardNumCode: numCode,
    standardScale: rate.scale,
    standardSymbol: rate.symbol
  }
}

function renderCurrencyBeforeTitle(value, onChange, data = [], disabled, label, noPopupContainer, hiddlenRate) {
  const title = getValueStrCode(value)
  const checked = data.filter(element => element?.strCode === title)
  return (
    <CurrencyDropdown
      isFeeTypeAmount
      trigger={['click']}
      data={data}
      onChange={onChange}
      checkedData={checked}
      disabled={disabled}
      useEUI={useEUI}
      hiddlenRate={hiddlenRate}
      noPopupContainer={noPopupContainer}
    >
      {useEUI ? (
        <div className={styles['eui-input-group-addon-title']}>
          {label ? `${label}（${title}）` : title}
          {!disabled && <OutlinedDirectionDown fontSize={12} className="ml-4 ai-c" /> }
        </div>
      ) : (
        <div className={`${styles['currency-money-currency-title']} class1`}>
          <div className="currency-title">{title}</div>
          <img className="img" src={CurrencyDownImage} />
        </div>
      )}
    </CurrencyDropdown>
  )
}

function renderCurrentBeforeTitle(value, prefix, label) {
  let title = getValueStrCode(value)
  if (prefix) {
    title = value[`${prefix}StrCode`]
  }
  if (useEUI) {
    return <div className={styles['eui-input-group-addon-title']}>{`${label || ''}（${title}）`}</div>
  } else {
    return <div className={`${styles['currency-money-currency-title']} class2`}>{title}</div>
  }
}

function renderRateInputBefore(value, type = 'foreign', customString = '') {
  const rate = type === 'foreign' ? value.rate : value.budgetRate || '-'
  let str = `${rate || '-'} = ${value.standardStrCode} : ${value[`${type}StrCode`]}${customString}`

  return (
    <div
      className={
        useEUI ? classNames('flex-1', styles['eui-input-group-addon-title']) : styles['currency-money-currency-title']
      }
    >
      <span className="rate">{type === 'foreign' ? i18n.get('核算汇率') : i18n.get('预算汇率')}</span>
      {useEUI ? (
        <Tooltip title={str} align={{ targetOffset: [0, 8] }}>
          <OutlinedTipsMaybe fontSize={12} />
        </Tooltip>
      ) : (
        <EKBIcon className="icon img" tooltipTitle={str} name="#EDico-editbeifen2" />
      )}
    </div>
  )
}

const RateDom = ({
  Input,
  euiInputNumberStyle,
  size,
  isRateDisable,
  hasAuthority,
  isOnlyCurrency,
  foreignCurrencyLoan,
  dataSource,
  disabled,
  isDisabled,
  rateModifiable,
  currencySelAble,
  isSelectedAutoRate,
  allowReferenceRate,
  selectCurrency,
  isReceivingAmount,
  _handleRateChange,
  _handleRecoveryRate,
  _handleAutoRate,
  customRateTips,
  enUs,
  isFlow
}) => (
  <Col span={enUs ? 0 : 13} className="dis-f">
    <Input
      style={euiInputNumberStyle}
      size={size}
      placeholder={isRateDisable ? i18n.get('暂无汇率') : i18n.get('请输入汇率')}
      value={
        hasAuthority && isOnlyCurrency && !isEqual(foreignCurrencyLoan.rate, dataSource.rate)
          ? foreignCurrencyLoan.rate
          : dataSource.rate
      }
      disabled={isRateDisable}
      onChange={_handleRateChange}
      className={'rate-input'}
      addonBefore={renderRateInputBefore(dataSource, 'foreign', customRateTips)}
    />
    {!disabled && (isDisabled ? !isDisabled : rateModifiable && currencySelAble) && (
      <>
        <DefaultRecoveryRateTip
          disabled={disabled}
          isDisabled={isDisabled}
          rateModifiable={rateModifiable}
          currencySelAble={currencySelAble}
          isSelectedAutoRate={isSelectedAutoRate}
          allowReferenceRate={allowReferenceRate}
          selectCurrency={selectCurrency}
          isReceivingAmount={isReceivingAmount}
          dataSource={dataSource}
          onClick={_handleRecoveryRate}
          isFlow={isFlow}
        />
        {!isFlow && (
          <Tooltip
            title={i18n.get('根据输入值自动计算汇率')}
            className="ml-8"
            placement="topRight"
            align={{ targetOffset: [-10, 0] }}
          >
            <Button
              icon={<OutlinedEditPaCalculation />}
              category="secondary"
              size="middle"
              onClick={_handleAutoRate}
              theme={isSelectedAutoRate ? 'highlight' : 'default'}
              data-testid="auto-rate-btn"
            />
          </Tooltip>
        )}
      </>
    )}
  </Col>
)
