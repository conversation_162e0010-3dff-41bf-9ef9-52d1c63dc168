import { action } from './constant'
import { BillActionBase } from './base-action'
import { app } from '@ekuaibao/whispered'

export class PayAction extends BillActionBase {
  action = action.pay
  label = i18n.get('支付')
}

export class WithdrawPayAction extends BillActionBase {
  label = i18n.get('撤销支付')

  onAction = async () => {
    return this.handleWithdraw()
  }

  handleWithdraw = () => {
    const { backlog, bus } = this.props
    // 找出纳的会签节点
    const cashierCountersign = backlog?.flowId?.plan?.nodes?.find(
      node => node?.name === '出纳支付' && node?.type === 'countersign'
    )
    app.close()
    const getApproverPromise = cashierCountersign?.counterSigners?.length
      ? Promise.resolve({})
      : app.invokeService('@bills:withdraw:approvers', { id: backlog.flowId.id })
    return getApproverPromise
      .then(approver => {
        return app.open('@bills:BillWithdrawModal', { flow: approver, cashierCountersign })
      })
      .then(params => {
        const {
          flowId: { id }
        } = backlog
        params = { ...params, flowId: id }
        return app.invokeService('@bills:withdraw:save', params)
      })
      .then((_: never) => {
        bus?.reload?.()
        app.invokeService('@common:get:backlog:count:payment')
      })
  }
}
