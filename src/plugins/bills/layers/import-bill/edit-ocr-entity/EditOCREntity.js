import React, { PureComponent } from 'react'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import moment from 'moment'
import { Dynamic } from '@ekuaibao/template'
import { Alert } from '@hose/eui'
import { Button, Icon, Form } from 'antd'
import styles from './EditOCREntity.module.less'
import OCRTemplateMap from './config.js'
import { editable } from '../../../../../components'
import MessageCenter from '@ekuaibao/messagecenter'
import Money from '../../../../../elements/puppet/Money'
import OCREntityImage from './OCREntityImage'
import { saveOCRcardInfo, getInputInvoiceData } from '../../../bills.action'
import { app as api } from '@ekuaibao/whispered'
import { OCREntityMasterMap } from './OCREntityUtil'
import { canShowPreviewImage, getInvoiceType } from '../util'
import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'
import { showMessage } from '@ekuaibao/show-util'
import { get } from 'lodash'
import { handlerCurrencyMoneySelectChange } from '../../../util/defaultCurrency.js'
import { OutlinedTipsClose } from '@hose/eui-icons'
import { logEditInvoiceCurreny } from '../../../../../lib/logs'
import { Tooltip } from '@hose/eui'
import { enableOtherInvoiceByDimension } from '../../../../../lib/featbit/feat-switch'

const newElement = [...editable]

const layout = {
  labelCol: { span: 24, style: { fontSize: '14px' } },
  wrapperCol: { span: 24 }
}

function create(T) {
  return Form.create({
    withRef: false,
    onValuesChange(props, values) {
      setTimeout(() => {
        props.bus.emit('dynamic:value:changed', values)
      }, 0)
    }
  })(T)
}

@EnhanceModal({
  className: 'custom-modal-layer'
})
export default class EditOCREntity extends PureComponent {
  constructor(props) {
    super(props)
    let { invoiceInfo } = props
    this.state = {
      value: this.fnFormatValue(invoiceInfo),
      template: [],
      tooltipOpen: true
    }
  }
  bus = new MessageCenter()

  componentDidMount() {
    const { invoiceInfo } = this.props
    let { form } = invoiceInfo.master
    const _value = form && form.E_system_发票主体_发票代码
    this.isInvoiceCode(_value)
    this.bus.on('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
  }

  componentWillUnmount() {
    this.bus.un('currency:money:select:change', this._handlerCurrencyMoneySelectChange)
  }

  _handlerCurrencyMoneySelectChange = data => {
    handlerCurrencyMoneySelectChange.call(this, data, 'editInvoice')
  }

  fnFormatValue = invoiceInfo => {
    const { form } = invoiceInfo.master
    if (invoiceInfo.master?.entityId === 'system_非税收入类票据') {
      form['E_system_非税收入类票据_收款单位'] = !!form['E_system_非税收入类票据_收款单位']?.length
        ? form['E_system_非税收入类票据_收款单位']
        : form['E_system_非税收入类票据_发票种类']
    }
    return form
  }

  // 校验海外币种
  validateDemissCurreny = async()=>{
    let pass = true
    const { invoiceInfo } = this.props
    if (api.has('get:bills:value')) {
      const dimentionCurrency = api.getState()['@bills'].dimentionCurrencyInfo
      const { values } = await api.invoke('get:bills:value') || {}
      const { form, entityId } = invoiceInfo.master
      const money = form[`E_${entityId}_金额`] || {}
      // 只要海外票校验，海外票法人实体金额做了转换
      if(values?.legalEntityMultiCurrency && entityId === 'system_海外发票'){
        const baseCurrencynumCode = values?.legalEntityMultiCurrency?.form?.baseCurrencyId || dimentionCurrency?.currency?.numCode
        if(baseCurrencynumCode && baseCurrencynumCode !== money?.standardNumCode){
          pass = false
          showMessage.error(i18n.get('当前币种不符合发票主体的币种要求，请刷新页面重新选择'))
          logEditInvoiceCurreny({...values, ...invoiceInfo})
        }
      }
    }

    return pass
  }

  handleOK = async () => {
    const { invoiceInfo, submitterId = {} } = this.props
    const newInvoiceInfo = { ...invoiceInfo }
    let { form } = invoiceInfo.master
    const invoiceType = form.E_system_发票主体_发票类别
    const region = form.E_system_发票主体_识别范围 // @i18n-ignore
    const validateCurreny = await this.validateDemissCurreny()
    if(!validateCurreny){
      return
    }

    this.bus.getValueWithValidate().then(res => {
      Object.keys(res).forEach(item => {
        if (res[item] === undefined) {
          res[item] = ''
        }
      })
      newInvoiceInfo.master.form = { ...newInvoiceInfo.master.form, ...res }
      const {
        master: { id, entityId, form }
      } = invoiceInfo
      let value = newInvoiceInfo.master.form
      const params = { invoiceId: id, entityId, value }
      if (entityId === 'system_海外发票') {
        const money = value['E_system_海外发票_金额']
        const originCurrency = value['E_system_海外发票_currency']
        const currency = money?.foreignNumCode ?? money?.standardNumCode ?? originCurrency
        value['E_system_海外发票_currency'] = currency
      }
      if (entityId === 'system_发票主体') {
        if (!res['E_system_发票主体_发票金额'] && !res['E_system_发票主体_校验码']) {
          return showMessage.error(i18n.get('请输入金额或校验码'))
        }
        this.setState({ tooltipOpen: false })
        // @i18n-ignore
        const {
          E_system_发票主体_发票代码, // @i18n-ignore
          E_system_发票主体_发票号码, // @i18n-ignore
          E_system_发票主体_发票日期, // @i18n-ignore
          E_system_发票主体_发票金额, // @i18n-ignore
          E_system_发票主体_校验码 // @i18n-ignore
        } = res || {}
        let kprq =
          typeof E_system_发票主体_发票日期 === 'number' // @i18n-ignore
            ? moment(E_system_发票主体_发票日期).format('YYYYMMDD') // @i18n-ignore
            : E_system_发票主体_发票日期 // @i18n-ignore
        const fpje =
          typeof E_system_发票主体_发票金额 === 'object'
            ? get(E_system_发票主体_发票金额, 'standard', '')
            : E_system_发票主体_发票金额
        const fpImg = form?.E_system_发票主体_图片 // @i18n-ignore
        let query = {
          source: 'OCR',
          fpdm: E_system_发票主体_发票代码, // @i18n-ignore
          fphm: E_system_发票主体_发票号码, // @i18n-ignore
          jym: E_system_发票主体_校验码, // @i18n-ignore
          kprq: kprq,
          fpje: fpje,
          staffId: submitterId.id || '',
          region
        }
        if (invoiceType === 'BLOCK_CHAIN') {
          query = Object.assign(query, { fpje: fpje, isBlockChain: true })
        }
        if (fpImg != void 0) {
          query = Object.assign(query, { key: fpImg })
        }
        api.dispatch(getInputInvoiceData(query)).then(
          res => {
            res['_id'] = id
            this.props.layer.emitOk(res)
          },
          err => {
            return showMessage.error(err.message)
          }
        )
      } else {
        this.setState({ tooltipOpen: false })
        api.dispatch(saveOCRcardInfo(params)).then(res => {
          newInvoiceInfo.master = res.value
          this.props.layer.emitOk(newInvoiceInfo)
          //showMessage.success('编辑成功，可重新发起自动匹配')
        })
      }
    })
  }

  handleCancel = () => {
    this.setState({ tooltipOpen: false })
    this.props.layer.emitCancel()
  }

  renderInvoiceHeader = () => {
    const {
      invoiceInfo: { master }
    } = this.props
    const { entityId, form } = master
    if (entityId !== 'system_发票主体' && entityId !== 'system_航空运输电子客票行程单') {
      //@i18n-ignore
      return null
    }
    let { E_system_发票主体_购买方名称, E_system_发票主体_发票日期, E_system_发票主体_价税合计 } = form
    const name = E_system_发票主体_购买方名称
    const money = E_system_发票主体_价税合计
    const date = moment(E_system_发票主体_发票日期).format(i18n.get('YYYY年MM月DD日'))
    // -电子发票（增值税专用发票），电子发票（普通发票），电子发票（航空运输电子客票行程单）和 电子发票（铁路电子客票）在【金额】字段处输入价税合计总金额
    // - 其他增值税发票在【金额】字段处输入不计税金额
    const message = <>
      <div>
        {i18n.get(
          '- {__k0}，{__k1}，{__k2}和 {__k3}在【金额】字段处输入{__k4}',
          {
            __k0: i18n.get('电子发票（增值税专用发票）'),
            __k1: i18n.get('电子发票（普通发票）'),
            __k2: i18n.get('电子发票（航空运输电子客票行程单）'),
            __k3: i18n.get('电子发票（铁路电子客票）'),
            __k4: i18n.get('价税合计总金额')
          })}
      </div>
      <div>
        {i18n.get('- 其他增值税发票在【金额】字段处输入{__k0}', { __k0: i18n.get('不计税金额') })}
      </div>
    </>
    return <>
      {entityId === 'system_发票主体' && <Alert message={message} type="info" style={{ marginBottom: '8px' }} />}
      <div className="edit-content-head">
        {entityId === 'system_发票主体' ? ( // @i18n-ignore
          <div>
            <div className="invoice-edit-intr">
              <div className="invoice-edit-title">{name}</div>
              <div className="invoice-edit-date">{date}</div>
            </div>
            <div className="invoice-edit-total">
              <Money className="invoice-edit-money" value={money} />
              <div className="invoice-edit-merge">{i18n.get('价税合计')}</div>
            </div>
          </div>
        ) : (
          <div>
            <div className="invoice-edit-intr">
              <div className="invoice-tip">
                {i18n.get('当前仅支持单程机票单的识别，如导入多行程单，则默认显示第一行程')}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  }

  getCaptcha = (invoiceCode, isSetFieldsValue) => {
    const { invoiceInfo } = this.props
    let { form } = invoiceInfo.master
    const isFullDigital = form?.E_system_发票主体_发票号码?.length === 20
    // let invoiceType = getInvoiceType(invoiceCode)
    if (form && form.E_system_发票主体_发票类别 === 'BLOCK_CHAIN') {
      this.setState({ template: OCRTemplateMap['system_Token'] })
    } else if (invoiceCode !== null) {
      api.invokeService('@bills:get:invoice:inputType', { fpdm: invoiceCode, fphm: '' }).then(res => {
        const { fplx, showType } = res?.value || {}
        if (isFullDigital) {
          this.setState({ template: OCRTemplateMap['allFormAvailalbe'] })
        } else if (showType === 'ALL') {
          this.setState({ template: OCRTemplateMap['system_Token'] })
        } else if (showType === 'AMOUNT') {
          this.setState({ template: OCRTemplateMap['system_发票主体'] })
        } else {
          this.setState({ template: OCRTemplateMap['addFormCheck'] })
        }
      })
    } else {
      //普通发票
      this.setState({ template: OCRTemplateMap['addFormCheck'] })
    }
    if (isSetFieldsValue) {
      setTimeout(() => {
        this.bus.getValue().then(res => {
          this.setState({
            value: res
          })
        })
      }, 0)
    }
  }

  isInvoiceCode = (code, isSetFieldsValue) => {
    const { invoiceInfo } = this.props
    let { entityId } = invoiceInfo.master
    if (entityId === 'system_发票主体') {
      // @i18n-ignore
      this.getCaptcha(code, isSetFieldsValue)
    } else {
      this.setState({ template: OCRTemplateMap[entityId] })
    }
  }

  onBlur = (e, field) => {
    // @i18n-ignore
    if (field.name === 'E_system_发票主体_发票代码') {
      const code = e.target.value
      if (!code) return
      this.isInvoiceCode(code, 'isSetFieldsValue')
    }
  }

  renderContent = () => {
    const { template, value } = this.state
    let time = value.E_system_发票主体_发票日期
    if (time && time.length === 8) {
      let y = time.slice(0, 4)
      let m = time.slice(4, 6)
      let d = time.slice(6, 8)
      time = +moment(y + '-' + m + '-' + d)
    }
    value.E_system_发票主体_发票日期 = time
    let { invoiceInfo, attachment, isForeignCurrencyEdit } = this.props
    let { form } = invoiceInfo.master
    const url = get(attachment, 'url', '')
    const img = form['E_system_发票主体_PDF'] || form['E_system_发票主体_图片'] || url
    // @i18n-ignore
    const keys = { key: img, regions: form['E_system_发票主体_识别范围'] }
    const canPreviewImage = canShowPreviewImage(attachment?.fileName)
    const isCurrencyEdit = !['system_海外发票','system_其他','system_发票主体'].includes(invoiceInfo.master.entityId) && isForeignCurrencyEdit

    return (
      <div className="edit-content">
        {canPreviewImage ? <OCREntityImage keys={keys} /> : null}
        <div className={canPreviewImage ? 'edit-content-right' : 'edit-invoice-content'}>
          <Dynamic
            className={'edit-invoice-content-template'}
            bus={this.bus}
            useEUI={true}
            create={create.bind(this)}
            value={value}
            template={template}
            isForeignCurrencyEdit={isCurrencyEdit}
            elements={newElement}
            layout={layout}
            onEditBlur={this.onBlur}
            noPopupContainer={true}
          />
        </div>
      </div>
    )
  }

  renderHeader = () => {
    const { invoiceInfo } = this.props
    const { entityId, form } = invoiceInfo.master
    let title = ''
    if (entityId === 'system_发票主体') {
      // @i18n-ignore
      title = INVOICE_TYPE()[form && form[i18n.get('E_system_发票主体_发票类别')]] || i18n.get('增值税发票')
    } else {
      title = OCREntityMasterMap[entityId]
    }
    return (
      <div className="modal-header">
        {i18n.get('编辑') + title}
        <OutlinedTipsClose  className="close-icon" onClick={this.handleCancel} />
      </div>
    )
  }

  renderFooter = () => {
     const  { currency } = api.getState()['@bills'].dimentionCurrencyInfo || {}
     const isdimentionCurrency = currency && enableOtherInvoiceByDimension()
     const { tooltipOpen } = this.state

    return (
      <div className="modal-footer">
        {
          !isdimentionCurrency && <Button key="cancel" size="large" className="mr-20" onClick={this.handleCancel}>
            {i18n.get('取消')}
          </Button>
        }
         <Tooltip title={i18n.get('存在法人实体信息，请核对金额，重新保存发票信息')} placement="topRight" open={isdimentionCurrency && tooltipOpen} >
           <Button key="ok" type="primary" size="large" onClick={this.handleOK}>
            {i18n.get('保存')}
          </Button>
        </Tooltip>
      </div>
    )
  }

  render() {
    return (
      <div className={styles['edit-invoice-wrap']}>
        {this.renderHeader()}
        <div className="edit-invoice-content">
          {this.renderInvoiceHeader()}
          {this.renderContent()}
        </div>
        {this.renderFooter()}
      </div>
    )
  }
}
