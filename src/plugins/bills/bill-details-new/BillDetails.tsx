import React, { useMemo } from 'react'
import BillInfoEditableContainer from '../parts/right-part/billInfo/BillInfoEditableContainer'
import BillInfoReadOnlyContainer from '../parts/right-part/billInfo/BillInfoReadOnlyContainer'
import MessageCenter from '@ekuaibao/messagecenter'
import { UserInfo, Backlog } from './types'
import { EnhanceConnect } from '@ekuaibao/store'
import { ILayerProps } from '@ekuaibao/enhance-layer-manager'

interface BillDetailsPropsDerivedFromState {
  userInfo: UserInfo
}

export interface BillDetailsProps extends BillDetailsPropsDerivedFromState {
  /**
   * 单据详情数据
   */
  backlog: Backlog
  bus?: MessageCenter
  layer?: ILayerProps['layer']
  callBack?: () => void
  /**
   * 是否展示顶部的单据导航按钮（上一个/下一个）
   */
  showUpDown?: boolean
  /**
   * 自动打开的明细ID
  */
  autoOpenDetailId?: string
  /**
   * 直接传递给单据详情组件的props
  */
  billDetailsProps?: any
  /**
   * 强制使用只读或编辑模式，此时会忽略单据状态
  */
  forceMode?: 'readonly' | 'editable'
  footer: React.ReactNode
  riskData: any
  /**
   * 在只读态时是否可以删除评论，在审批流中使用
  */
  canDeleteComment?: boolean
  /**
   * 小权限id，小权限内的单据动作比如删除评论
  */
  privilegeId?: string
}

export const BillDetails = ({
  backlog,
  userInfo,
  bus,
  layer,
  callBack,
  showUpDown = true,
  autoOpenDetailId,
  billDetailsProps,
  forceMode,
  footer,
  riskData,
  canDeleteComment,
  privilegeId,
}: BillDetailsProps) => {
  const { flowId: flow } = backlog

  const isEditable = useMemo(() => {
    if (forceMode === 'readonly') {
      return false
    } else if (forceMode === 'editable') {
      return true
    }
    if (flow.state === 'new') {
      return true
    }
    const editableState = ['draft', 'rejected']
    const isEditableState = editableState.includes(flow.state)
    const isOwner = flow.ownerId?.id === userInfo.staff.id
    return isEditableState && isOwner
  }, [flow, userInfo, forceMode])

  const detailStack = useMemo(() => {
    if (autoOpenDetailId) {
      return { id: autoOpenDetailId }
    }
    return undefined
  }, [autoOpenDetailId])

  if (isEditable) {
    return (
      <BillInfoEditableContainer
        billFooter={footer}
        layer={layer}
        bus={bus}
        dataSource={flow}
        showHeaderClose
        showFullScreenDrawer
        callBack={callBack}
        showUpDown={showUpDown}
        riskData={riskData}
        canDeleteComment={canDeleteComment} // 驳回状态时，可以删除评论
        privilegeId={privilegeId}
        {...billDetailsProps}
      />
    )
  }

  return (
    <BillInfoReadOnlyContainer
      billFooter={footer}
      showUpDown={showUpDown}
      layer={layer}
      bus={bus}
      dataSource={flow}
      detailStack={detailStack}
      showHeaderClose
      showFullScreenDrawer
      riskData={riskData}
      canDeleteComment={canDeleteComment}
      privilegeId={privilegeId}
      {...billDetailsProps}
      onOpenOwnerLoanList={
        billDetailsProps?.onOpenOwnerLoanList
        ? () => billDetailsProps?.onOpenOwnerLoanList(backlog)
        : undefined
      }
    />
  )
}

const EnhanceConnectBillDetails = EnhanceConnect((state: any) => ({
  userInfo: state['@common'].userinfo.data,
}))(BillDetails)

export default EnhanceConnectBillDetails as React.ComponentType<BillDetailsProps>
