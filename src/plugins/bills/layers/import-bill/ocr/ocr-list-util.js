/**
 *  Created by zyb on 2019/1/23.
 */
import moment from 'moment'
import { getMoney } from '../../../../../lib/misc'
import { isElectronicAirAndTrain, isElectronicTitle, getFormattedInvoiceTitle } from '../invoice/ImportInvoiceUtils';

const regionID = i18n.get('E_system_发票主体_识别范围')
import { INVOICE_TYPE } from '@ekuaibao/lib/lib/enums'

export function formatOCRResult(rep) {
  rep = Array.isArray(rep) ? { items: rep } : rep
  const { items = [] } = rep
  return items.map(item => {
    const {
      master: { entityId }
    } = item
    const func = OCRFormateMap[entityId]
    return func(item)
  })
}

const OCRFormateMap = {
  system_发票主体: formateInvoce,
  system_医疗发票: formateMedicalInvoce,
  system_非税收入类票据: formateNontaxInvoce,
  system_火车票: formateTrain,
  system_出租车票: formateTaxi,
  system_其他: formateOther,
  system_过路费发票: formateRoadToll,
  system_客运汽车发票: formatePassengerCar,
  system_航空运输电子客票行程单: formateAircraft,
  system_定额发票: formateQuota,
  system_消费小票: formateShopping,
  system_机打发票: formateMachine,
  system_海外发票: formatOverseas
}

export const getTitleByInvoiceMark = (invoiceForm, prefix) => {
  if(isElectronicAirAndTrain(invoiceForm)){
    return isElectronicTitle(invoiceForm)
  }else if (invoiceForm?.['E_system_发票主体_进销项标识'] !== 'OUTPUT_INVOICE_RECEIPT') {
    const title = invoiceForm[i18n.get(`{__k0}_销售方名称`, { __k0: prefix })]
    return getFormattedInvoiceTitle(title)
  }
  const title = invoiceForm[i18n.get(`{__k0}_购买方名称`, { __k0: prefix })]
  return getFormattedInvoiceTitle(title)
}

function formateInvoce(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_发票主体')
  const date = master.form[i18n.get(`{__k0}_发票日期`, { __k0: prefix })]
  const dateStr = moment(date).format(i18n.get('YYYY年MM月DD日'))
  const title = getTitleByInvoiceMark(master.form, prefix)
  const invoiceName = INVOICE_TYPE()[master.form[i18n.get('E_system_发票主体_发票类别')]] || i18n.get('增值税发票')
  return {
    id: master.id,
    title: title,
    subTitle: dateStr,
    money: getMoney(master.form[i18n.get(`{__k0}_价税合计`, { __k0: prefix })]),
    type: 'Invoice',
    tagName: invoiceName,
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateMedicalInvoce(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_医疗发票')
  const date = master.form[i18n.get(`{__k0}_开票日期`, { __k0: prefix })]
  const dateStr = moment(date).format(i18n.get('YYYY年MM月DD日'))
  const title = master.form[i18n.get(`{__k0}_发票种类`, { __k0: prefix })]
  const invoiceName = i18n.get('医疗发票')
  return {
    id: master.id,
    title: getFormattedInvoiceTitle(title),
    subTitle: dateStr,
    money: master.form[i18n.get(`{__k0}_金额合计`, { __k0: prefix })],
    type: 'medicalInvoice',
    tagName: invoiceName,
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateNontaxInvoce(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_非税收入类票据')
  const date = master.form[i18n.get(`{__k0}_开票日期`, { __k0: prefix })]
  const dateStr = moment(date).format(i18n.get('YYYY年MM月DD日'))
  const title = master.form[i18n.get(`{__k0}_发票种类`, { __k0: prefix })] ||
    master.form[i18n.get(`{__k0}_收款单位`, { __k0: prefix })]
  const invoiceName = master.form?.E_system_非税收入类票据_发票类别
  return {
    id: master.id,
    title: getFormattedInvoiceTitle(title),
    subTitle: dateStr,
    money: master.form[i18n.get(`{__k0}_金额合计`, { __k0: prefix })],
    type: 'Nontax',
    tagName: invoiceName,
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateTrain(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_火车票')
  const fromCity = master.form[i18n.get(`{__k0}_上车车站`, { __k0: prefix })]
  const toCity = master.form[i18n.get(`{__k0}_下车车站`, { __k0: prefix })]
  const date = `${moment(master.form[i18n.get(`{__k0}_乘车时间`, { __k0: prefix })]).format(
    i18n.get('YYYY年MM月DD日 HH:mm')
  )}`
  const trainNumber = master.form[i18n.get(`{__k0}_车次`, { __k0: prefix })] || ''
  const passenger = master.form[i18n.get(`{__k0}_乘车人姓名`, { __k0: prefix })]
  
  // 判断拼接字段都为空的情况
  let title
  if (!fromCity && !toCity) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = i18n.get(`{__k0} —— {__k1}`, { __k0: fromCity || '', __k1: toCity || '' })
  }
  
  return {
    id: master.id,
    title: title,
    subTitle: `${date} ${trainNumber} ${passenger}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'Train',
    tagName: i18n.get('铁路客票'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateTaxi(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_出租车票')
  const city = `${master.form[i18n.get(`{__k0}_发票所在地`, { __k0: prefix })]}`
  const date = `${moment(master.form[i18n.get(`{__k0}_上车时间`, { __k0: prefix })]).format(
    i18n.get('YYYY年MM月DD日 HH:mm')
  )}-${moment(master.form[i18n.get(`{__k0}_下车时间`, { __k0: prefix })]).format('HH:mm')}`
  const mileage = master.form[i18n.get(`{__k0}_里程`, { __k0: prefix })]
  const mileageStr = mileage ? `${mileage}km` : ''
  return {
    id: master.id,
    title: getFormattedInvoiceTitle(i18n.get(city)),
    subTitle: `${date} ${mileageStr}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'Taxi',
    tagName: i18n.get('出租车票'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateOther(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_其他')
  const date = `${moment(master.form[i18n.get(`{__k0}_日期`, { __k0: prefix })]).format(i18n.get('YYYY年MM月DD日'))}`
  return {
    id: master.id,
    title: i18n.get('其他'), // 固定 title，不需要变更
    subTitle: `${date}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'Other',
    tagName: i18n.get('其他票据'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formatOverseas(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_海外发票')
  const date = `${moment(master.form[i18n.get(`{__k0}_日期`, { __k0: prefix })]).format(i18n.get('YYYY年MM月DD日'))}`
  const title = master.form[`${prefix}_title`]
  return {
    id: master.id,
    title: getFormattedInvoiceTitle(title),
    subTitle: `${date}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    moneyForeign: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'Overseas',
    tagName: master.form[i18n.get(`{__k0}_票据类型`, { __k0: prefix })] || i18n.get('海外票据'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateRoadToll(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_过路费发票')
  const entrance = master.form[i18n.get(`{__k0}_入口`, { __k0: prefix })]
  const exit = master.form[i18n.get(`{__k0}_出口`, { __k0: prefix })]
  const date = `${moment(master.form[i18n.get(`{__k0}_时间`, { __k0: prefix })]).format(
    i18n.get('YYYY年MM月DD日 HH:mm')
  )}`
  
  // 判断拼接字段都为空的情况
  let title
  if (!entrance && !exit) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = i18n.get(`${entrance || ''} - ${exit || ''}`)
  }
  
  return {
    id: master.id,
    title: title,
    subTitle: `${date}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'RoadToll',
    tagName: i18n.get('过路费发票'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formatePassengerCar(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_客运汽车发票')
  const fromStation = master.form[i18n.get(`{__k0}_出发车站`, { __k0: prefix })]
  const toStation = master.form[i18n.get(`{__k0}_达到车站`, { __k0: prefix })]
  const date = `${moment(master.form[i18n.get(`{__k0}_时间`, { __k0: prefix })]).format(
    i18n.get('YYYY年MM月DD日 HH:mm')
  )}`
  const passenger = master.form[i18n.get(`{__k0}_姓名`, { __k0: prefix })]
  
  // 判断拼接字段都为空的情况
  let title
  if (!fromStation && !toStation) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = i18n.get(`${fromStation || ''} - ${toStation || ''}`)
  }
  
  return {
    id: master.id,
    title: title,
    subTitle: `${date} ${passenger}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'PassengerCar',
    tagName: i18n.get('客运汽车票'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateAircraft(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_航空运输电子客票行程单')
  const fromStation = master.form[i18n.get(`{__k0}_出发站`, { __k0: prefix })]
  const toStation = master.form[i18n.get(`{__k0}_到达站`, { __k0: prefix })]
  const date = `${moment(master.form[i18n.get(`{__k0}_乘机时间`, { __k0: prefix })]).format(
    i18n.get('YYYY年MM月DD日 HH:mm')
  )}`
  const planNumber = master.form[i18n.get(`{__k0}_航班号`, { __k0: prefix })]
  const name = master.form[i18n.get(`{__k0}_乘机人姓名`, { __k0: prefix })]
  const seat = master.form[i18n.get(`{__k0}_航班舱型`, { __k0: prefix })]
  
  // 判断拼接字段都为空的情况
  let title
  if (!fromStation && !toStation) {
    title = getFormattedInvoiceTitle(null)
  } else {
    title = `${fromStation || ''} - ${toStation || ''}`
  }
  
  return {
    id: master.id,
    title: title,
    subTitle: `${date} ${planNumber} ${name} ${seat}`,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'Aircraft',
    tagName: i18n.get('机票行程单'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateQuota(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = i18n.get('E_system_定额发票')
  return {
    id: master.id,
    title: i18n.get('定额发票'), // 固定 title，不需要变更
    subTitle: '',
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'Quota',
    tagName: i18n.get('定额发票'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateShopping(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = 'E_system_消费小票' // @i18n-ignore
  let title = master.form[`${prefix}_店名`] // @i18n-ignore
  const date = `${moment(master.form[`${prefix}_时间`]).format(i18n.get('YYYY年MM月DD日 HH:mm'))}` // @i18n-ignore
  return {
    id: master.id,
    title: getFormattedInvoiceTitle(title),
    subTitle: `${date}`,
    money: master.form[`${prefix}_金额`], // @i18n-ignore
    type: 'ShoopingInvoice',
    tagName: '消费小票',
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}

function formateMachine(sourceData) {
  const { master, status, ischeck } = sourceData
  const prefix = 'E_system_机打发票' // @i18n-ignore
  const title = master.form[i18n.get(`{__k0}_销售方名称`, { __k0: prefix })]
  const date = master.form[i18n.get(`{__k0}_时间`, { __k0: prefix })]
  const dateStr = moment(date).format(i18n.get('YYYY年MM月DD日'))
  const invoiceName = master.form?.E_system_机打发票_发票种类
  return {
    id: master.id,
    title: getFormattedInvoiceTitle(title),
    subTitle: dateStr,
    money: master.form[i18n.get(`{__k0}_金额`, { __k0: prefix })],
    type: 'MachineInvoice',
    tagName: invoiceName || i18n.get('机打发票'),
    region: master.form[regionID],
    status,
    ischeck,
    active: master.active,
    sourceData
  }
}
