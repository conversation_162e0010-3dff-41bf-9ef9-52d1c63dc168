import { Modal } from "@hose/eui";
import { app } from "@ekuaibao/whispered";
import { BillActionBase } from "./base-action";
import { retractFlow } from "../../bills.action";

export class RetractAction extends BillActionBase {
  label = i18n.get('撤回')

  onAction = () => {
    return this.handleConfirmRetract()
  }

  handleConfirmRetract = () => {
    return new Promise((rs, rj) => {
      const { backlog } = this.props
      const { id } = backlog.flowId
      // const { reloadData } = this
      Modal.confirm({
        title: i18n.get('您是否确认要撤回该单据?'),
        okText: i18n.get('确认'),
        cancelText: i18n.get('取消'),
        onOk() {
          app.dispatch(retractFlow(id)).then((action: any) => {
            if (!action.error) {
              // reloadData()
              rs(1)
            } else {
              rj()
            }
          })
        },
        onCancel: rj,
      })
    })
  }
}