export enum BillStateEnum {
    New = 'new', // 新建
    Draft = 'draft', // 草稿
    Pending = 'pending', // 提交中
    Approving = 'approving', // 审批中
    Paying = 'paying', // 待支付
    Paid = 'paid', // 已完成
    Archived = 'archived', // 已归档
    Nullify = 'nullify', // 已作废
    Rejected = 'rejected', // 已驳回
    Sending = 'sending', // 寄送中
    Receiving = 'receiving', // 收单中
    Withdraw = 'withdraw', // 已撤回
    ReceivingExcep = 'receivingExcep' // 收单异常
}

// 单据生成来源
export enum SourceSignEnum {
    SYSTEM = 'SYSTEM', // 系统创建
}