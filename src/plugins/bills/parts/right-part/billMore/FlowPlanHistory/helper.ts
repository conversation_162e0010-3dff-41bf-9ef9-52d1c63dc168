import { IFlowLog } from './types'
import { getV } from '@ekuaibao/lib/lib/help'
import { get } from 'lodash'

export const filterFlowPlanLogs = (allData: IFlowLog[], filterType: string): IFlowLog[] => {
  let data = allData
  const whitelist = ['current-approving']
  if (filterType === 'comment') {
    data = allData.filter(item => {
      if (whitelist.includes(item.action)) {
        return true
      }
      return (
        item.action !== 'freeflow.carbonCopy' &&
        item.attributes &&
        (item.attributes.comment?.length > 0 || item.attributes.delOperatorId)
      )
    })
  }
  if (filterType === 'approve') {
    data = allData.filter(item => {
      if (whitelist.includes(item.action)) {
        return true
      }
      return !~['freeflow.comment', 'freeflow.select.approver', 'freeflow.modify'].indexOf(item.action)
    })
  }
  if (filterType === 'staff_approval') {
    data = allData.filter(item => {
      const isStaffApprovalAction = [
        'freeflow.submit',
        'freeflow.reject',
        'freeflow.agree',
        'freeflow.addnode',
        'freeflow.pay', // 支付完成
        'freeflow.pay.by.offline', // 转线下支付
        'freeflow.paying', // 支付中
        'freeflow.pay.partial.paying', // 部分支付中
        'freeflow.pay.partial.success', // 部分支付成功
        'freeflow.pay.partial.failure', // 部分支付失败
        'freeflow.repaying', // 重新支付中
        'freeflow.failure' // 支付失败
      ].includes(item.action)
      if (whitelist.includes(item.action)) {
        return true
      }
      if (!isStaffApprovalAction) {
        return false
      }
      if (getV(item, 'attributes.isEbotNode')) {
        return false
      }
      if (getV(item, 'attributes.isInvoiceApplicationNode')) {
        return false
      }
      if (getV(item, 'attributes.isRecalNode')) {
        return false
      }
      if (item.action === 'freeflow.reject') {
        return item.attributes && !item.attributes.isAuto
      }
      return true
    })
  }
  if (filterType === 'effective') {
    data = allData.filter(item => {
      if (whitelist.includes(item.action)) {
        return true
      }
      const skippedType = [
        'APPROVER_NOT_FOUND',
        'APPROVER_NOT_FOUND_BY_ROLE',
        'NO_ABILITY',
        'PAY_AMOUNT_IS_0'
      ].includes(item?.attributes?.skippedType)
      return !(item?.action === 'freeflow.skipped' && skippedType)
    })
  }
  return data
}

export const isEbotNodeByNode = (item: IFlowLog): boolean => {
  return (
    get(item, 'attributes.isEbotNode') ||
    get(item, 'attributes.isRecalNode') ||
    get(item, 'attributes.isInvoiceApplicationNode')
  )
}

/**
 * 判断节点是否应该被隐藏
 * @param node 节点对象
 * @param options 配置选项
 * @param {boolean} [options.includeAIAgent=false] 是否包含AI审批节点
 * @returns 是否应该隐藏节点
 */
export const shouldHideNode = (node: any, options: {
  includeAIAgent?: boolean
} = {}): boolean => {
  const { includeAIAgent = false } = options

  if (!node) {
    return false
  }

  const { skippedType, type, ebotConfig } = node

  // 检查重算节点和跳过节点的隐藏逻辑
  if (type === 'recalculate' && node.config && node.config.hiddenNode) {
    return true
  }

  if (skippedType !== 'NO_SKIPPED' && node.config && node.config.hiddenNode) {
    return true
  }

  // 检查EBot相关节点的隐藏逻辑
  const isEbotType = type === 'ebot' || type === 'invoicingApplication'
  const isAIAgentType = includeAIAgent && type === 'aiApproval'

  if ((isEbotType || isAIAgentType) && ebotConfig && ebotConfig.hiddenNode) {
    const hiddenModule = node?.ebotConfig?.hiddenModule
    return hiddenModule && hiddenModule.includes('feeflow')
  }

  return false
}

/**
 * 处理隐藏节点的评论项，清空nodeId
 * @param item - 流程日志项
 * @returns 处理后的日志项
 */
export const processHiddenNodeComment = (item: IFlowLog): IFlowLog => {
  return {
    ...item,
    attributes: {
      ...item.attributes,
      nodeId: ''
    }
  }
}

/**
 * 判断流程日志项是否应该显示
 * @param item - 流程日志项
 * @param flowPlanNodeMap - 流程节点映射
 * @returns 是否显示该项
 */
export const shouldShowFlowLogItem = (item: IFlowLog, flowPlanNodeMap: Record<string, any>): boolean => {
  const node = flowPlanNodeMap[item?.attributes?.nodeId]
  
  // 如果找不到对应节点，默认显示
  if (!node) {
    return true
  }
  
  const isHideNode = shouldHideNode(node, { includeAIAgent: true })
  
  // 如果是隐藏节点的评论，需要特殊处理但仍然显示
  if (isHideNode && item.action === 'freeflow.comment') {
    return true
  }
  
  // 其他情况根据节点是否隐藏来决定
  return !isHideNode
}

/**
 * 过滤和处理隐藏节点的数据
 * @param data - 原始流程日志数据
 * @param flowPlanNodeMap - 流程节点映射
 * @returns 处理后的流程日志数据
 */
export const fnFilterHiddenNodes = (data: IFlowLog[], flowPlanNodeMap: Record<string, any>): IFlowLog[] => {
  return data
    .filter(item => shouldShowFlowLogItem(item, flowPlanNodeMap))
    .map(item => {
      const node = flowPlanNodeMap[item?.attributes?.nodeId]
      const isHideNode = node && shouldHideNode(node, { includeAIAgent: true })
      
      // 如果是隐藏节点的评论，处理nodeId
      if (isHideNode && item.action === 'freeflow.comment') {
        return processHiddenNodeComment(item)
      }
      
      return item
    })
}
