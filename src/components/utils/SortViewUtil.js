import React, { useEffect } from 'react'
import { Select } from 'antd'
import styles from './SortViewUtil.module.less'
import EKBIcon from '../../elements/ekbIcon'
const { Option } = Select

const sortType = [
  { label: i18n.get('按金额合计'), value: '1' },
  { label: i18n.get('按发票时间'), value: '2' },
  { label: i18n.get('按风险数量'), value: '3' }
]

const SortSelector = props => {
  const { onTypeChange, onSortChange, isSort = true, value = '2' } = props

  return (
    <div className={styles['sort-selector-bg']}>
      <Select value={value} onChange={onTypeChange}>
        {sortType.map(item => {
          return (
            <Option key={item.value} value={item.value}>
              <div
                style={{ display: 'flex', justifyContent: 'space-between', lineHeight: 'inherit' }}
                className="sort-invoices-item form_item__label_forFix"
              >
                <div>
                  {item.label}
                  {value == item.value ? (
                    <span>{isSort ? i18n.get('降序') : i18n.get('升序')}</span>
                  ) : (
                    <span>{i18n.get('排序')}</span>
                  )}
                </div>
                {value == item.value && (
                  <div className="sort" style={{ display: 'inline-block' }} onClick={onSortChange}>
                    {isSort ? (
                      <>
                        <EKBIcon name="#EDico-shengxu-2" style={{ color: 'var(--gray-13)' }} />
                        <EKBIcon name="#EDico-jiangxu-2" style={{ color: 'var(--brand-base)', marginLeft: '-7px' }} />
                      </>
                    ) : (
                      <>
                        <EKBIcon name="#EDico-shengxu-2" style={{ color: 'var(--brand-base)' }} />
                        <EKBIcon name="#EDico-jiangxu-2" style={{ marginLeft: '-7px', color: 'var(--gray-13)' }} />
                      </>
                    )}
                  </div>
                )}
              </div>
            </Option>
          )
        })}
      </Select>
    </div>
  )
}

export { SortSelector }
