import { BillActionBase } from './base-action'
import { message, Modal } from '@hose/eui'
import { Resource } from '@ekuaibao/fetch'

const invoiceAction = new Resource('/api/v2/invoice')

export class InvoiceReminderAction extends BillActionBase {
  label = i18n.get('提醒补充发票')

  onAction = () => {
    return this.handleInvoiceReminder()
  }

  handleInvoiceReminder = () => {
    const { backlog } = this.props
    return new Promise((resolve, reject) => {
      Modal.confirm({
        title: '确定发送提醒?',
        content:
          '这些单据的补充发票提醒状态将会变为「已提醒」你可以在列表展示里找到「补充发票提醒状态」字段，并以此进行筛选。',
        okText: i18n.get('确定'),
        cancelText: i18n.get('取消'),
        onOk: async () => {
          try {
            await invoiceAction.POST(`/wait/remind/[flowIds]`, {
              flowIds: [backlog.flowId.id]
            })
            message.success(i18n.get('提醒成功'))
            resolve(true)
          } catch {
            reject()
          }
        }
      })
    })
  }
}