/**************************************
 * Created By LinK On 2021/12/9 10:31.
 **************************************/

import React, { useEffect } from 'react'
import ViewSwitcher from '../../../elements/ViewSwitcher'
import { Button } from '@hose/eui'
import { OutlinedTipsAdd } from '@hose/eui-icons'
import { app as api } from '@ekuaibao/whispered'
import {
  fnApportionDetails,
  formatCopyBillData,
  getChangeTemplateModalKey,
  logCreateBillFromRecordNote,
} from '../util/billUtils'
import { enableHidingFinishedBills } from '../../../lib/featbit'
import { AIChatEntryButton } from '../../../components/AIChatEntry'


const BillTableViewHeader = props => {
  const { isList, changeViewType, ifCanCreate } = props
  useEffect(() => {
    api.watch('from:requisition', handleFromRequisition)
    const openTemplateModalDeta = api.getState('@bills.openTemplateModalDeta')

    if (openTemplateModalDeta.isOpen) {
      const { requisitionInfo, isApply } = openTemplateModalDeta
      handleFromRequisition(requisitionInfo, isApply)
    }

    return () => {
      api.un('from:requisition', handleFromRequisition)
    }
  }, [])

  const handleFromRequisition = async (args: any, isApply: boolean) => {
    const { bus } = props
    const modalKey = getChangeTemplateModalKey()
    const data: any = await api.open(modalKey, {
      specificationGroups: args.visibleSpecificationList
    })
    args.defaultSpecification = data
    // 快速报销和随手记的自动分摊逻辑
    if (args.isQuickExpends || args.withNotes?.length) {
      args.withNotes = await fnApportionDetails(args.withNotes, data)
    }

    if (args.noteIds) {
      logCreateBillFromRecordNote({ data: args.noteIds, billSpecificationId: data.id })
    }
    if(isList){
      const callback = () => {
        bus.emit('list:select:change', {
          formType: data.type,
          state: 'new',
          requisitionInfo: isApply ? args : undefined
        })
      }
      bus.invoke('check:value:changed', data.type).then(res => {
          callback()
        },
        error => {
          if (error === 'cancel') return
          callback()
        }
      )
    }else{
      return api.open('@bills:BillInfoCreatePopup', {
        bus,
        data: args.defaultSpecification,
        line: {
          formType: data.type,
          state: 'new',
          requisitionInfo: isApply ? args : undefined
        }
      })
    }

  }
  const handleCheckArchived = () => {
    api.open('@bills:ArchivedStackerModal', {
      viewKey: 'ArchivedTableView',
      type: 'Archived',
      handleCopyBill: async (dataSource: any) => {
        const data = await formatCopyBillData(dataSource)
        return api.open('@bills:BillEditableDrawer', {
          data,
          from: 'from_myBill'
        })
      }
      })
  }

  const handelCreateBillNew = () => {
    api.emit('create:new:bill', { isModal: !isList })
  }

  const fnOpenGuideBillMoreInfo = () =>{
    const { newBillList, newBillTableList} = api.getState()['@bills']
    const isOpenBill = document.getElementById('bill-info-editable-container')
    // 打开第一条单据
    if(!isList && Boolean(newBillTableList?.length)){
      props.bus.emit('table:row:click',newBillTableList[0])
    }else if(newBillList[0]?.flow && !isOpenBill){
      props?.bus?.emit('list:select:change', newBillList[0]?.flow)
    }
  }
  // 清空表格模式数据
  const handleClickList = () =>{
    api.invokeService('@bills:save:get:my:bill:TableList',[])
  }

  return (
    <div className={`bill-view-header`}>
      <span className="bill-view-title">{i18n.get('我的单据')}</span>
      <div className="bill-btn-box">
        {!isList && <AIChatEntryButton className="mr-8"/>}
        {
            !enableHidingFinishedBills() && !isList && <Button className="mr-8" category="secondary" size="middle" onClick={handleCheckArchived}>
              {i18n.get('已完成单据')}
            </Button>
        }
        {
          ifCanCreate && <Button className="mr-8" category="primary" size="middle" icon={<OutlinedTipsAdd />} onClick={handelCreateBillNew}>
              {i18n.get('新建')}
          </Button>
        }
        <ViewSwitcher isList={isList} handleSwitcher={changeViewType} handleClickList = {handleClickList} />
      </div>
    </div>
  )
}

export default BillTableViewHeader
