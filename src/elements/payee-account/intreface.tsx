export interface ChannelProps {
  active: boolean //激活状态
  agencyPay: boolean // 是否支持代发
  batchPay: boolean //是否支持批量支付
  channel: string   //渠道
  code: string  //编码（唯一）
  corporationGrant: boolean //是否需要企业授权 (待定)
  customRemarkLength: boolean //是否针对银行配置摘要长度
  grant: boolean  //是否需要账户授权
  icon: string  //图标
  manualable: boolean //是否需要手动刷新支付结果
  mobilePay: boolean  //是否支持移动端支付
  name: string  //名称
  needPopup: boolean  //是否需要弹窗
  needPurpose: boolean  //是否需要填写用途
  needRemark: boolean //是否需要填写摘要
  powerCode: string   //charge 码
  refreshable: boolean  //是否支持自动刷新
  remarkLength: number  //摘要长度 {"其他":14,"招商银行": 200}
  singlePay: boolean  //是否支持单笔支付
  smsCheck: boolean //短信验证时机，默认为空，为空时不需要短信验证  ["PAY","QUERY","GRANT","REVIEW"]
  validCondition: boolean //是否校验高级选项
}

export interface PayeeConfig {
  createAccount: CreateAccount
  accountSort?: (string)[] | null
  privacy: Privacy
  allowShared: boolean
  forbiddenModify: boolean
  conciseInput: boolean
  forbiddenAdd: boolean
  networkNotRequired: boolean
  remarkDisplay: boolean
  publicBackgroundCls:string
}
export interface CreateAccount {
  checked: boolean
  creator: CreatorOrVisibility
  visible: boolean
}
export interface CreatorOrVisibility {
  fullVisible: boolean
  staffs?: (null)[] | null
  roles?: (null)[] | null
  departments?: (null)[] | null
  departmentsIncludeChildren: boolean
}
export interface Privacy {
  protectPrivacy: boolean
  visibility: CreatorOrVisibility
}


export interface Props {
  formChannel: string
  data: any
  isSelf?: boolean
  onChannelChange?: (value: string) => void
  onClick?: () => void
  handleWalletClick?: (e: any) => void
  handleSetDefault?: () => void
  handleClickCancelDefault?: () => void
  className?: string
  payFromChannel?: string
  isWalletManager?: boolean
  isOpened?: boolean
  parentID?: string
  selectDisabled?: boolean
  configDisabled?: boolean
  inSelectPayMethodModal?: boolean
  isDefault?: boolean
  isSelected?: boolean
  isManangePage?: boolean
  channel?: string
  isSettingDepency?: boolean
  dynamicChannels?: []
  channelList?: []
  dynamicChannelMap?: any
  showCancleDefault?: boolean
  allCurrencyRates?: any
  FOREIGN_CURRENCY_PAY?: boolean
  standardCurrency?: any
  payeeConfig?:{
    publicAccountConfig:PayeeConfig
    personalAccountConfig:PayeeConfig
  },
  globalFields:any[]
  showCollectionBtns?: boolean
  handleCollect?: () => void
  handleCancelCollect?: () => void
  isCNY?:boolean
}