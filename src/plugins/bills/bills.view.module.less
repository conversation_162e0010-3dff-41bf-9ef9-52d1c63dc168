.bills-view {
  // background-color: #fff;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.bills-view-warp {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1 1;
  border-radius: inherit;
  overflow-y: hidden;
  background-color: var(--eui-bg-base);

  &.bills-view-warp-list {
    padding: 16px 8px 16px 16px;
  }

  :global {
    .eui-breadcrumb {
      margin-bottom: 8px;
    }

    .border-radius {
      border-radius: 8px;
    }

    .bill-view-header {
      height: 64px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      background-color: #fff;
      border-bottom: 1px solid var(--eui-line-divider-default);
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;

      .bill-view-title {
        font-size: 16px;
        line-height: 24px;
        font-weight: 500;
        color: var(--eui-text-title);
      }

      .bill-btn-box {
        display: flex;

        .crate-bill-button {
          font-size: 14px;
          padding: 3px 16px;
          border-radius: 4px;
          margin-right: 8px;
        }

        .tips {
          border-radius: 6px;
          border: 1px solid var(--eui-line-border-component);
          font-size: 12px;
          padding: 6px 9px;
          margin-right: 8px;
          cursor: pointer;
          height: 32px;

          &:hover {
            background-color: var(--eui-fill-hover);
          }
        }
      }
    }

    .bills-header {
      border: 0;
    }

    .bills-content {
      padding: 16px 15px 0 15px;
    }

    .bills-left {
      border: 0;
      background: none;
    }

    .layout5-bills-left {
      margin-right: 1px;
      box-shadow: 0px 16px 64px 0px rgba(29, 43, 61, 0.2), 0px 0px 0px 0px var(--eui-line-divider-default);
    }
  }

  &.bills-view-warp-table {
    flex-direction: row;


    .bills-view-warp-table-inner {
      flex: 1;
      overflow-y: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
    }
  }
}


.ai-chat-box {
  width: 360px;
  transition: all 0.5s;

  &.chatHidden {
    width: 0;
    border: none;
    margin-left: 0;
  }
}