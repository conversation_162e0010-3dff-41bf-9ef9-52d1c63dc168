import React, { useMemo } from 'react'
import { Button, Tooltip } from '@hose/eui'
import { OutlinedDirectionRefresh } from '@hose/eui-icons'
import { BudgetStrCode, getExchangeRateDifferential } from '../../components/utils/fnCurrencyObj'

interface DefaultRecoveryRateTipProps {
  disabled: boolean
  isDisabled: boolean
  rateModifiable: boolean
  currencySelAble: boolean
  isSelectedAutoRate: boolean
  allowReferenceRate?: boolean
  selectCurrency: any
  isReceivingAmount: boolean
  dataSource: any
  onClick: () => void
  isFlow?: boolean
}

export const DefaultRecoveryRateTip: React.FC<DefaultRecoveryRateTipProps> = props => {
  const {
    disabled,
    isDisabled,
    rateModifiable,
    currencySelAble,
    isSelectedAutoRate,
    allowReferenceRate,
    selectCurrency,
    isReceivingAmount,
    dataSource,
    onClick,
    isFlow
  } = props
  const currentIsDisabled = isDisabled ? !isDisabled : rateModifiable && currencySelAble

  const rateTips = useMemo(() => {
    let rate = selectCurrency?.rate
    if (isReceivingAmount && dataSource?.foreignNumCode && dataSource?.standardNumCode) {
      rate = getExchangeRateDifferential(dataSource?.standardNumCode, dataSource?.foreignNumCode)
    }
    return i18n.get('恢复初始设定值，企业汇率值为{__k0}', { __k0: rate })
  }, [selectCurrency, isReceivingAmount, dataSource])

  if (!disabled && currentIsDisabled) {
    if(isFlow && (isSelectedAutoRate || allowReferenceRate)) return null
    return (
      <Tooltip title={rateTips} className="ml-8" placement="topRight" arrowPointAtCenter>
        <Button
          icon={<OutlinedDirectionRefresh />}
          category="secondary"
          size="middle"
          disabled={isSelectedAutoRate || allowReferenceRate}
          onClick={onClick}
          data-testid="recovery-rate-btn"
        />
      </Tooltip>
    )
  }
  return null
}

interface DefaultBudgetRateTipProps {
  disabled: boolean
  isDisabled: boolean
  allowEditBudgetRate: boolean
  currencySelAble: boolean
  isSelectedAutoBudgetRate: boolean
  dimensionCurrency: any
  onClick: () => void
  isFlow?: boolean
}

export const DefaultBudgetRateTip: React.FC<DefaultBudgetRateTipProps> = props => {
  const {
    disabled,
    isDisabled,
    allowEditBudgetRate,
    currencySelAble,
    isSelectedAutoBudgetRate,
    dimensionCurrency,
    onClick,
    isFlow
  } = props
  const currentIsDisabled = isDisabled ? !isDisabled : allowEditBudgetRate && currencySelAble

  const rateTips = useMemo(() => {
    let rateTips = ''
    if (dimensionCurrency) {
      const rate = dimensionCurrency.rates.find(rate => rate.strCode === BudgetStrCode)
      if (rate) {
        rateTips = i18n.get('恢复初始设定值，企业汇率值为{__k0}', { __k0: rate.budgetRate })
      }
    }
    return rateTips
  }, [dimensionCurrency])

  if (!disabled && currentIsDisabled) {
    if(isFlow && isSelectedAutoBudgetRate) return null
    return (
      <Tooltip title={rateTips} className="ml-8" placement="topRight" arrowPointAtCenter>
        <Button
          icon={<OutlinedDirectionRefresh />}
          category="secondary"
          size="middle"
          disabled={isSelectedAutoBudgetRate}
          onClick={onClick}
          data-testid="recovery-budget-rate-btn"
        />
      </Tooltip>
    )
  }
  return null
}
