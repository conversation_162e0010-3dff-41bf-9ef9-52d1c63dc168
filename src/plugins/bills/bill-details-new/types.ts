import { ButtonProps } from "@hose/eui"
import { FlowAction } from "../layers/bill-info-popup/FlowAction"

interface FlowCalcRiskWarning {
  active: boolean
  corporationId: string
  createTime: number
  dataCorporationId: null | string
  id: string
  riskWarning: []
  sourceCorporationId: null | string
  status: string
  updateTime: number
}

type BillForm = any
type FlowItemLog = any
export type UserInfo = any
type FlowState = any

export interface FlowItem {
  actions: []
  active: boolean
  appId: string
  calcRiskWarning: FlowCalcRiskWarning
  corporationId: string
  createTime: number
  dataCorporationId: null | string
  flowType: "freeflow"
  form: BillForm
  formType: "expense" | ""
  id:  string
  invoiceRemind: boolean
  logs: FlowItemLog[]
  ownerDefaultDepartment: string
  ownerId: UserInfo
  sourceCorporationId: null | string
  state: FlowState
  updateTime: number
  flowRulePerformLogs: null | {
    results: any
  }
  plan?: {
    taskId: string
    nodes: {
      id: string
      approverId: {
        name: string
      }
      name: string
      type: string
      counterSigners: any[]
    }[]
  }
}

type BacklogState = string

export interface Backlog {
  addCountReport: boolean
  autoApproveType: string
  corporationId: string
  createTime: number
  crossCorp: boolean
  dataCorporationId: null | string
  dbVersion: number
  flowId: FlowItem
  grayver: string
  hangUp: string
  id: string
  isUrgent: boolean
  logId: number
  nodeId: string
  nodeName: string
  ownerId: string
  pipeline: number
  rejectAlert: boolean
  rejectEndTime: number
  remindEndTime: number
  sourceCorporationId: null | string
  state: BacklogState
  threadId: string
  type: string
  updateTime: number
}

export enum FlowActionScene {
  /* 制单人 (我的单据) */
  OWNER = 'OWNER',
  /* 审批人 (待办) */
  APPROVER = 'APPROVER',
  /* 管理员 (单据管理) */
  ADMIN = 'ADMIN',
}


export interface Action {
  action: FlowAction,
  category?: ButtonProps['category'],
  customData?: any,
  describe?: string,
  deviceType?: string,
  enName?: string,
  id: string,
  name: string,
  scene?: FlowActionScene[],
  states?: string[],
  theme?: ButtonProps['theme'],
  type?:  string,
  // 用于自定义按钮，不走actionsMap
  onAction?: () => void
}