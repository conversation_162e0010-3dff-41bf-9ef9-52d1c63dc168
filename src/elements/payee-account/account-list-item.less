@import '~@ekuaibao/eui-styles/less/token.less';

.account-list-item-wrap {
  border-radius: @radius-3;
  padding: @space-7 @space-6 @space-6 @space-8;
  overflow: hidden;
  width: 100%;
  min-height: 108px;
  &:last-child {
    margin-bottom: 0;
  }
  &.account-list-item-not-allowed {
    cursor: not-allowed;
  }
  .account-selected-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    overflow: hidden;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    .account-selected-icon {
      width: 74px;
      height: 74px;
      color: @color-white-2;
    }
  }
  .account-content-wrap {
    color: @color-white-1;
    display: flex;
    flex-direction: column;
    .up-wrap {
      display: flex;
      justify-content: space-between;
      flex: 1;
      position: relative;
      .title-wrap {
        display: flex;
        flex: 1;
        width: 92%;
        align-items: center;
        .account-title {
          .font-size-5;
          .font-weight-3;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          z-index: 1;
        }
        .account-code {
          .font-size-2;
          .font-weight-2;
          max-width: 30%;
          margin-left: @space-4;
        }
        .account-disabled {
          .font-weight-2;
          margin-left: @space-4;
          display: inline-block;
          padding: @space-1 @space-3;
          background: rgba(255, 255, 255, 0.1);
          border-radius: @radius-2;
        }
      }
      .item-setting {
        padding: @space-2;
        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-radius: @radius-2;
        }
      }
    }
    .bottom-wrap {
      display: flex;
      justify-content: space-between;
      .bank-content {
        flex: 1;
        width: 100%;
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        .other-files {
          .font-weight-2;
          z-index: 10;
          margin-top: 5px;
          margin-right: 24px;
          display: inline-block;
          padding: @space-1 @space-3;
          background: rgba(255, 255, 255, 0.1);
          border-radius: @radius-2;
          max-width: 288px;
        }
        .bank-wrap {
          flex: 1;
          display: flex;
          width: 70%;
          align-items: center;
          margin-top: @space-5;
          margin-bottom: @space-4;
          .icon-bg {
            width: 20px;
            height: 20px;
            overflow: hidden;
            margin-right: @space-2;
          }
          .font-size-2;
          .bank-icon {
            border-radius: @radius-5;
            background-color: @color-white-1;
            padding: @space-1;
          }
          .item-setting {
            margin-left: @space-1;
            padding: @space-2;
          }
          .bank-name {
            margin-right: @space-4;
            .font-weight-3;
            max-width: 30%;
          }
          .bank-no {
            font-weight: bold;
            max-width: 80%;
          }
          .currency-str {
            font-weight: bold;
            width: 120px;
            margin-left: @space-4;
          }
          .bank-large-no {
            max-width: 90%;
          }
        }
        .bank-large-wrap {
          width: 90%;
        }
        .channel-wrap {
          display: flex;
          align-items: center;
          .font-size-2;
          .icon-bg {
            margin-right: @space-2;
          }
        }
      }
      .payment-con-wrap {
        height: 100%;
        display: flex;
        align-items: flex-end;
        .selecter {
          z-index: 1;
          background-color: @color-white-2;
          border-radius: @radius-2;
          border: 1px solid rgba(29, 43, 61, 0.06);
          .ant-select-selection--single {
            height: 30px;
            background-color: initial;
            border: none;
            border-top: none;
            .ant-select-selection__rendered {
              display: flex;
              align-items: center;
              line-height: 30px;
            }
          }
          .dis-f {
            .font-size-2;
            .font-weight-3;
            color: @color-black-2;
          }
        }
      }
    }
    .payee-bank-wrap {
      flex-direction: column;
      margin: 0 !important;
      align-items: initial !important;
    }
    .payee-title-wrap {
      display: flex;
      width: 92%;
      align-items: center;
      .account-title {
        .font-size-2;
        z-index: 1;
        .font-weight-3;
        max-width: 75%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .account-large-title {
        max-width: 60%;
      }
      .account-default {
        background-color: @color-white-2;
        border-radius: @radius-2;
        padding: 0 @space-2;
      }
      .account-default-red {
        color: @color-error-2;
      }
      .account-default-blue {
        color: #378ed0;
      }
      .account-type,
      .account-shared{
        .font-weight-2;
        margin-left: @space-4;
        display: inline-block;
        padding: @space-1 @space-3;
        background: rgba(255, 255, 255, 0.1);
        border-radius: @radius-2;
      }
      .remark-item {
        z-index: 1;
      }
    }
    // .payee-large-title-wrap {
    //   width: 100%;
    // }
    .bank-large-no {
      .font-size-6;
    }
    .set-default {
      z-index: 1;
      cursor: pointer;
      padding: 0 5px;
      display: inline-flex;
      align-items: center;
      visibility: hidden;
      border-radius: @radius-2;
      flex-shrink: 0;
      background: rgba(255, 255, 255, 0.1);
      &:hover {
        background: rgba(255, 255, 255, 0.4);
        transition: all 0.3s;
      }
    }
    .set-collect {
      position: absolute;
      right: 0;
      top: 27px;
    }
  }
  .icon-bg {
    background-color: @color-white-1;
    border-radius: @radius-5;
    display: flex;
    align-items: center;
    justify-content: center;
    .disable-img {
      opacity: 0.3;
    }
  }
  .payee-account-content-wrap {
    margin: 0;
    .bank-wrap {
      .dis-f {
        margin-top: @space-3;
      }
    }
  }

  .flex-end {
    justify-content: flex-end !important;
  }
  .bg-img {
    right: 124px;
    top: -24px;
    > svg {
      transform: rotate(-15deg);
      height: 150px;
      width: 150px;
      color: rgba(255, 255, 255, 0.25);
    }
  }
}

.account-list-item-dropdown-option {
  display: flex;
  align-items: center;
  .font-size-2;
  color: @color-black-2;
}

.account-list-item-dropdown {
  .account-list-item-dropdown-option {
    display: flex;
    align-items: center;
    .font-size-2;
    color: @color-black-2;
  }
}

.payee-account-list-item-wrap {
  padding: @space-5 @space-6;
}

.payee-account-list-item-wrap,
.account-list-item-wrap {
  &:hover {
    .set-default {
      visibility: visible;
    }
  }
}

.account-list-item-wrap {
  &:hover {
    .set-default {
      visibility: visible;
    }
  }
}

.selected-item {
  //border-radius: @radius-4;
  //border: 6px solid rgba(185, 221, 255, 1);
  .account-selected-mask {
    display: flex;
    opacity: 1 !important;
    background: @color-black-3;
  }
}

.red-bg {
  background: linear-gradient(101deg, rgba(253, 117, 102, 1) 0%, rgba(252, 82, 98, 1) 100%);
}

.blue-bg {
  background: linear-gradient(101deg, rgba(55, 142, 208, 1) 0%, rgba(55, 100, 191, 1) 100%);
}

.green-bg {
  background: linear-gradient(100deg,#34cb4c 8%, #22ac38 93%);
}

.gray-bg {
  background: linear-gradient(101deg, rgba(165, 169, 170, 1) 0%, rgba(194, 198, 198, 1) 100%);
  cursor: not-allowed;
}

.mod-green-bg {
  background: linear-gradient(101deg,#14C9C9 8%, #3599BB 100%) !important;
}
