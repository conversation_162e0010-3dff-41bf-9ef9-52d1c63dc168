import React from "react";
import { Avatar } from "@hose/eui";
import { getAIAgent } from '../plugins/bills/bills.action'
import styles from './ai-agent-utils.module.less'
import { toJS, isObservableObject } from 'mobx'
import { cloneDeep } from 'lodash'
import classNames from 'classnames'

export const getAIAgentObj = (node: any, nodesAIAgentMap: any = {}) => {
  let agent
  let agentTitle
  const aiAgentId = node?.ebotConfig?.setting?.extras?.aiAgent || node?.ebotConfig?.setting?.aiAgent || node?.attributes?.extras?.aiAgent
  if (aiAgentId) {
    if (typeof aiAgentId === 'string' && aiAgentId in nodesAIAgentMap) {
      agent = nodesAIAgentMap[aiAgentId] || {
        name: i18n.get('匹配不到智能体'),
        icon: 'EBotIconNode'
      }
    } else if (typeof aiAgentId === 'object' && aiAgentId !== null) {
      agent = aiAgentId
    } else {
      agent = null
    }
  }

  if (agent) {
    agentTitle = <div className={styles['ai-agent-title']}>
      <Avatar size="mini" shape="circle" src={agent?.icon}>{agent?.name}</Avatar>
      <span>{agent?.name}</span>
    </div>
  } else {
    agentTitle = i18n.get('匹配不到智能体，将自动跳过')
  }

  return {
    agentTitle,
    agent,
    avatar: <Avatar size="large" shape="circle" src={agent?.icon}>{agent?.name}</Avatar>
  }
}

export const AIAgentTag:React.FC<{text:React.ReactNode, style?: React.CSSProperties}> = ({ text, style }) => {
  return <div className={classNames('ai-agent-label-tag',styles['ai-agent-label-tag'])} style={style}>
    <span className={classNames('ai-agent-label-tag-text',styles['ai-agent-label-tag-text'])}>{text}</span>
  </div>
}

export const getAIAgentLabelDom = (langName, isBold = false) => {
  return <span className={`${styles['ai-agent-label']} ${isBold && styles['bold']}`}>
    <span className={styles['ai-agent-label-name']}>{langName}</span>
    <AIAgentTag text='AI' />
  </span>
}

export const fetchNodesAIAgentMap = async (nodesAIAgentMap: any = {}, nodes: any = [], logs?: any) => {
  const map = { ...nodesAIAgentMap }
  for (const node of nodes) {
    const aiAgentId = node?.ebotConfig?.setting?.extras?.aiAgent || node?.ebotConfig?.setting?.aiAgent
    if (node.type === 'aiApproval' && aiAgentId && !map[aiAgentId]) {
      if (typeof aiAgentId === 'string') {
        const res = await getAIAgent({ id: aiAgentId })
        if (res.code === 200) {
          map[aiAgentId] = res.data
        }
      } else {
        map[aiAgentId?.id] = aiAgentId
      }
    }
  }
  if (Array.isArray(logs)) {
    for (const log of logs) {
      const aiAgentId = log?.attributes?.extras?.aiAgent
      if (log.attributes?.nodeType === 'AIAPPROVAL' && aiAgentId && !map[aiAgentId]) {
        const res = await getAIAgent({ id: aiAgentId })
        if (res.code === 200) {
          map[aiAgentId] = res.data
        }
      }
    }
  }
  return map
}

// 'reject' [-1]  (驳回)
// 'now'    [0]   (审批中)
// 'done'   [1]   (通过)
// ''       [2]   (未处理)
export const getFlowPlanFromLogs = (plan, logs) => {
  let isRetract = logs && logs.length > 0 && logs[logs.length - 1].action === 'freeflow.retract'
  if (isObservableObject(plan)) {
    plan = toJS(plan)
  }
  let flowPlan = cloneDeep(plan)
  let taskId = flowPlan?.taskId
  let prevId = flowPlan?.prevId
  flowPlan?.nodes?.reduce(
    (prev, next) => {
      if (prevId === 'SUBMIT' && taskId === next.id) {
        if (prev.stateEx === undefined) {
          prev.stateEx = 'done'
        }

        next.stateEx = 'now' // 审批中
        return next
      }

      if (taskId === 'SUBMIT' && prevId === prev.id) {
        prev.stateEx = isRetract ? 'retract' : 'reject' //驳回
        next.stateEx = ''
        return next
      }

      if (flowPlan.nodes.length === 1 && taskId === 'SUBMIT' && prevId === next.id) {
        //只有一个节点
        next.stateEx = isRetract ? 'retract' : 'reject'
        return next
      }

      if (prev.stateEx === 'now' || prev.stateEx === '') {
        next.stateEx = ''
        return next
      }

      if (next.id === prevId) {
        //最后一个节点
        if (taskId === null) {
          next.stateEx = 'done'
        } else if (taskId === 'SUBMIT') {
          next.stateEx = isRetract ? 'retract' : 'reject'
        }

        prev.stateEx = 'done'
        return next
      }

      if (prev.id === prevId && next.id === taskId) {
        prev.stateEx = 'done'
        next.stateEx = 'now'
        return next
      }

      if (
        (prev.skippedType === 'APPROVER_NOT_FOUND_BY_ROLE' ||
          prev.skippedType === 'APPROVER_SAME_AS_SUBMITTER_BY_ROLE') &&
        next.id === taskId
      ) {
        next.stateEx = 'now'
      }

      if (next.id === taskId) {
        prev.stateEx = 'done'
        next.stateEx = 'now'
      }

      prev.stateEx = 'done' //通过
      return next
    },
    { id: 'SUBMIT' }
  )
  return flowPlan
}

export const isAIAgentNode = (el: any) => {
  return el?.node?.type === 'aiApproval' || el?.attributes?.nodeType === "AIAPPROVAL"
}

export const getSkippedNodeStaff = (node) => {
  return node?.attributes?.nodeApproverIds?.map(item => ({
    avatar: item.avatar,
    name: item.name
  }))
}