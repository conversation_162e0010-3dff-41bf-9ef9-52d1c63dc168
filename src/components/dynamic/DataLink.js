/**
 *  Created by gym on 2018/5/25 下午12:56.
 */
import React, { PureComponent, Fragment } from 'react'
import { EnhanceField } from '@ekuaibao/template'
import { wrapper } from '../layout/FormWrapper'
import { required } from '../validator/validator'
import styles from './DataLink.module.less'
import { get, cloneDeep, isObject } from 'lodash'
import { isDisable, isAllowModifyFiled } from '../utils/fnDisableComponent'
import CarBusinessCard from '../../elements/dataLink-card/MyCarBusinessCard'
import { Button } from '@hose/eui'
import DataLinkCard from '../../elements/dataLink-card/DataLinkCard'
import { app as api } from '@ekuaibao/whispered'
const emitResetFieldsExternals = api.invokeServiceAsLazyValue('@bills:import:emitResetFieldsExternals')
import { checkPriority } from '../utils/fnFormartDatalinkData'
import { dimensionValueVisible } from '../utils/fnDimension'
import { showModal } from '@ekuaibao/show-util'
const fnCheckNeedPhone = api.invokeServiceAsLazyValue('@expansion-center:import:fnCheckNeedPhone')
import { EnhanceConnect } from '@ekuaibao/store'
import { getAssignmentRuleById } from '../../plugins/bills/bills.action'
const baseData = ['supplierReconciliation', 'supplierSettlement', 'supplier']
const supplierData = ['supplierReconciliation', 'supplierSettlement']
import { isTravelOrder } from './utils'
import { handleDependence, shouldUpdateValue } from '../../components/utils/DependenceUtil'
import { getDepedenceParam } from '../utils/DependenceUtil'
import { getTrueKey } from '../../lib/utils'
import { checkStaffDataRange } from '../utils/fnCheckStaffDataRange'
import { fieldDefaultValue } from '../utils/fnInitalValue'
import { getBoolVariation, useDepartmentVisible } from '../../lib/featbit'
import { datalinkHandleDenpence, useNewAutomaticAssignment } from '../utils/fnAutoDependence'
import { OutlinedTipsAdd } from '@hose/eui-icons'

@EnhanceConnect(state => ({
  remunerationBatchField: state['@remuneration'].remunerationBatchField,
  multiplePayeesMode: state['@bills'].multiplePayeesMode
}))
@EnhanceField({
  descriptor: {
    type: 'dataLink'
  },
  validator: (field, props) => (rule, value, callback) => {
    const { hide, field: field1 } = field
    const { remunerationBatchField } = props
    if (hide && field1 === remunerationBatchField) {
      return callback()
    }
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
export default class DataLink extends PureComponent {
  constructor(props) {
    super(props)
    let { field } = this.props
    let { dependence, dependenceCondition } = field
    let dependenceMap =
      dependence?.map(v => {
        const { direction, roleDefId } = v
        return { direction, roleDefId, dependenceId: '' }
      }) || []
    this.state = {
      field: field,
      islock: false,
      ledgerLockList: [],
      dependenceData: undefined,
      dependenceParams: getDepedenceParam({
        dependenceFeeType: false,
        dependence: field?.dependence,
        dataType: field?.dataType,
        recordSearch: dependenceMap,
        dependenceCondition
      })
    }
  }
  hasEmitHandleDependenceChange = false
  componentWillMount() {
    const { bus } = this.props
    bus.on('on:dependence:change', this.fnHandleDenpence)
    bus.on('on:autoAssignDataLink', this.autoAssignDataLink)
    bus.on('clear:autoAssignDataLink', this.clearAutoAssignDataLink)
    // bus.on('fee:detail:feetype:change', this.handleFeeDetailChange)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.value !== nextProps.value) {
      const { value, field, isPermitForm, onChange } = nextProps
      if (!get(nextProps, 'value.template')) {
        const type = get(field, 'referenceData.platformId.type', '')
        if (value && type !== 'PRIVATE_CAR') {
          this.getDataLinkTempById(typeof value === 'string' ? value : value.id)
        }
      }
      const { dependenceData } = this.state
      const { dependence, allowCancelDependence } = field || {}
      if (isPermitForm && dependence?.length && !allowCancelDependence) {
        const isExist = dependenceData?.data?.some(v => {
          return v?.dataLink?.id === value?.id
        })
        if (!isExist) {
          onChange && onChange(undefined)
        }
      }

      shouldUpdateValue.call(this, this.props, nextProps, true)
    }
  }

  componentDidMount() {
    const {
      bus,
      value,
      field,
      field: { referenceData, dependence, rangeOnlyOneAutomaticAssignment, filterId },
      onComponentLoadFinished
    } = this.props
    const { plannedIds } = referenceData
    bus.on('continue:add:detail', this.handleContinueAddDetail)
    bus.on('set:delegator', this.fnDelegatorChange)
    if (onComponentLoadFinished && !this.hasEmitHandleDependenceChange) {
      onComponentLoadFinished(field)
      this.hasEmitHandleDependenceChange = true
    }

    let type = get(referenceData, 'platformId.type')
    let id = ''
    if (value) {
      id = typeof value === 'string' ? value : value.id || get(value, 'data.dataLink.id')
    }
    if (value && type !== 'PRIVATE_CAR') {
      const assignment = !!bus.$_fromPlanQuery
      this.getDataLinkTempById(id, assignment)
    } else {
      if (plannedIds !== null) {
        api.invokeService('@third-party-manage:get:ledgerList:list', referenceData.id).then(res => {
          const lockList = res.items.filter(item => item.isLock === true)
          const lock = !!lockList.length
          if (lock !== this.state.islock) {
            this.setState({ islock: lock, ledgerLockList: lockList })
          }
        })
      }
    }
    if (!id && type !== 'PRIVATE_CAR' && !filterId && !dependence?.length && useNewAutomaticAssignment() && rangeOnlyOneAutomaticAssignment) {
      const params = {
        entityId: referenceData.id,
        type: 'TABLE',
        query: { limit: { start: 0, count: 2 } }
      }
      api.invokeService('@third-party-manage:search:datalink:by:entityId', params).then(res => {
        const data = res?.items?.data
        if (data?.length === 1) {
          bus.setValidateLevel(1)
          this.getDataLinkTempById(data[0]?.dataLink?.id, undefined)
        }
      })
    }
    setTimeout(() => bus.emit('on:dependence:change', { key: field.name, id }, { isInit: true }), 200)
  }

  fnDelegatorChange = () => {
    const { onChange, value } = this.props
    if (!value) {
      return
    }
    onChange && onChange(undefined)
  }

  getIsCurrentDataLinkField = obj => {
    const { field } = obj
    if (!field?.field) return false
    return field?.field === this.props?.field?.field
  }

  // 业务对象字段唯一值自动赋值
  autoAssignDataLink = obj => {
    const isCurrentField = this.getIsCurrentDataLinkField(obj)
    if (isCurrentField && obj?.id) {
      this.getDataLinkTempById(obj.id, true)
    }
  }

  // 清空指定业务对象字段值
  clearAutoAssignDataLink = obj => {
    const isCurrentField = this.getIsCurrentDataLinkField(obj)
    if (isCurrentField) {
      this.handleClearRelation()
    }
  }

  getDataLinkTempById = (id, assignment = true, key) => {
    const { onChange } = this.props
    if (!id) {
      onChange && onChange()
      return
    }
    api.invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' }).then(async res => {
      if (assignment) {
        // 第一次进入的时候不启动联动赋值，因为如果表单上面有值，会被联动赋值覆盖
        this.setFormValue(res.value.data)
      }
      let newValue = { ...res.value, id: id }
      if (key) {
        // 只有传 key 才是档案关系埋点
        // TODO: 档案关系埋点
        let { billData, billSpecification, feeType, dataSource, value, field } = this.props
        const oldValue = value
        let newBillData = billData
        let message = '单据上的档案关系赋值'
        if (feeType) {
          message = '明细上的档案关系赋值'
        } else {
          newBillData = dataSource
        }
        api?.logger?.info(message, {
          specificationId: billSpecification?.id,
          specificationName: billSpecification?.name,
          flowId: newBillData?.flowId || newBillData?.id,
          code: newBillData?.code || newBillData?.form?.code,
          sceneName: '档案关系值变化',
          feeTypeId: feeType?.id,
          feeTypeName: feeType?.name,
          field: field?.field,
          dependField: key,
          oldValue,
          newValue
        })
      }
      onChange && onChange(newValue)
    })
  }

  fnHandleDenpence = async ({ id, key }, options = {}) => {
    if (useNewAutomaticAssignment()) {
      const result = await datalinkHandleDenpence({ id, key }, options, this.getDataLinkTempById, this.state, this.props)
      result && this.setState(result)
      return
    }
    const { onChange, field, bus, value, billState, isDetail, detailId, isModify, currentNode } = this.props
    // 审批中修改时不允许修改档案关系，如果是新建费用，允许修改
    const modifyFlowUseDepence = isModify && isDetail && !detailId ? false : isModify
    if (modifyFlowUseDepence && getBoolVariation('cyxq-71195-modify-datalink-assignment') && !isAllowModifyFiled(currentNode, field?.name)) {
      return
    }

    const { dependence, dependenceCondition, allowCancelDependence } = field
    // 非单据新建及费类新建（费类有detailId 则为编辑）
    const isInitLoad = ((billState !== 'new' && Boolean(!isDetail)) || Boolean(detailId)) && options?.isInit // 初始化加载出来的数据
    const list = this.state.dependenceParams?.recordSearch?.map((v, i) => {
      // dependenceId 这个key在字段信息里表示的依赖字段name,但是在参数里表示的是依赖的具体字段的value.id
      const dependenceId = dependence[i]?.dependenceId
      if (dependenceId === key) {
        v.dependenceId = id
      }
      return v
    })
    const dependenceParams = {
      key,
      dataType: field.dataType,
      id,
      dependenceFeeType: false,
      dependence: field.dependence,
      onChange,
      recordSearch: list,
      dependenceCondition
    }
    const result = await handleDependence(dependenceParams)
    if (result) {
      let { billData, billSpecification, feeType, dataSource } = this.props
      const oldValue = value
      let newBillData = billData
      let message = '单据上的档案关系赋值'
      if (feeType) {
        message = '明细上的档案关系赋值'
      } else {
        newBillData = dataSource
      }
      const { matchDefaultValue, leafItems, ...others } = result
      // 初始化加载出来的数据，并且已经有值了，就不触发联动赋值
      if (isInitLoad && value && value.id && !others?.data?.length && !isModify) {
        return
      } else if (!matchDefaultValue) {
        const { data = [] } = others
        const id = value && value.id
        const item = data.find(v => v?.dataLink?.id === id)
        const res = data && data.length > 1 ? {} : data[0]
        const selectItem = item ? item : res
        if (!item && selectItem?.dataLink?.id) {
          // 要不要重新赋值
          //只有当前选择的值改变的时候才调用联动赋值
          this.getDataLinkTempById(selectItem?.dataLink?.id, undefined, key)
        } else if (!allowCancelDependence && !item) {
          // 要不要清空已经存在的值：返回值中没有已经存在的值且不允许取消依赖关系
          onChange()
        }
        bus.setValidateLevel(1)
        this.setState({
          dependenceData: others,
          dependenceParams: getDepedenceParam(dependenceParams)
        })
      }
      api?.logger?.info(message, {
        specificationId: billSpecification?.id,
        specificationName: billSpecification?.name,
        flowId: newBillData?.flowId || newBillData?.id,
        code: newBillData?.code || newBillData?.form?.code,
        sceneName: '档案关系变化',
        feeTypeId: feeType?.id,
        feeTypeName: feeType?.name,
        field: field?.field,
        dependField: key,
        oldValue,
        newValue: result?.data
      })
    }
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('continue:add:detail', this.handleContinueAddDetail)
    bus.un('set:delegator', this.fnDelegatorChange)
    bus.un('on:dependence:change', this.fnHandleDenpence)
    bus.un('on:autoAssignDataLink', this.autoAssignDataLink)
    bus.un('clear:autoAssignDataLink', this.clearAutoAssignDataLink)
    // bus.un('fee:detail:feetype:change', this.handleFeeDetailChange)
  }

  // handleFeeDetailChange = (feeType, { template }) => {
  //   const { value, field } = this.props
  //   const hasField = template?.find(v => v?.field === field?.field)
  //   if (value?.id && !!hasField) {
  //     this.setFormValue(value)
  //   }
  // }

  handleContinueAddDetail = () => {
    const { onChange } = this.props
    onChange(undefined)
  }

  handleRenewSelect = () => {
    this.fetchDataLink()
  }

  fetchDataLink = () => {
    const {
      bus,
      flowId,
      external,
      value,
      field: { allMatchList }
    } = this.props
    const {
      field: { referenceData, label, filterId, dependence, allowCancelDependence, field },
      islock,
      ledgerLockList,
      dependenceParams
    } = this.state
    const groupType = get(referenceData, 'platformId.groupType')
    const type = get(referenceData, 'platformId.type')
    fnCheckNeedPhone()({ groupType, type }).then(res => {
      if (res) {
        return api.open('@aikeCRM:RegisterPhoneModal', { type }).then(() => {
          showModal.info({
            title: i18n.get('数据同步中'),
            content: i18n.get('数据正在同步中，请稍候再试。'),
            okText: i18n.get('确定')
          })
        })
      }
      // // 是否是订单
      // if (referenceData.type === 'referenceData' && type === 'TRAVEL_MANAGEMENT') {
      //   const limitTripOrders = fnCheckLimitTripOrder(billSpecification)
      // }
      bus
        .invoke('element:select:dataLink', {
          referenceData,
          flowId,
          selectedEntity: value,
          islock,
          ledgerLockList,
          filterId,
          dataLink: { id: referenceData.id, selectedEntity: value, type, name: label },
          dependenceParams: dependence && dependence.length ? dependenceParams : undefined,
          allowCancelDependence: allowCancelDependence,
          allMatchList,
          field
        })
        .then(result => {
          const { data } = result
          this.getDataLinkTempById(data.dataLink.id)
          this.setTripBasedata(data, type)
          emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
        })
    })
  }

  // 将明细中的坐席赋值
  setTripBasedata = (data, type) => {
    let { isDetail, form } = this.props
    let tripType = get(data, 'dataLink.entity.type')
    let id = get(data, 'dataLink.entity.parentId')
    if (isDetail && type == 'TRAVEL_MANAGEMENT' && id && (tripType == 'FLIGHT' || tripType == 'TRAIN')) {
      let name = this.getTemplateTrip(tripType)
      let value = this.getDataLinkValue(data, id, tripType)
      if (name && value) {
        let obj = {}
        obj[name] = value
        form && form.setFieldsValue(obj)
      }
    }
  }
  getDataLinkValue = (data, id, tripType) => {
    let value = ''
    // @i18n-ignore
    const F = {
      经济舱: 'ECONOMY',
      商务舱: 'BUSINESS',
      头等舱: 'FIRST'
    }
    // @i18n-ignore
    const T = {
      硬座: 'YZ',
      软座: 'RZ',
      硬卧: 'YW',
      软卧: 'RW',
      高级软卧: 'GJRW',
      一等座: 'YD',
      二等座: 'ED',
      商务座: 'SW',
      高铁动卧: 'DW'
    }
    if (tripType == 'FLIGHT') {
      value = get(data, `dataLink.E_${id}_航班舱型`)
      value = value ? F[value] : ''
    } else if (tripType == 'TRAIN') {
      value = get(data, `dataLink.E_${id}_火车坐席`) || get(data, `dataLink.E_${id}_火车席位`)
      value = value ? T[value] : ''
    }
    return value
  }
  getTemplateTrip = type => {
    let { template = [] } = this.props
    const Map = {
      TRAIN: 'ref:basedata.Enum.TrainSeatType',
      FLIGHT: 'ref:basedata.Enum.CabinType'
    }
    let name = ''
    template &&
      template.forEach(i => {
        if (i && i.type && i.type == Map[type]) {
          name = i.name
        }
      })
    return name
  }

  setFormValue = data => {
    const {
      field: { assignmentRule = {}, isLinkageAssignment, referenceData }
    } = this.state
    if (isLinkageAssignment) {
      if (referenceData.id !== data.dataLink.entityId) {
        // 获取子类业务对象赋值规则，子类业务对象只有一个赋值规则
        api.dispatch(getAssignmentRuleById([data.dataLink.entityId])).then(result => {
          const ruleList = result.items
          this.fnUpdateData(data, ruleList[0] || assignmentRule)
        })
      } else {
        this.fnUpdateData(data, assignmentRule)
      }
    }
  }

  fnUpdateData = async (data, assignmentRule) => {
    const { bus, template, form, submitterId } = this.props
    const { fields = [] } = assignmentRule
    const {
      field: { referenceData }
    } = this.state
    let valueMap = {}
    const depFields = []
    const staffFields = []
    fields.forEach(item => {
      template.forEach(v => {
        if (item.targetField === v.field && checkPriority(v)) {
          let value = data.dataLink[item.sourceField]
          if (value !== undefined) {
            if (v.type === 'number') {
              valueMap[v.name] = `${value * 1}`
            } else if (v.type.startsWith('ref:basedata.Dimension')) {
              if (!!value) {
                value.needCheckValue = true
              }
              valueMap[v.name] = dimensionValueVisible(value, submitterId) ? value : undefined
            } else if (v.type === 'dataLink') {
              const id = get(value, 'data.dataLink.id')
              // fix bug：XSG-12499
              // 如果业务对象存在联动赋值的情况
              if (id && v.isLinkageAssignment) {
                // 业务对象关联带出相关业务对象的数据填充到表单
                api
                  .invokeService('@bills:get:datalink:template:byId', { entityId: id, type: 'CARD' })
                  .then(async res => {
                    const resData = res.value.data
                    if (referenceData.id !== resData.dataLink.entityId) {
                      // fix bug: XSG-13696
                      // 获取子类业务对象赋值规则，
                      const rule = v.assignmentRule
                      if (rule) {
                        this.fnUpdateData(resData, rule)
                      } else {
                        api.dispatch(getAssignmentRuleById([resData.dataLink.entityId])).then(result => {
                          const ruleList = result.items
                          // 当业务对象有赋值规则的时候再进行子业务对象联动赋值
                          if (ruleList && ruleList.length > 0) {
                            this.fnUpdateData(resData, ruleList[0])
                          }
                        })
                      }
                    }
                  })
              }
              valueMap[v.name] = { ...value, id }
            } else if (v.type === 'payeeInfo') {
              const payeeValue = form.getFieldValue('payeeId') //单据收款信息为多收款人时不赋值，明细上的收款信息要赋值
              valueMap[v.name] = payeeValue && payeeValue.multiplePayeesMode ? payeeValue : value
            } else if (v.type === 'text' && value?.location) {
              valueMap[v.name] = value?.name || value?.address
            } else if (v.type === 'ref:organization.Staff' || v.type === 'list:ref:organization.Staff') {
              staffFields.push(v)
              const rangeValue = checkStaffDataRange(v, value)
              if (Array.isArray(rangeValue)) {
                if (rangeValue.length) {
                  valueMap[v.name] = rangeValue
                }
              } else if (!!rangeValue) {
                valueMap[v.name] = rangeValue
              }
            } else if (v.type === 'ref:organization.Department') {
              depFields.push(v)
              valueMap[v.name] = cloneDeep(value)
            } else {
              valueMap[v.name] = cloneDeep(value)
            }
          }

          if (value === undefined && v.editable === false && v.defaultValue && v.defaultValue.type === 'none') {
            valueMap[v.name] = value
          }
        }
      })
    })
    // for (let key in valueMap) {
    //   if (typeof valueMap[key] === 'undefined') {
    //     delete valueMap[key]
    //   }
    // }
    for (let key in valueMap) {
      if (valueMap[key] === null) {
        valueMap[key] = undefined
      }
    }
    // TODO: 业务对象联动赋值埋点
    let { billData, billSpecification, feeType, id, dataSource } = this.props
    if (billData) {
      // 说明在费用明细里
      api?.logger?.info('业务对象联动赋值', {
        specificationId: billSpecification?.id,
        specificationName: billSpecification?.name,
        flowId: billData?.flowId,
        code: billData?.code,
        sceneName: '费用上业务对象联动赋值',
        feeTypeId: feeType?.id,
        feeTypeName: feeType?.name,
        datalinkField: id,
        dataLinkId: data?.dataLink?.id,
        operateType: '添加',
        resultForm: valueMap
      })
    } else {
      bus?.getFieldsValue()?.then(value => {
        api?.logger?.info('业务对象联动赋值', {
          specificationId: billSpecification?.id,
          specificationName: billSpecification?.name,
          flowId: dataSource?.id,
          code: dataSource?.form?.code,
          sceneName: '单据上业务对象联动赋值',
          feeTypeId: undefined,
          feeTypeName: undefined,
          datalinkField: id,
          dataLinkId: data?.dataLink?.id,
          operateType: '添加',
          resultForm: valueMap
        })
      })
    }
    if (useDepartmentVisible()) {
      for (const v of depFields) {
        const isCanSelect = await bus.invoke(`department:check:${v.name}:value`, valueMap[v.name])
        if (!isCanSelect) {
          delete valueMap[v.name]
        }
      }
      const [configVisibilityStaffs, contactConfig] = await Promise.all([
        api.dataLoader('@contactManagement.configVisibilityStaffs').load(),
        api.dataLoader('@contactManagement.contactConfig').load()
      ])
      if (!configVisibilityStaffs.fullVisible && contactConfig?.filterScope !== 'UNLIMITED') {
        const visibleStaffIdsSet = new Set(configVisibilityStaffs.staffIds)
        for (const v of staffFields) {
          if (Array.isArray(valueMap[v.name])) {
            const newValue = valueMap[v.name].filter(staff => visibleStaffIdsSet.has(staff.id))
            newValue.length ? (valueMap[v.name] = newValue) : delete valueMap[v.name]
          } else if (isObject(valueMap[v.name]) && !visibleStaffIdsSet.has(valueMap[v.name].id)) {
            delete valueMap[v.name]
          }
        }
      }
    }
    !!form && form.setFieldsValue(valueMap)
    if (valueMap.loanMoney) {
      bus.emit('loanMoney:changed', valueMap.loanMoney)
    }
  }
  handleClearRelation = () => {
    const { onChange, external, value } = this.props
    if (value) {
      onChange && onChange(undefined)
      emitResetFieldsExternals.value && emitResetFieldsExternals.value(external)
    }
  }

  //手动点击'清空关联' 时增加一步 联动清空操作（只清一级字段，嵌套业务对象不处理）
  handleManualClearRelation = () => {
    this.handleClearRelation()
    this.fnCleanAssignData()
  }
  fnCleanAssignData = () => {
    const { bus, template, form } = this.props
    const {
      field: { assignmentRule = {}, isLinkageAssignment }
    } = this.state
    const { fields = [], clearAssignWhenManualDelete } = assignmentRule
    if (isLinkageAssignment && clearAssignWhenManualDelete) {
      let valueMap = {}
      fields.forEach(item => {
        template.forEach(v => {
          if (item.targetField === v.field && checkPriority(v)) {
            valueMap[v.name] = fieldDefaultValue(v, undefined)
          }
        })
      })
      if (Object.keys(valueMap).length) {
        !!form && form.setFieldsValue(valueMap)
      }
      if (valueMap.loanMoney) {
        bus.emit('loanMoney:changed', valueMap.loanMoney)
      }
    }
  }
  hanldeOnClick = () => {
    let { value, field, disabledStaff = false } = this.props
    const isOrder = isTravelOrder(field)
    const dataLink = get(value, 'data.dataLink')
    const tripType = get(dataLink, 'entity.type', '')
    const { ability = '', name = '' } = field
    if (ability === 'contractSettle') {
      disabledStaff = true
    }
    const trueKey = getTrueKey(name)
    if (trueKey === 'relationContract') {
      disabledStaff = true
    }
    if (value) {
      const id = value.id || get(value, 'data.dataLink.id')
      if (isOrder) {
        api.open('@bills:TripOrderPopup', {
          title: i18n.get('订单详情'),
          entityInfo: { ...value.data },
          tripType: tripType
        })
      } else {
        api.open('@bills:DataLinkDetailModal', {
          entityInfo: { dataLink: { id }, entityId: field.referenceData },
          field,
          disabledStaff,
          showClose: true
        })
      }
    }
  }

  renderDataLink = () => {
    let { field, value, external, billSpecification } = this.props
    const isReconciliation = supplierData.indexOf(get(field, 'name')) !== -1
    let { placeholder, optional } = field
    placeholder = optional ? i18n.get('(选填)') + placeholder : placeholder
    const billType = get(billSpecification, 'type')
    const disabled = isDisable(this.props)
    //
    const hasChange = baseData.indexOf(get(field, 'name')) === -1 || billType === 'payment'
    return (
      <Fragment>
        {value ? (
          <DataLinkCard
            field={field}
            entityInfo={value}
            external={external}
            isReconciliation={isReconciliation}
            isReaonly={disabled}
            hasChange={hasChange}
            onClick={this.hanldeOnClick}
            onRenewSelet={this.handleRenewSelect}
            onClearRelation={this.handleManualClearRelation}
          />
        ) : (
          <div className="datalink-select">
            <Button onClick={this.handleRenewSelect} data-testid="field-dataLink-select" size="small" category="secondary" disabled={disabled} icon={<OutlinedTipsAdd />}>
              {i18n.get('选择')}
            </Button>
          </div>
        )}
      </Fragment>
    )
  }

  render() {
    const { value, field, isReaonly } = this.props
    let type = get(field, 'referenceData.platformId.type')
    const showCard = type === 'PRIVATE_CAR' && value
    if (type === 'PRIVATE_CAR') {
      field.label = i18n.get('行车记录')
    }
    if (showCard) {
      return (
        <div className={styles['dataLink-input-wrapper']}>
          <CarBusinessCard {...this.props} />
          {!isReaonly ? (
            <div className="card-bottom">
              <span onClick={this.handleRenewSelect}>{i18n.get('重新选择')}</span>
              <span onClick={this.handleManualClearRelation}>{i18n.get('清空关联')}</span>
            </div>
          ) : null}
        </div>
      )
    }
    return <div className={styles['dataLink-input-wrapper']}>{this.renderDataLink()}</div>
  }
}
