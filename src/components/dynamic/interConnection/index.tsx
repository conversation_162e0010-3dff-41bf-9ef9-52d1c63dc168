// @i18n-ignore-all
import React, { PureComponent } from 'react'
import { <PERSON>hanceField } from '@ekuaibao/template'
import { Select, Spin, Icon, TreeSelect, Divider } from 'antd'
import styles from './index.module.less'
const { Option } = Select
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import MessageCenter from '@ekuaibao/messagecenter'
import { cloneDeep, get, debounce } from 'lodash'
import { wrapper } from '../../layout/FormWrapper'
const ViewBox = api.require('@inter-connection/view-edit/temp/index')
import { required } from '../../validator/validator'
import { billTypeMap } from '@ekuaibao/lib/lib/enums'
const { TreeNode } = TreeSelect
interface Props {
  [key: string]: any
}
interface State {
  [key: string]: any
}

@EnhanceField({
  descriptor: {
    test(field) {
      let { type, referenceData } = field
      return type === 'engineConnect'
    }
  },
  validator: (field, props) => (rule, value, callback) => {
    if (rule.level > 0) {
      return callback()
    }
    callback(required(field, value))
  },
  wrapper: wrapper()
})
@EnhanceConnect(state => ({
  baseDataProperties: state['@common'].globalFields.data,
  specificationGroupsList: state['@custom-specification'].specificationGroupsList,
  userInfo: state['@common'].userinfo.data,
  requisitionInfo: state['@bills'].requisitionInfo,
  fieldMap: state['@common'].fieldMap,
  IngineConnect: state['@common'].powers.IngineConnect
}))
export default class interConnection extends PureComponent<Props, State> {
  bus: any = new MessageCenter()
  lastFetchId = 0
  constructor(props) {
    super(props)
    let id = (get(props, 'field.dataType.elemType.entity') || '').replace('engine.Connect.', '')
    let mode = this.props?.field?.importMode == 'MULTIPLE'
    this.state = {
      mode: mode || false,
      interId: id,
      data: [],
      values: this.props?.value || [],
      valuearr: [],
      fetching: false,
      master: {},
      baseInfo: null
    }
    this.fetchDataLink = debounce(this.fetchDataLink, 400)
  }

  componentDidMount() {
    let { interId } = this.state
    let { IngineConnect, value } = this.props
    if(!interId){
      return 
    }
    if (!IngineConnect && (value?.length == 0 || !value)) {
      return 
    }
    this.getTemplate()
    this.fetchDataLink('')
    this.getData(this.props?.value || [])
  }
  getTemplate = () => {
    let { interId } = this.state
    api.invokeService('@inter-connection:get:template:by:id', interId).then((res: any) => {
      if (res?.value) {
        this.setState({
          master: res.value
        })
      }
    })
  }

  getData = (ids: any[]) => {
    if (ids.length == 0) {
      return
    }
    let { interId } = this.state
    api.invokeService('@inter-connection:get:template:data', { id: interId, ids: ids.join(',') }).then((res: any) => {
      const valuearr = res?.items || []
      this.setState({ valuearr, data: valuearr, fetching: false })
    })
  }

  handleOnChange = value => {
    const { onChange } = this.props
    onChange && onChange(value)
  }
  handleRemove = index => {}

  fetchDataLink = val => {
    let { interId } = this.state
    this.lastFetchId += 1
    const fetchId = this.lastFetchId
    this.setState({ fetching: true })
    api.invokeService('@inter-connection:get:template:data', { id: interId, keyWord: val }).then((res: any) => {
      if (fetchId !== this.lastFetchId) {
        return
      }
      const data = res?.items || []
      this.setState({ data, fetching: false })
    })
  }
  handleBlur=()=>{
    this.fetchDataLink('')
  }
  handleDelete = (i: any, index: number) => {
    let u = [...this.state.valuearr]
    u.splice(index, 1)
    let arr = u.map(i => i.id.toString())
    this.setState({
      values: arr,
      valuearr: u
    })
    this.handleOnChange(arr)
  }
  handleDeselect = (v: any) => {
    let u = cloneDeep(this.state.valuearr).filter(i => i.id !== v)
    let arr = u.map(i => i.id.toString())
    this.setState(
      {
        valuearr: u,
        values: arr
      },
      () => {
        this.handleOnChange(arr)
      }
    )
  }
  getPlaceholder = () => {
    let text = '请输入名称或id'
    let { dateType, searchCode, searchName } = this.state?.master?.config || {}

    if (dateType == 'DATALINK') {
      text = '请输入业务对象的' 
      text+= (searchCode && searchName) ? '名称或编码' : searchName ? '名称' : '编码'
    }
    if (dateType == "FLOW") {
      text = '请输入单据的' 
      text+= (searchCode && searchName) ? '名称或单号' : searchName ? '名称' : '单号'
    }
    return i18n.get(text)
  }
  renderSelect = () => {
    const { fetching, data, mode, master, values } = this.state
    let obj = {}
    let dateType = master?.config?.dateType
    if (mode) {
      obj.mode = 'multiple'
    }
    return (
      <div className="search">
        <Select
          showSearch
          {...obj}
          value={values}
          placeholder={this.getPlaceholder()}
          notFoundContent={fetching ? <Spin size="small" /> : null}
          filterOption={false}
          onSearch={this.fetchDataLink}
          onChange={this.handleChange}
          onDeselect={this.handleDeselect}
          style={{ width: '100%' }}
          onBlur={this.handleBlur}
          showArrow={false}
        >
          {data.map(d => (
            <Option key={d.id}>{d.name}{d.code ? `(${d.code})`:''}</Option>
          ))}
        </Select>
        <Icon type="search" />

        {/* // suffixIcon={()=><Icon type="down" />}
          // showArrow={false}
          //disabled={value.find(i => i == d.id)} */}
      </div>
    )
  }

  handleClick = (i: any, index: number) => {
    let dateType = this.state?.master?.config?.dateType
    let dleType = this.props?.dleType
    if (dateType == 'FLOW' && i?.id && dleType != 'detail') {

      if (api.require<any>('@lib/featbit').supportBillDetailsSwitchingInDrawer()) {
        api.open('@bills:BillInfoDrawerV2', {
          currentId: i.id,
          flows: [],
          showUpDown: false,
          scene: 'OWNER',
          forceMode: 'readonly',
          buttons: [
            {
              action: 'freeflow.export',
              name: i18n.get('导出全部附件')
            },
            {
              action: 'freeflow.print',
              name: i18n.get('打印单据')
            },
            {
              action: 'freeflow.comment',
              name: i18n.get('评论')
            },
          ]
        })
        return
      }

      api.invokeService('@bills:get:flow-info', { id: i.id }).then(resp => {
        this.__billInfoPopup(resp.value)
      })
    } else if (dateType == 'DATALINK' && i?.id) {
      api.invokeService('@bills:get:datalink:template:byId', { entityId: i?.id, type: 'CARD' }).then(res => {
        let u = res.value?.data || {}
        u.id = i?.id
        this.getPlatform(u)
      })
    }
  }
  getPlatform = u => {
    let platformName = u?.dataLink?.entity?.name
    api.open('@bills:DataLinkDetailModal', {
      entityInfo: { ...u, platformName },
      showClose: true,
      viewKey: 'DataLinkDetailModal'
    })
    return
  }
  __billInfoPopup = flow => {
    let title = `${i18n.get(billTypeMap()[flow.formType])}${i18n.get('详情')}`
    const params = {
      title,
      invokeService: '@bills:get:flow-info',
      params: { id: flow.id },
      backlog: { id: -1, flowId: flow },
      reload: () => {}
    }
    api.open('@bills:BillInfoPopup', params, true)
  }
  handleChange = value => {
    let { mode } = this.state
    let v = this.state.value || []
    let nv = Array.isArray(value) ? value : [value]
    let d = this.state.valuearr.concat(this.state.data)
    let newv = nv
    if (mode) {
      newv = Array.from(new Set([...v, ...nv]))
    }
    newv = newv.map((i: any) => {
      return d.find((u: any) => u.id == i)
    })

    this.setState({
      values: newv.map(i => i.id),
      valuearr: newv,
      fetching: false
    })
    this.handleOnChange(newv.map(i => i.id.toString()))
  }
  renderList = () => {
    let { valuearr, master, mode } = this.state
    let data = []
    let { meta = {}, mappings } = master
    if (!mappings || mappings.length == 0 || !meta) {
      return null
    }
    if (valuearr?.length > 0) {
      data = mode ? valuearr : [valuearr[0]]
    }
    return (
      <div className="inter-connection-list">
        <ViewBox
          master={meta?.template || meta}
          temp={master}
          data={data}
          platform="pc"
          handleDelete={this.handleDelete}
          handleClick={this.handleClick}
        />
      </div>
    )
  }
  render() {
    let { IngineConnect, value } = this.props
    if (!IngineConnect && (value?.length == 0 || !value)) {
      return <p>{i18n.get('暂无权限')}</p>
    }
    let { interId } = this.state
    if(!interId){
      return (
        <p>{i18n.get('无法解析')}</p>
      )
    }
    return (
      <div className={styles['inter-connection-box']}>
        {this.renderSelect()}
        {this.renderList()}
      </div>
    )
  }
}
