import React, { Component } from 'react'
import { Form } from 'antd'
import { Table, Popover, Space, Button, Alert, InputNumber } from '@hose/eui'
import { FormComponentProps } from 'antd/es/form'
import { EnhanceConnect } from '@ekuaibao/store'
import styles from './DataLinkEditTable.module.less'
import { includes, isEqual, cloneDeep, debounce } from 'lodash'
import { resetDataLinEdits, formatCellValue } from './utils'
import { FilledTipsWarning } from '@hose/eui-icons'
import EKBIcon from '../../../../elements/ekbIcon'
import { ENUM_TYPES } from '../../../consts'
import DataLinksCell from './DataLinksCell'
import RefCell from './RefCell'
import AttachmentsCell from './AttachmentsCell'
import SimpleCell from './SimpleCell'
import CityCell from './CityCell'
import SwitchCell from './SwitchCell'
import MoneyCell from './MoneyCell'
import DateRangeCell from './DateRangeCell'
import DateCell from './DateCell'
import ListRefOrganizationStaffCell from './ListRefOrganizationStaffCell'
import RefOrganizationStaffCell from './RefOrganizationStaffCell'
import PayeeInfoCell from './PayeeInfoCell'
import EnumCell from './EnumCell'
import SimpleEditCell from './SimpleEditCell'
import NumberEditCell from './NumberEditCell'
import SwitchEditCell from './SwitchEditCell'
import DateEditCell from './DateEditCell'
import DateRangeEditCell from './DateRangeEditCell'
import EnumEditCell from './EnumEditCell'
import CityEditCell from './CityEditCell'
import PayeeInfoEditCell from './PayeeInfoEditCell'
import RefOrganizationStaffEditCell from './RefOrganizationStaffEditCell'
import ListRefOrganizationStaffEditCell from './ListRefOrganizationStaffEditCell'
import RefDepartmentEditCell from './RefDepartmentEditCell'
import RefEditCell from './RefEditCell'
import DataLinkEditCell from './DataLinkEditCell'
import DataLinkListEditCell from './DataLinkListEditCell'
import MoneyEditCell from './MoneyEditCell'
import AttachmentsEditCell from './AttachmentsEditCell'
import NumberCell from './NumberCell'
import MessageCenter from '@ekuaibao/messagecenter'
import { app as api } from '@ekuaibao/whispered'
import {
  OutlinedEditBatchYes,
  OutlinedTipsAdd,
  OutlinedDirectionDownload,
  OutlinedDirectionUpload,
  OutlinedDirectionWindowMini,
  OutlinedDirectionWindowMax
} from '@hose/eui-icons'
import { Fetch } from '@ekuaibao/fetch'
import { getLinkNodeElement, triggerClick } from '@ekuaibao/sdk-bridge/sdk/utils'
import moment from 'moment'
import { realName } from '../../../../elements/util'
import { getBoolVariation } from '../../../../lib/featbit'
import { Summary, AlertApportionError, getNumValue, formater } from './EditTableWrapper'
interface IProps extends FormComponentProps {
  isEdit?: boolean
  field?: any
  dataSource?: any[]
  onSelect?: () => void
  onGetData?: (data: any, index?: number | string) => void
  onFieldEdit?: (data: any, index: any) => void
  editing?: boolean
  authStaffStaffMap?: any
  bus?: any
  flowBus?: any
  columns?: any[]
  components?: any[]
  noTransColumns?: boolean
  canCreate?: boolean
  rowSelection?: {
    type?: string
    onChange?: (selectedRowKeySet: Set<number>, selectedRows: any[]) => void
  }
  type?: 'settlementChecking' | undefined
  entityId: string
  isHabSetValue?: boolean
  pagination?: any
  onJumpLastPage?: (total: number) => void
  onRowClick?: any
  record?: any
  dataIndex?: string
  title?: string
  onChange?: () => void
  flowFormFieldsValue?: any
  onApportionFormChange?: (data: any) => void
  apportionConfigs?: {
    field: string;
    label: string;
    type: string;
    limitConfig?: {
      field?: string;
      operator?: string
    }
  }[]
}

interface IState {
  dataSource?: any[]
  needCalculateUpdateFields: Array<string> | null
  selectedRowKeySet: Set<number>
  hoveredRowIndex?: number
  debouncedFunctions?: Map<number, any>
  deletePopoverVisible: boolean
  deleteTargetIndex?: number
  isFullScreen: boolean
  loading: boolean
  apportionErrors?: string[]
  apportionForm?: any
}

class EditableCell extends Component<IProps> {
  renderCellItem = props => {
    const {
      field: { type }
    } = props
    if (type === 'textarea' || type === 'text') {
      return <SimpleEditCell {...props} />
    } else if (type === 'number') {
      return <NumberEditCell {...props} />
    } else if (type === 'switcher') {
      return <SwitchEditCell {...props} />
    } else if (type === 'date') {
      return <DateEditCell {...props} />
    } else if (type === 'dateRange') {
      return <DateRangeEditCell {...props} />
    } else if (includes(ENUM_TYPES, type) || type?.startsWith('basedata.Enum')) {
      return <EnumEditCell {...props} />
    } else if (type === 'city') {
      return <CityEditCell {...props} />
    } else if (type === 'payeeInfo') {
      return <PayeeInfoEditCell {...props} />
    } else if (type === 'ref:organization.Staff') {
      return <RefOrganizationStaffEditCell {...props} />
    } else if (type === 'list:ref:organization.Staff') {
      return <ListRefOrganizationStaffEditCell {...props} />
    } else if (type === 'ref:organization.Department') {
      return <RefDepartmentEditCell {...props} useEUI />
    } else if (
      type.startsWith('ref') &&
      type !== 'ref:organization.Staff' &&
      type !== 'ref:organization.Department' &&
      type !== 'ref:basedata.Enum.currency' &&
      !includes(ENUM_TYPES, type)
    ) {
      return <RefEditCell {...props} />
    } else if (type === 'dataLink') {
      return <DataLinkEditCell {...props} />
    } else if (type === 'dataLinks') {
      return <DataLinkListEditCell {...props} />
    } else if (type === 'money') {
      return <MoneyEditCell {...props} />
    } else if (type === 'attachments' || type === 'aiAttachments') {
      return <AttachmentsEditCell {...props} />
    } else {
      return '未知类型'
    }
  }


  render() {
    const {
      editing,
      children,
      isEdit,
      field,
      dataSource,
      onSelect,
      onGetData,
      onFieldEdit,
      authStaffStaffMap,
      bus,
      columns,
      components,
      noTransColumns,
      canCreate,
      rowSelection,
      type,
      entityId,
      isHabSetValue,
      pagination,
      onJumpLastPage,
      form,
      record,
      dataIndex,
      title,
      onChange,
      ...restProps
    } = this.props


    if (!field?.type) {
      return <td {...restProps}>{children}</td>
    }

    const { getFieldDecorator } = form
    return <td {...restProps}>{editing ? this.renderCellItem({ getFieldDecorator, ...this.props }) : children}</td>
  }
}

@EnhanceConnect(state => ({
  authStaffStaffMap: state['@common'].authStaffStaffMap
}))
class EditTableWrapper extends Component<IProps, IState> {
  constructor(props) {
    super(props)
    this.state = {
      dataSource: props.dataSource,
      needCalculateUpdateFields: null,
      isFullScreen: false,
      selectedRowKeySet: new Set(),
      hoveredRowIndex: undefined,
      debouncedFunctions: new Map(),
      deletePopoverVisible: false,
      deleteTargetIndex: undefined,
      loading: false,
      apportionForm: {},
      apportionErrors: [],
    }
  }

  componentWillMount(): void {
    const { bus = new MessageCenter(), flowBus, field } = this.props
    bus.watch('get:edit:table:values', this.handleGetEditTableValues)
    flowBus?.watch(`submit:bill:click:validate:${field?.name}`, this.handleValidateValues)
  }

  componentWillUnmount(): void {
    let { bus = new MessageCenter(), flowBus, field } = this.props
    bus.un('get:edit:table:values', this.handleGetEditTableValues)
    flowBus?.un(`submit:bill:click:validate:${field?.name}`, this.handleValidateValues)
    // 清理全屏相关的事件监听器和样式
    document.removeEventListener('keydown', this.handleEscKey)
    document.body.style.overflow = 'auto'
  }

  handleFullScreen = () => {
    const { isFullScreen } = this.state
    this.setState({ isFullScreen: !isFullScreen }, () => {
      // 全屏时防止背景滚动
      if (!isFullScreen) {
        document.body.style.overflow = 'hidden'
        document.addEventListener('keydown', this.handleEscKey)
      } else {
        document.body.style.overflow = 'auto'
        document.removeEventListener('keydown', this.handleEscKey)
      }
    })
  }

  handleEscKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && this.state.isFullScreen) {
      this.handleFullScreen()
    }
  }

  handleGetEditTableValues = () => {
    return this.state.dataSource || []
  }

  componentDidMount(): void {
    this.fnDataSource(this.props)
  }

  // 计算数据所在的页码
  getDataPageNumber = (dataIndex: number, pageSize: number = 20) => {
    return Math.floor(dataIndex / pageSize) + 1
  }

  validateAllDataSources = () => {
    const dataLinkEntity = api.getState()['@bills'].dataLinkEntity
    const hasEmptyData = []
    const { dataSource } = this.state
    const { pagination } = this.props

    // 如果没有分页配置，返回空数组
    if (!pagination) {
      return []
    }

    // 计算当前页的数据范围
    const currentPage = pagination.current || 1
    const pageSize = pagination.pageSize || 20
    const currentPageDataStartIndex = (currentPage - 1) * pageSize
    const currentPageDataEndIndex = Math.min(currentPageDataStartIndex + pageSize, dataSource.length)

    // 校验非当前页的数据
    dataSource.forEach((it, index) => {
      // 跳过当前页的数据
      if (index >= currentPageDataStartIndex && index < currentPageDataEndIndex) {
        return
      }

      const { dataLinkTemplateId } = it
      const currDataLinkEntity = dataLinkEntity.find(it => it.templateId === dataLinkTemplateId)
      if (currDataLinkEntity) {
        let components = currDataLinkEntity?.tableTemplate?.components
        components = components?.filter(i => i.type !== 'switcher')
        components.some(oo => {
          const { field, optional } = oo
          const val = it[field]
          if (!optional && (!val || val?.length === 0)) {
            const pageNumber = this.getDataPageNumber(index, pageSize)
            hasEmptyData.push({
              index: index + 1, // 全局索引（从1开始）
              pageNumber: pageNumber // 数据所在的页码
            })
            return true
          }
        })
      }
    })

    return hasEmptyData
  }

  handleValidateValues = () => {
    const { form, pagination } = this.props
    return new Promise((resolve, reject) => {
      form.validateFields((err, values) => {
        if (err) {
          resolve('failed')
        } else {
          const hasEmptyData = this.validateAllDataSources()
          if (hasEmptyData.length > 0) {
            // 获取所有有问题的页码
            const pageNumbersMap = {}
            hasEmptyData.forEach(item => {
              pageNumbersMap[item.pageNumber] = true
            })
            const pageNumbers = Object.keys(pageNumbersMap).map(Number).sort((a, b) => a - b)
            // 如果有分页，跳转到第一个有问题的页面
            if (pagination && pageNumbers.length > 0) {
              pagination.onChange(pageNumbers[0], pagination.pageSize)
            }

            form.validateFields()
            reject('failed')
          } else {
            resolve('success')
          }
        }

      })
    })
  }

  componentWillReceiveProps(nextProps: Readonly<IProps>, nextContext: any): void {
    if (!isEqual(nextProps.dataSource, this.props.dataSource)) {
      this.fnDataSource(nextProps)
    }

    if (nextProps.pagination?.current !== this.props.pagination?.current) {
      this.setState({ loading: true })
      setTimeout(() => {
        this.resetFormFields(this.state.dataSource)
        this.setState({ loading: false })
      }, 0)
    }
  }

  fnDataSource = props => {
    const { dataSource = [] } = props
    this.setState({ dataSource })
  }

  // 重新设置表单字段值以匹配新的数据源
  resetFormFields = (newData, index?: number) => {
    const { form, components, pagination } = this.props
    if (typeof index === 'number') {
      // 只更新指定行的表单字段
      const newFormValues = {}
      if (newData[index]) {
        components?.forEach(component => {
          const fieldName = component.name
          const fieldNameWithIndex = `${fieldName}_${index}`
          const value = newData[index][fieldName]
          newFormValues[fieldNameWithIndex] = value
        })
        // 判断index是否在当前页
        if (pagination) {
          const currentPage = pagination.current || 1
          const pageSize = pagination.pageSize || 20
          const currentPageStartIndex = (currentPage - 1) * pageSize
          const currentPageEndIndex = currentPageStartIndex + pageSize

          // 只有当index在当前页范围内时，才设置表单值
          if (index >= currentPageStartIndex && index < currentPageEndIndex) {
            form.setFieldsValue(newFormValues)
          }
        } else {
          // 如果没有分页，直接设置表单值
          form.setFieldsValue(newFormValues)
        }
      }
    } else {
      // 清除所有表单字段并重新设置
      form.resetFields()

      // 根据新数据重新设置表单值
      const newFormValues = {}

      // 计算当前页的数据范围
      let startIndex = 0
      let endIndex = newData.length

      if (pagination) {
        const currentPage = pagination.current
        const pageSize = pagination.pageSize
        startIndex = (currentPage - 1) * pageSize
        endIndex = Math.min(startIndex + pageSize, newData.length)
      }
      // 只为当前页的数据设置表单值
      for (let recordIndex = startIndex; recordIndex < endIndex; recordIndex++) {
        const record = newData[recordIndex]
        if (record) {
          components?.forEach(component => {
            const fieldName = component.name
            const fieldNameWithIndex = `${fieldName}_${recordIndex}`
            const value = record[fieldName]
            newFormValues[fieldNameWithIndex] = value
          })
        }
      }
      form.setFieldsValue(newFormValues)
    }
  }

  handleCreate = () => {
    const { components } = this.props
    const { dataSource } = this.state
    const index = dataSource.length
    const newData = { index }
    components?.forEach(el => {
      newData[el?.name] = el.type === 'switcher' ? false : ''
    })
    const data = dataSource.concat([newData])
    this.updateTableData({
      actionName: 'onCreate',
      index: index,
      dataSource: data,
      changeField: null
    })
  }

  handleSelect = () => {
    const { onSelect } = this.props
    onSelect?.()
  }

  // 处理鼠标悬浮
  handleRowMouseEnter = (record, index) => {
    this.setState({ hoveredRowIndex: index })
  }

  handleRowMouseLeave = () => {
    this.setState({ hoveredRowIndex: undefined })
  }

  // 批量删除
  handleBatchDelete = () => {
    const { onGetData } = this.props
    const { dataSource, selectedRowKeySet } = this.state

    const newData = dataSource
      .filter((item, index) => {
        return !selectedRowKeySet.has(index)
      })
      .map((item, newIndex) => ({
        ...item,
        index: newIndex
      }))

    this.setState(
      {
        dataSource: newData,
        selectedRowKeySet: new Set(),
        hoveredRowIndex: undefined
      },
      () => {
        // 强制刷新组件以确保UI状态正确
        this.forceUpdate()
        this.resetFormFields(newData)
      }
    )

    onGetData?.(newData)
  }

  removeNthElement = (arr, n) => {
    return arr.slice(0, n).concat(arr.slice(n + 1))
  }

  handleDelete = (index, e) => {
    e?.stopPropagation()
    e?.preventDefault()
    const { onGetData } = this.props
    const { dataSource, selectedRowKeySet } = this.state

    // 删除指定索引的元素，并重新计算所有元素的index
    const newData = this.removeNthElement(dataSource, index).map((item, newIndex) => ({
      ...item,
      index: newIndex
    }))

    const newSelectedRowKeys = []
    Array.from(selectedRowKeySet).forEach(val => {
      if (val > index) {
        newSelectedRowKeys.push(val - 1)
      } else if (val === index) {
        !selectedRowKeySet.has(val) && newSelectedRowKeys.push(val - 1)
      } else {
        newSelectedRowKeys.push(val)
      }
    })

    this.setState({ dataSource: newData, selectedRowKeySet: new Set(newSelectedRowKeys) }, () => {
      // 重新设置表单字段值以匹配新的数据源
      this.resetFormFields(newData)
    })

    onGetData?.(newData)
  }

  handleCopy = index => {
    const { onGetData, entityId, components } = this.props
    const { dataSource } = this.state
    // 复制指定索引的元素，并为新元素分配正确的index
    const copiedItem = cloneDeep(dataSource[index])
    const newIndex = dataSource.length
    const newValue = {
      ...copiedItem,
      dataLinkId: null,
      index: newIndex
    }
    const codeField = components?.find(item => item.name === `E_${entityId}_code`)
    if (codeField?.dataType?.type === 'text' && newValue[`E_${entityId}_code`]) {
      newValue[`E_${entityId}_code`] = ''
    }
    const newData = dataSource.concat(newValue)
    this.setState({ dataSource: newData }, () => {
      this.resetFormFields(newData, newIndex)
    })

    onGetData?.(newData)
  }

  renderReminder = () => {
    return (
      <div style={{ color: 'var(--eui-function-danger-500)' }}>
        <p>{i18n.get('注意')}</p>
        <p>{i18n.get('1、如果要新增数据请下载空白模版')}</p>
        <p>{i18n.get('2、导入后单据表格中原有数据将清空，并将Excel中的数据展示在表格中')}</p>
      </div>
    )
  }

  handleImport = () => {
    const { entityId, onGetData, field } = this.props
    api
      .open('@bills:ImportDetailByExcel', {
        type: 'datalink4flow',
        flag: {
          id: entityId,
          fieldName: field?.label
        },
        renderReminder: this.renderReminder
      })
      .then((data: any[]) => {
        const flattenData = data?.map((item, index) => ({
          ...item.dataLinkForm,
          dataLinkTemplateId: item.dataLinkTemplateId,
          dataLinkId: item.dataLinkId,
          index
        }))
        this.setState({ dataSource: flattenData })
        this.resetFormFields(flattenData)
        onGetData?.(flattenData)
      })
  }
  handleExport = async () => {
    const { field, type } = this.props
    const { selectedRowKeySet } = this.state
    const result: { staff: any } = await api.open('@third-party-manage:ExportOption', {
      showStaff: true,
      hideColumnOption: true
    })
    const { staff } = result

    const newData = cloneDeep(this.state.dataSource).filter((item) => {
      if (selectedRowKeySet.size > 0) {
        return selectedRowKeySet.has(item.index)
      } else {
        return true
      }
    }).map((item) => {
      const dataLinkId = item.dataLinkId || null
      const dataLinkTemplateId = item.dataLinkTemplateId
      delete item.disabledByState
      delete item.dataLinkId
      delete item.dataLinkTemplateId
      delete item.index
      const dataForm = resetDataLinEdits(item)
      Object.keys(dataForm).forEach(key => {
        if (!dataForm[key]) {
          delete dataForm[key]
        }
      })
      return {
        dataLinkId,
        dataLinkTemplateId: dataLinkTemplateId,
        dataLinkForm: dataForm
      }
    })
    const body = {
      items: newData,
      form: {
        staffType: staff
      }
    }
    Fetch.POST(
      `/api/v1/datalink/excel/export/datalink4flow/$${this.props.entityId}`,
      { corpId: encodeURIComponent(Fetch.ekbCorpId) },
      {
        body,
        headers: { accept: '*' },
        isBlob: true
      }
    )
      .then(blob => {
        const el = getLinkNodeElement()
        const url = window.URL.createObjectURL(blob)
        const l = type === 'settlementChecking' ? '账单调整' : field?.label
        const filename = `${l}_${moment().format('YYYYMMDD_HHmmss')}.xlsx`
        const name = decodeURIComponent(filename as string)
        el.setAttribute('href', url)
        el.setAttribute('download', name)
        triggerClick(el)
      })
      .catch(err => { })
  }

  getNeedCalculateUpdateFields = () => {
    const { components } = this.props
    const { needCalculateUpdateFields } = this.state
    if (needCalculateUpdateFields) {
      return needCalculateUpdateFields
    }
    const calculateUpdateFields = []
    components?.forEach(item => {
      if (item.formula) {
        calculateUpdateFields.push(item.name)
      }
    })
    this.setState({
      needCalculateUpdateFields: calculateUpdateFields
    })
    return calculateUpdateFields
  }

  calculateFormula = debounce(async (newDataSource, index) => {
    const { entityId, onGetData } = this.props
    try {
      const needCalculateUpdateFields = this.getNeedCalculateUpdateFields()
      if (needCalculateUpdateFields.length === 0) {
        return
      }
      this.setState({ loading: true })
      const formValue = Object.entries(newDataSource[index] || {}).reduce((acc, [key, value]) => {
        acc[key] = typeof value === 'number' && key !== 'index' ? String(value) : value
        return acc
      }, {})
      const params = {
        dataLinkFormList: [
          {
            form: formValue,
            dataLinkId: newDataSource[index]?.dataLinkId
          }
        ],
        dataLinkEntityId: entityId
      }

      const result = await api.invokeService('@bills:get:getDataLinkEditCalculate', params)
      const newValues = {}

      Object.keys(result[0].form).forEach(key => {
        if (needCalculateUpdateFields.includes(key)) {
          newValues[key] = result[0].form[key]
        }
      })
      const newData = newDataSource.slice()
      newData[index] = {
        ...newData[index],
        ...newValues,
        index
      }
      this.setState({ dataSource: newData }, () => {
        this.resetFormFields(newData, index)
        this.setState({ loading: false })
        onGetData?.(newData)
      })
    } catch (error) {
      this.setState({ loading: false })
      console.log(error)
    }
  }, 500)

  updateTableData = async ({ actionName, index, dataSource, changeField }) => {
    const { onGetData, onJumpLastPage } = this.props
    this.setState({ dataSource }, () => {
      if (actionName === 'onCreate') {
        onJumpLastPage?.(dataSource.length)
      }
      onGetData?.(dataSource)
      const flag = getBoolVariation('ao-2-datalink-show-calculate')
      const isNeedFormula = changeField?.calculation?.dependenciesBy?.length > 0
      if ((actionName === 'onCreate' && flag) || (actionName === 'onChange' && isNeedFormula && flag)) {
        this.calculateFormula(dataSource, index)
      }
    })
  }
  // 新增：实时保存方法
  handleRealTimeSave = (field: any, value: any, recordIndex: number) => {
    const { components, type } = this.props
    const fieldName = field?.name
    const { dataSource } = this.state
    const newData = dataSource.slice() // 替代 [...dataSource]
    const index = newData.findIndex(item => item.index === recordIndex)

    if (index > -1) {
      const updatedRecord = { ...newData[index], [fieldName]: value }

      // 格式化单个字段值
      const formattedValue = formatCellValue({ [fieldName]: value }, components || [], type)
      updatedRecord[fieldName] = formattedValue[fieldName]

      newData[index] = updatedRecord
      this.updateTableData({
        actionName: 'onChange',
        index: recordIndex,
        dataSource: newData,
        changeField: field
      })
    }
  }

  onChange = (field, recordIndex?: number, value?: any) => {
    const vv = value?.target?.value || value
    // 如果是实时保存模式，且提供了recordIndex，触发实时保存
    if (recordIndex !== undefined && field?.name) {
      this.handleRealTimeSave(field, vv, recordIndex)
    }
  }

  getColumns = () => {
    const {
      authStaffStaffMap,
      components,
      noTransColumns,
      isEdit,
      rowSelection,
      isHabSetValue,
      canCreate,
      apportionConfigs,
      onApportionFormChange
    } = this.props
    const { apportionForm } = this.state
    if (noTransColumns) {
      return components.map(item => {
        return {
          ...item,
          dataIndex: item.dataIndex.split('.')
        }
      })
    }
    let columns =
      components?.map((item, index) => {
        const { field, customData, type } = item
        return {
          title: realName({ name: item.label, enName: item.enLabel }),
          dataIndex: field,
          key: field,
          field: item,
          editable: this.props.type === 'settlementChecking' ? item.editable : true,
          width: type === 'money' ? 150 : 100,
          render: (text, record) => {
            if (type === 'textarea' || type === 'text') {
              return <SimpleCell text={text} name={item.name} />
            } else if (type === 'number') {
              return <NumberCell text={text} field={item} />
            } else if (type === 'city') {
              return <CityCell text={text} />
            } else if (type === 'switcher') {
              return <SwitchCell text={text} />
            } else if (type === 'money') {
              return <MoneyCell text={text} />
            } else if (type === 'dateRange') {
              return <DateRangeCell text={text} field={item} />
            } else if (type === 'date') {
              return <DateCell text={text} field={item} />
            } else if (type === 'list:ref:organization.Staff') {
              return <ListRefOrganizationStaffCell text={text} authStaffStaffMap={authStaffStaffMap} />
            } else if (type === 'ref:organization.Staff') {
              return <RefOrganizationStaffCell text={text} />
            } else if (type === 'payeeInfo') {
              return <PayeeInfoCell text={text} />
            } else if (includes(ENUM_TYPES, type) || type?.startsWith('basedata.Enum')) {
              return <EnumCell text={text} customData={customData} />
            } else if (
              type?.startsWith?.('ref') &&
              type !== 'ref:organization.Staff' &&
              !includes(ENUM_TYPES, type) &&
              type !== 'ref:basedata.Enum.currency'
            ) {
              // 包含部门
              return <RefCell text={text} />
            } else if (type === 'dataLink' || type === 'dataLinks') {
              return <DataLinksCell text={text} isHabSetValue={isHabSetValue} />
            } else if (type === 'attachments') {
              return <AttachmentsCell value={text} />
            } else {
              return '未知类型'
            }
          }
        }
      }) || ([] as any[])

      if (getBoolVariation('aprd-5665-datalink') && apportionConfigs.length > 0) {
        const apportionColumns = apportionConfigs.map(item => ({
          title: item.label,
          dataIndex: item.field,
          key: item.field,
          field: item,
          columnType: 'apportionColumn',
          width: 160,
          render: (text, record) => {
            const numberValue = getNumValue(text)
            const unit = item.type === 'MONEY' ? 'CNY' : ''
            if (isEdit) {
              return <InputNumber
                formatter={value => `${unit} ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value!.replace(/\CNY\s?|(,*)/g, '')}
                style={{ width: '100%' }}
                onClick={(e) => {
                  e.stopPropagation()
                }}
                placeholder={i18n.get('请输入')}
                value={numberValue}
                onChange={(value) => {
                  const val = {
                    [item.field]: item.type === 'MONEY' ? {
                      standard: value,
                      standardNumCode: "156",
                      standardScale: 2,
                      standardStrCode: "CNY",
                      standardSymbol: "¥",
                      standardUnit: "元"
                    } : value
                  }
                  apportionForm[record.index] = apportionForm[record.index] ? {
                    ...apportionForm[record.index],
                    ...val,
                  } : val
                  this.setState({
                    apportionForm
                  }, () => {
                    onApportionFormChange(apportionForm)
                  })
  
                }}
              />
            } else {
              return formater(`${unit} ${numberValue}`)
            }
  
          }
        }))
        columns = columns.concat(apportionColumns)
      }

    if (isEdit) {
      columns.push({
        title: i18n.get('操作'),
        dataIndex: 'operation',
        key: 'operation',
        width: 150,
        fixed: 'right',
        render: (text, record, index) => {
          const editable = true

          return editable ? (
            <div className="btn-group">
              {canCreate && <Button category="text" theme="highlight" onClick={() => this.handleCopy(record.index)}>
                {i18n.get('复制')}
              </Button>}
              <Popover
                content={
                  <div style={{ padding: '12px 0' }}>
                    <div style={{ marginBottom: '8px', color: 'var(--eui-text-title)', font: 'var(--eui-font-body-b1)', display: 'flex', alignItems: 'center' }}>
                      <FilledTipsWarning style={{ marginRight: '4px', color: 'var(--eui-function-warning-500)', fontSize: 14 }} />
                      {i18n.get('确定删除吗？')}
                    </div>
                    <div style={{ color: 'var(--eui-text-title)', font: 'var(--eui-font-body-r1)', marginBottom: '12px', marginLeft: 18 }}>
                      {i18n.get('删除操作无法撤销')}
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <Button
                        category="text"
                        size="small"
                        style={{ marginRight: '8px' }}
                        onClick={(e) => {
                          e.stopPropagation()
                          e.preventDefault()
                          this.setState({ deletePopoverVisible: false, deleteTargetIndex: undefined })
                        }}
                      >
                        {i18n.get('取消')}
                      </Button>
                      <Button
                        category="primary"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          e.preventDefault()
                          this.handleDelete(record.index, null)
                          this.setState({ deletePopoverVisible: false, deleteTargetIndex: undefined })
                        }}
                      >
                        {i18n.get('确定')}
                      </Button>
                    </div>
                  </div>
                }
                trigger="click"
                onOpenChange={(visible) => {
                  this.setState({ deletePopoverVisible: visible })
                }}
                open={this.state.deletePopoverVisible && this.state.deleteTargetIndex === index}
                placement="top"
              >
                <Button category="text" theme="highlight" onClick={(e) => {
                  e.stopPropagation()
                  this.setState({ deletePopoverVisible: true, deleteTargetIndex: index })
                }}>
                  {i18n.get('删除')}
                </Button>
              </Popover>
            </div>
          ) : (
            <div className="btn-group">
              {record.disabledByState ? null : (
                <a>
                  <EKBIcon
                    className="icon-style stand-20-icon"
                    name="#EDico-zf-delete"
                    onClick={e => this.handleDelete(index, e)}
                  />
                </a>
              )}
            </div>
          )
        }
      })
    }
    if (!rowSelection) {
      let pagination = this.props.pagination || {
        current: 1,
        pageSize: this.state.dataSource.length
      }
      const { selectedRowKeySet, hoveredRowIndex, dataSource } = this.state
      const currentPageDataStartIndex = (pagination.current - 1) * pagination.pageSize
      const currentPageDataLength =
        dataSource.length - currentPageDataStartIndex > pagination.pageSize
          ? pagination.pageSize
          : dataSource.length - currentPageDataStartIndex
      const currentPageSelectedIds = []
      for (let i = 0; i < currentPageDataLength; i++) {
        const realIndex = currentPageDataStartIndex + i
        if (selectedRowKeySet.has(realIndex)) {
          currentPageSelectedIds.push(realIndex)
        }
      }
      columns.unshift({
        title: isEdit ? (
          <input
            type="checkbox"
            checked={currentPageDataLength && currentPageSelectedIds.length === currentPageDataLength}
            ref={input => {
              if (input) {
                input.indeterminate =
                  currentPageSelectedIds.length > 0 && currentPageSelectedIds.length < currentPageDataLength
              }
            }}
            onChange={e => {
              const newSelectedRowKeySet = new Set(selectedRowKeySet)
              if (e.target.checked) {
                for (let i = 0; i < currentPageDataLength; i++) {
                  newSelectedRowKeySet.add(currentPageDataStartIndex + i)
                }
              } else {
                for (let i = 0; i < currentPageDataLength; i++) {
                  newSelectedRowKeySet.delete(currentPageDataStartIndex + i)
                }
              }
              this.setState({ selectedRowKeySet: newSelectedRowKeySet })
            }}
            style={{ cursor: 'pointer' }}
          />
        ) : null,
        dataIndex: 'index',
        key: 'index',
        width: 50,
        fixed: 'left',
        render: (text, record, index) => {
          const realIndex = (pagination.current - 1) * pagination.pageSize + index
          const isSelected = selectedRowKeySet.has(realIndex)
          const isHovered = hoveredRowIndex === realIndex
          const showCheckbox = isSelected || isHovered

          return (
            <div
              className="edit-number-wrapper"
              style={{ position: 'relative', width: '100%', textAlign: 'center' }}
              onMouseEnter={() => this.handleRowMouseEnter(record, realIndex)}
              onMouseLeave={this.handleRowMouseLeave}
              onClick={e => {
                e.stopPropagation()
                e.preventDefault()
              }}
            >
              {showCheckbox && isEdit ? (
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={e => {
                    e.stopPropagation()
                    const isChecked = e.target.checked
                    const newSelectedRowKeySet = new Set(selectedRowKeySet)
                    isChecked ? newSelectedRowKeySet.add(realIndex) : newSelectedRowKeySet.delete(realIndex)
                    this.setState({ selectedRowKeySet: newSelectedRowKeySet })
                  }}
                  style={{ cursor: 'pointer' }}
                />
              ) : (
                <span>{index + 1}</span>
              )}
            </div>
          )
        }
      })
    }
    return columns
  }

  render() {
    const { dataSource, selectedRowKeySet, isFullScreen, apportionErrors } = this.state
    const {
      bus,
      form,
      canCreate,
      rowSelection,
      onSelect,
      isEdit,
      field,
      type,
      pagination = false,
      onRowClick,
      flowFormFieldsValue
    } = this.props
    let columns = this.getColumns()
    const summaryIndex = columns.findIndex(item => item?.columnType === 'apportionColumn')
    if (isEdit && field?.behaviour !== 'REF') {
      columns = columns.map(col => {
        return Object.assign({}, col, {
          onCell: record => ({
            record,
            bus,
            dataIndex: `${col.dataIndex}_${record.index}`,
            title: col.title,
            field: col.field,
            editing: col.editable,
            form,
            onChange: (value) => this.onChange(col.field, record.index, value),
          })
        })
      })
    }

    if (!columns.length) {
      return null
    }

    let otherProps: any = rowSelection ? { rowSelection } : {}
    if (summaryIndex >= 0) {
      otherProps.summary = () => <Summary
        flowFormFieldsValue={flowFormFieldsValue}
        summaryIndex={summaryIndex}
        columns={columns}
        dataSource={dataSource}
      />
    }

    const wrapperClassName = isFullScreen ? styles.fullScreenWrapper : styles.dataLinkEditTableWrapper
 
    const scroll = isFullScreen ? { x: 300, y: 'calc(100vh - 200px)' } : { x: 300 }
    return (<div className={wrapperClassName}>
       <AlertApportionError
          columns={columns}
          flowFormFieldsValue={flowFormFieldsValue}
          dataSource={dataSource}
        />
        {apportionErrors.length > 0 && <Alert
          type="error"
          message={`当前表格存在 ${apportionErrors.length} 个问题，请修改后再提交单据`}
          className='overflow-description'
          description={
            <div>
              {apportionErrors.map((item, index) => <div key={index}>${item}</div>)}
            </div>
          }
        />}
      <div className={styles.dataLinkEditTableHeader}>
        <Space>
          {onSelect && (
            <span onClick={this.handleSelect} className={styles.itemCreateBtn}>
              <OutlinedEditBatchYes /> {i18n.get('选择')}
            </span>
          )}
          {canCreate && (
            <span onClick={this.handleCreate} className={styles.itemCreateBtn}>
              <OutlinedTipsAdd /> {i18n.get('点击添加')}
            </span>
          )}
          {(canCreate || onSelect) && field?.behaviour !== 'REF' && (
            <span onClick={this.handleImport} className={styles.itemCreateBtn}>
              <OutlinedDirectionDownload /> {i18n.get('导入')}
            </span>
          )}
          {type !== 'settlementChecking' && isEdit && field?.behaviour !== 'REF' && (
            <span onClick={this.handleExport} className={styles.itemCreateBtn}>
              <OutlinedDirectionUpload /> {i18n.get('导出')}
            </span>
          )}
          {selectedRowKeySet.size > 0 && (
            <Popover
              content={
                <div style={{ padding: '8px 0' }}>
                  <div style={{ marginBottom: '8px', color: '#333', fontSize: '14px' }}>
                    {i18n.get('确定删除选中的 {count} 条数据吗？', { count: selectedRowKeySet.size })}
                  </div>
                  <div style={{ color: '#666', fontSize: '12px', marginBottom: '12px' }}>
                    {i18n.get('删除操作无法撤销')}
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <Button
                      category="text"
                      size="small"
                      style={{ marginRight: '8px' }}
                      onClick={() => this.setState({ deletePopoverVisible: false })}
                    >
                      {i18n.get('取消')}
                    </Button>
                    <Button
                      category="primary"
                      size="small"
                      onClick={() => {
                        this.handleBatchDelete()
                        this.setState({ deletePopoverVisible: false })
                      }}
                    >
                      {i18n.get('确定')}
                    </Button>
                  </div>
                </div>
              }
              open={this.state.deletePopoverVisible && this.state.deleteTargetIndex === undefined}
              onOpenChange={(visible) => {
                this.setState({
                  deletePopoverVisible: visible,
                  deleteTargetIndex: visible ? undefined : this.state.deleteTargetIndex
                })
              }}
              trigger="click"
              placement={isFullScreen ? 'bottom' : undefined}
            >
              <span className={styles.itemCreateBtn} style={{ color: '#ff4d4f', cursor: 'pointer' }}>
                <OutlinedTipsAdd /> {i18n.get('批量删除')}
              </span>
            </Popover>
          )}
          {isFullScreen ? (
            <span onClick={this.handleFullScreen} className={styles.itemCreateBtn}>
              <OutlinedDirectionWindowMini /> {i18n.get('退出全屏')}
            </span>
          ) : (
            <span onClick={this.handleFullScreen} className={styles.itemCreateBtn}>
              <OutlinedDirectionWindowMax /> {i18n.get('全屏')}
            </span>
          )}
        </Space>
      </div>
      {/* @ts-ignore */}
      <Table
        className={styles.dataLinkEditTable}
        components={{
          body: {
            cell: EditableCell
          }
        }}
        bordered
        dataSource={dataSource}
        columns={columns}
        pagination={pagination}
        size="middle"
        scroll={scroll}
        onRow={(record, index) => ({
          onClick: () => onRowClick?.(record, index)
        })}
        loading={this.state.loading}
        {...otherProps}
      />
    </div>
    )
  }
}

export default Form.create()(EditTableWrapper)
