import styles from './card-log-item.module.less'
import moment from 'moment'
import { Popover } from 'antd'
import Highlighter from 'react-highlight-words'
import { flowStateMap as logMap, formatAttachments, renderComment, addAtHighlighter } from './flow-log-item'
import { comment2MentionContent, formatRegStr } from './util/parse'
import 'moment/locale/zh-cn'
import { getStaffShowByConfig } from '../../utilFn'
import { renderCommentWithMarkdown } from './FlowLogItemWrapper';
function fnFormatDate(timeStamp) {
  moment.locale('zh-cn')
  let now = moment().format('YYYY-MM-DD')
  let day = moment(timeStamp).format('YYYY-MM-DD')
  let diff = moment(day).diff(now, 'days', true)
  let time = moment(timeStamp).format('HH:mm')
  return diff < -6
    ? moment(timeStamp).format(i18n.get('MM月DD日'))
    : diff < -2
      ? moment(timeStamp).format('ddd HH:mm')
      : diff === -2
        ? `${i18n.get('前天')} ${time}`
        : diff === -1
          ? `${i18n.get('昨天')} ${time}`
          : diff === 0
            ? `${i18n.get('今天')} ${time}`
            : ''
}

function parseReg(items = []) {
  return items.map(item => {
    return formatRegStr(item)
  })
}

const renderApproveContent = ({ content, highlighterWords, time, popoverContent = [] }, subDom) => {
  return (
    <div className={styles.horizontal}>
      <span className={styles.date}>{fnFormatDate(time)}</span>
      {popoverContent.length > 0 ? (
        <Popover overlayClassName={styles['card-log-item-popover']} content={popoverContent} trigger="hover">
          <div className={styles['approve-content-wrapper']}>
            {highlighterWords ? (
              <Highlighter
                highlightClassName={styles['content-highlight']}
                searchWords={highlighterWords}
                autoEscape={false}
                textToHighlight={content}
              />
            ) : (
              content
            )}
            {!!subDom && subDom}
          </div>
        </Popover>
      ) : (
        <div className={styles['approve-content-wrapper']}>
          {highlighterWords ? (
            <Highlighter
              highlightClassName={styles['content-highlight']}
              searchWords={highlighterWords}
              autoEscape={false}
              textToHighlight={content}
            />
          ) : (
            content
          )}
          {!!subDom && subDom}
        </div>
      )}
    </div>
  )
}

const renderAttachments = attachments => {
  const data = formatAttachments(attachments)
  return (
    <Popover title={data.label} content={data.attachmentsContent}>
      <div className={styles['attachment-wrapper']}>
        {i18n.get('{__k0} 个{__k1}', { __k0: data.length, __k1: data.label })}
      </div>
    </Popover>
  )
}

export const flowStateMap = {
  submit: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  agree: {
    render(item, userInfo) {
      const {
        attachments,
        attributes: { comment },
        time,
        ac
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { atDom } = addAtHighlighter(item, userInfo)
      return (
        <div className={styles['vertical']}>
          {renderApproveContent({ content, highlighterWords, time }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  'admin.skipnode': {
    render(item, userInfo) {
      const {
        attachments,
        attributes: { comment },
        time,
        ac
      } = item
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { atDom } = addAtHighlighter(item, userInfo)
      return (
        <div className={styles['vertical']}>
          {renderApproveContent({ content, highlighterWords, time }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{renderComment(item)}</div>}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  reject: {
    render(item, userInfo) {
      const {
        attachments,
        attributes: { comment },
        time,
        ac
      } = item
      const hasAttachments = attachments && attachments.length
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)
      const element = <Highlighter
        highlightClassName={styles['highlight']}
        searchWords={highlighterKey}
        textToHighlight={commentContent}
      />
      return (
        <div className={styles['vertical']}>
          {renderApproveContent({ content, highlighterWords, time }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>
            {renderCommentWithMarkdown(commentContent, element)}
          </div>}
          {!!hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  alter: {
    render(item) {
      const {
        attachments,
        attributes: { comment },
        time,
        ac
      } = item
      const hasAttachments = attachments && attachments.length
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return (
        <div className={styles['vertical']}>
          {renderApproveContent({ content, highlighterWords, time })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  pay: {
    render(item, userInfo, dataSource) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item, dataSource)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  paying: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent } = logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  },
  repaying: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent } = logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  },
  'pay.by.offline': {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent = [] } = logMap[ac].popoverContent && logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  },
  failure: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent } = logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  },
  'select.approver': {
    render(item, userInfo, dataSource) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item, userInfo, dataSource)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  skipped: {
    render(item, userInfo) {
      const {
        attributes: { comment },
        time,
        ac
      } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { atDom, highlighterWords: highlighterKey, content: commentContent } = addAtHighlighter(item, userInfo)
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords, time }, atDom)}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>
            <Highlighter
              highlightClassName={styles['highlight']}
              searchWords={highlighterKey}
              textToHighlight={commentContent}
            />
          </div>}
        </div>
      )
    }
  },
  autoAgree: {
    render(item) {
      const {
        attachments,
        attributes: { comment },
        time,
        ac
      } = item
      const hasAttachments = attachments && attachments.length
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return (
        <div className={styles['vertical']}>
          {renderApproveContent({ content, highlighterWords, time })}
          {comment && <div className={`${styles['content-msg']} translate-ignore-class`}>{comment}</div>}
          {!!hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  retract: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  withdraw: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  comment: {
    render(item, userInfo) {
      const {
        attachments,
        attributes: { participants = [], comment, receiverIds = [], delOperatorId, delTime },
        time,
        ac
      } = item
      const isAtMe = userInfo && !!~participants.map(e => e.id).indexOf(userInfo.id)
      const com = comment2MentionContent(comment, participants, logMap.staffDisplayConfigField)
      let highlighterWords = participants.map(item => ` @${item.name} `)
      highlighterWords = parseReg(highlighterWords)
      const hasAttachments = attachments && attachments.length > 0
      const { content, highlighterWords: contentHighlight } = logMap[ac].approveContent(item)
      const delOperator = delOperatorId && getStaffShowByConfig(delOperatorId)
      const delTimeFormat = delTime && moment(delTime).format('YYYY-MM-DD HH:mm')
      const subDom = isAtMe && (
        <div className={styles['horizontal']}>
          {i18n.get('，')}
          {i18n.get('并')}
          <div className={styles['refer-me']}>{i18n.get('@我')}</div>
        </div>
      )
      const element = comment ? <Highlighter
        highlightClassName={styles['highlight']}
        searchWords={highlighterWords}
        textToHighlight={com}
      /> : comment
      return (
        <div className={styles['content-wrapper']}>
          {renderApproveContent({ content, highlighterWords: contentHighlight, time }, subDom)}
          {comment && (
            <div className={`${styles['content-msg']} translate-ignore-class`}>
              {renderCommentWithMarkdown(com, element)}
            </div>
          )}
          {!comment && delOperatorId && (
            <div className={`${styles['content-msg']} translate-ignore-class fd-c`}>
              {i18n.get('评论已被管理员删除（{__k0}）', { __k0: delTimeFormat })}
            </div>
          )}
          {hasAttachments && renderAttachments(attachments)}
        </div>
      )
    }
  },
  carbonCopy: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  back: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  addnode: {
    render(item, userInfo) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)

      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  send: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  receive: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  modify: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  nullify: {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      return renderApproveContent({ content, highlighterWords, time })
    }
  },
  'pay.partial.success': {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent } = logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  },
  'pay.partial.paying': {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent } = logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  },
  'pay.partial.failure': {
    render(item) {
      const { time, ac } = item
      const { content, highlighterWords } = logMap[ac].approveContent(item)
      const { popoverContent } = logMap[ac].popoverContent(item)
      return renderApproveContent({ content, highlighterWords, time, popoverContent })
    }
  }
}

export default {
  flowStateMap
}
