/**************************************************
 * Created by nanyuantingfeng on 15/09/2017 16:21.
 **************************************************/
import { parseMeta2ColumnOthers, parseMeta2ColumSpecials, parseColumnFilter } from './columnOthers'
import parseFilterType from './parseFilterType'

export function prefix(name, prefixPath = '', exclude = []) {
  if (typeof prefixPath === 'string') {
    prefixPath = { '*': prefixPath }
  }

  if (!prefixPath[name] && !!~exclude.indexOf(name)) {
    return name
  }

  if (prefixPath[name] === '$') {
    return name
  }

  if (prefixPath[name]) {
    return `${prefixPath[name]}.${name}`
  }

  if (prefixPath['*']) {
    return `${prefixPath['*']}.${name}`
  }

  return name
}

const COLUMN_PROPERTY_MAPPING = {}

export function getColumnPropertyMapping() {
  return COLUMN_PROPERTY_MAPPING
}

const renderTitle = () => {
  return {
    render: (text, ...other) => {
      const isSamePerson = other[0]?.ownerId?.id === other[0]?.form?.submitterId?.id ? true : false
      const isBills = window.location.hash.includes('/bills')
      if (!text) return <span>-</span>
      return (
        <>
          {!isSamePerson && isBills && <span className="entrust-form-txt">{i18n.get('[委托]')}</span>}
          <span className="translate-ignore-class">{text}</span>
        </>
      )
    }
  }
}

function parseMeta2Column(
  property,
  prefixPath,
  mapping,
  specialColumn,
  dynamicChannelMap,
  Express,
  isDiDi,
  scenesType
) {
  let { name, label, dataType, canAsDimension = false } = property
  const { type, entity, elemType } = dataType
  const exclude = mapping.exclude
  //待办列表中关联申请和补充申请无法搜索出数据问题    解决PRO-11984问题
  if (name === 'expenseLink' || name === 'linkRequisitionInfo') {
    name = `${name}.name`
  }
  const dataIndex = prefix(name, prefixPath, exclude)
  const others = parseMeta2ColumnOthers(name, property, dynamicChannelMap, Express, scenesType)
  const filterOptions = parseColumnFilter(property, scenesType)
  COLUMN_PROPERTY_MAPPING[dataIndex] = property
  let specialList = ['submitterId', 'title', 'loanDate', 'repaymentDate', 'ownerId']
  let obj = {
    title: label,
    dataIndex,
    key: dataIndex,
    dataType: type,
    filterType: parseFilterType(property, type),
    sorter: true,
    label,
    canAsDimension,
    entity: entity ?? elemType?.entity,
    value: dataIndex,
    className: 'fs-14',
    ...others,
    ...filterOptions
  }
  if (specialColumn && specialList.indexOf(name) >= 0) {
    obj.render = parseMeta2ColumSpecials(name, specialColumn)
  }


  if (property.name === 'title') {
    obj.render = renderTitle().render
  }

  if (property.name === 'staffName' && isDiDi) {
    obj.sorter = false
    delete obj.filterType
  }

  return obj
}

export function createColumns(
  baseDataProperties = [],
  prefixPath,
  mapping,
  specialColumn,
  dynamicChannelMap,
  Express,
  isDiDi,
  scenesType,
  inFeeTypeMode
) {
  const filterAbilityList = ['apportion', 'loanInfo']
  if (!inFeeTypeMode) filterAbilityList.push('feeDetail')
  const filterDateTypeList = ['dynamic', 'profitAndLossSummary']
  const filterNameList = ['requisitionBalance']
  // 放开人员多选字段和关联申请(按明细)的过滤
  const isStaff = line => {
    if (line?.dataType?.type === 'list') {
      return !!line?.dataType?.elemType?.entity?.includes('.Staff') || line?.name === 'expenseLinks' //人员多选、关联申请(按明细)
    } else {
      return true
    }
  }
  const blackList = [
    'details',
    'attachments',
    'orders',
    'apportions',
    'amortizes',
    'apportionPercent',
    'apportionMoney',
    'payerId',
    'tripId',
    'tripType',
    'tripCity',
    'tripFromCity',
    'tripToCity',
    'tripForm',
    'tripTypeId',
    'validApprover'
  ]
  if (!inFeeTypeMode) {
    //consumptionReasons 消费事由有的企业需要，把他从黑名单里面移除
    blackList.push('feeTypeId', 'amount')
  }
  return baseDataProperties
    .filter(line => !~filterNameList.indexOf(line.name))
    .filter(line => !~filterDateTypeList.indexOf(line.dataType.type))
    .filter(isStaff)
    .filter(line => !~filterAbilityList.indexOf(line.ability))
    .filter(line => !~blackList.indexOf(line.name))
    .map(line =>
      parseMeta2Column(line, prefixPath, mapping, specialColumn, dynamicChannelMap, Express, isDiDi, scenesType)
    )
}

export function createColumnsSwitcherDataIndexes(type = 'all', prefixPath, mapping) {
  const array = mapping[type]
  const exclude = mapping.exclude
  return array.map(line => prefix(line, prefixPath, exclude))
}
