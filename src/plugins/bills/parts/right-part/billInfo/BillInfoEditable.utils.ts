import { get } from 'lodash'
import { getPayConfig } from '../../../util/parse'
import { enableCheckingVisibilityBeforeClearingAccounts } from '../../../../../lib/featbit';
import { Resource } from '@ekuaibao/fetch';

const accountsVisibilityCheck = new Resource('/api/pay/v2/accounts/visibility/check')

export interface SpecifationPayConfig {
  /**
   * 是否显示收款信息
   */
  readonly isRequirePayeeInfo: boolean;
  /**
   * 支付金额为零时，收款信息非必填
   */
  readonly optionalPayeeByZero: boolean;
  /**
   * 包含分摊明细
   */
  readonly paymentPlanByApportion: boolean;
  /**
   * 支付金额取值方式
   * loanMoney 取借款金额
   * expenseMoney-writtenOffMoney 费用金额－核销金额
   * reimbursementMoney 报账金额
   *
   */
  readonly payMoney: string;
  /**
   * 允许多收款人
   */
  readonly allowMultiplePayees: boolean;
  /**
   * 允许多收款人 按明细
   */
  readonly detailsOnly: boolean;
  /**
   * 允许多收款人 按金额
   */
  readonly moneyOnly: boolean;
  /**
   * 允许多收款人 按收款信息汇总明细金额
   */
  readonly payeeOnly: boolean;
  /**
   * 默认 false 是否允许选择收款货币
   */
  readonly allowSelectionReceivingCurrency: boolean;
  /**
   * 币种范围  为空是全部可选
   */
  readonly currencyRange: Set<string>;
}


const checkAccountsVisibility = async (submitterId: string, accountIds: string[]) => {
  try {
    const res =await accountsVisibilityCheck.POST('', {
      submitterId,
      accountIds,
    })
    return res.items || []
  } catch {
    return []
  }
}

/**
 * @description 处理单据支付信息变化，特别是在“更新”或者“切换”单据时的多收款人模式的管理。
 *
 * @param {Object} params - 参数对象
 * @param {Object} params.value - 单据表单值，包含 payeeId、details 等信息
 * @param {Object} params.templateInfo - 单据模板信息，用于获取支付配置
 * @param {Function} params.saveMultiplePayeed - 保存多收款人配置的回调函数
 * @param {Function} params.preparePayPlanData - 准备支付计划数据的回调函数
 *
 * ## 核心概念
 *
 * ### 收款模式
 * 系统支持三种收款模式：
 * - **按明细 (detailsOnly)**: 每个明细独立设置收款信息，收款信息在费用明细中维护
 * - **按金额 (moneyOnly)**: 统一在支付计划中设置收款信息
 * - **按收款信息汇总 (payeeOnly)**: 相同收款人的明细在支付时合并
 *
 * ### 关键数据结构
 * ```typescript
 * interface MultiplePayeeObj {
 *   multiplePayeesMode: boolean;  // 是否启用多收款人
 *   payPlanMode: boolean;         // 是否为按金额模式
 *   payeePayPlan: boolean;        // 是否为按收款信息汇总模式
 *   receivingCurrency?: string;   // 收款货币（可选）
 * }
 * ```
 *
 * ### 配置优先级
 * 当多个模式都未选中时，系统按以下优先级自动选择：
 * 1. 按金额 (moneyOnly)
 * 2. 按收款信息汇总 (payeeOnly)
 * 3. 按明细 (默认)
 *
 * @see {@link src/components/dynamic/SetMultiplePayee.js} - 多收款人设置组件
 * @see {@link src/plugins/bills/util/parse.js} - 包含 getPayConfig 函数
 *
 * @example
 * // 场景1：切换到按金额模式
 * // 用户选择"按金额"，系统清除明细中的收款账户，在支付计划中统一维护
 * handlePayInfoOfSpecifationChange({
 *   value: {
 *     payeeId: { multiplePayeesMode: true, payPlanMode: true },
 *     details: [...]
 *   },
 *   templateInfo: { ... },
 *   saveMultiplePayeed: (config) => { ... },
 *   preparePayPlanData: (value, flag) => { ... }
 * })
 *
 * @example
 * // 场景2：账户不可见处理
 * // 当收款账户对提交人不可见时，系统自动清理该账户信息
 *
 * @example
 * // 场景3：模式优先级处理
 * // 当没有选中任何模式时，按照 按金额 > 按汇总 > 按明细 的优先级自动选择
 *
 * @details
 * 业务场景说明：
 *
 * 1. **开启多收款人+按金额模式**
 *    - 清除所有明细中的收款账户信息(feeDetailPayeeId)
 *    - 调用preparePayPlanData准备支付计划
 *    - 收款信息在支付计划中维护
 *
 * 2. **开启多收款人+按明细模式**
 *    - 保留明细中的收款账户信息
 *    - 删除payPlan数据
 *    - 每个明细独立维护收款信息
 *
 * 3. **开启多收款人+按收款信息汇总**
 *    - 保留明细中的收款账户信息
 *    - 相同收款人的明细会在支付时合并
 *    - payeePayPlan标记为true
 *
 * 4. **关闭多收款人**
 *    - 清除所有明细中的收款账户信息
 *    - 删除payPlan数据
 *    - 使用单据级别的统一收款信息
 *
 * @throws {Error} 账户可见性检查失败时会捕获错误并执行旧的清理逻辑
 *
 * @see {@link checkAccountsVisibility} - 账户可见性检查函数
 * @see {@link SetMultiplePayee} - 多收款人设置组件
 */
export const handlePayInfoOfSpecifationChange = async ({
  value,
  templateInfo,
  saveMultiplePayeed,
  preparePayPlanData,
}: any) => {
  const { allowMultiplePayees, moneyOnly, payeeOnly, detailsOnly, payeePayPlan, allowSelectionReceivingCurrency } = getPayConfig(templateInfo)
  const multiplePayeesMode = get(value, 'payeeId.multiplePayeesMode')
  let multiplePayeeObj: any = {
    multiplePayeesMode: allowMultiplePayees ? multiplePayeesMode : false,
    payPlanMode: moneyOnly,
    payeePayPlan: payeePayPlan ?? value?.payeeId?.payeePayPlan
  }
  if (allowSelectionReceivingCurrency && value?.payeeId?.receivingCurrency) {
    multiplePayeeObj.receivingCurrency = value?.payeeId?.receivingCurrency
  }

  const runOldClear = () => {
    value.payeeId = !allowMultiplePayees && multiplePayeesMode ? null : multiplePayeeObj
    if (!allowMultiplePayees || (multiplePayeesMode && moneyOnly)) {
      value.details?.forEach(item => {
        delete item.feeTypeForm?.feeDetailPayeeId
      })
    }
  }

  const needClear = (items: any[], payeeId?: string) => {
    return items.length === 0 || (payeeId && items.every((item: any) => item.id !== payeeId))
  }

  if (enableCheckingVisibilityBeforeClearingAccounts()) {
    const submitterId = get(value, 'submitterId.id')
    // 1. 没有开发多收款人 2. 开启了多收款人，但是没有设置为多收款人
    if (!allowMultiplePayees || (allowMultiplePayees && !multiplePayeesMode)) {
      const payeeId = get(value, 'payeeId.id')
      if (payeeId) {
        try {
          const items = await checkAccountsVisibility(submitterId, [payeeId])
          if (needClear(items, payeeId)) {
            value.payeeId = null
          }
        } catch {
          runOldClear()
        }
      }
    } else {
      try {
        const getPayeeId = (_item: any) => get(_item, 'feeTypeForm.feeDetailPayeeId.id')
        const details = get(value, 'details', [])
        const accountIds = details.map((item: any) => getPayeeId(item))
        const items = await checkAccountsVisibility(submitterId, accountIds)
        multiplePayeeObj = {
          ...multiplePayeeObj,
          // payPlanMode由选项是否被激活，以及在原明细的payPlanMode以及是否在配置中激活来确定
          payPlanMode: moneyOnly && get(value, 'payeeId.payPlanMode'),
          payeePayPlan: payeeOnly && get(value, 'payeeId.payeePayPlan'),
        }
        // 如果当前没有选中任何收款维度，
        if (
          multiplePayeeObj.payPlanMode === false &&
          multiplePayeeObj.payeePayPlan === false &&
          detailsOnly === false
        ) {
          // 将当前激活的选项作为默认值
          if (moneyOnly) {
            multiplePayeeObj.payPlanMode = true
          } else if (payeeOnly) {
            multiplePayeeObj.payeePayPlan = true
          }
        }
        value.payeeId = multiplePayeeObj
        value.details?.forEach((item: any) => {
          if (
            // 1. 按金额模式下不需要账户信息
            // 2. 检查到不符合账户信息
            (multiplePayeeObj.multiplePayeesMode && multiplePayeeObj.payPlanMode) ||
            needClear(items, getPayeeId(item))
          ) {
            delete item.feeTypeForm.feeDetailPayeeId
          }
        })
      } catch(e) {
        console.error(e)
        runOldClear()
      }
    }
  } else {
    runOldClear()
  }

  saveMultiplePayeed(multiplePayeeObj)
  if (allowMultiplePayees && multiplePayeesMode && moneyOnly) {
    preparePayPlanData(value, false)
  } else {
    delete value.payPlan
  }
}