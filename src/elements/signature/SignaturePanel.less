@import '~@ekuaibao/eui-styles/less/token.less';

.signature_panel {
  display: flex;
  flex-direction: column;
  margin-top: -8px;
  justify-content: space-between;
  .img_wrap {
    width: 736px;
    height: 300px;
    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  .signature {
    min-height: 200px !important;
    border-radius: 8px;
    flex: 1;
  }
  .signature_image {
    width: 166px;
    height: 90px;
    border-radius: 6px;
  }
  .signature_placeholderStyle {
    .font-size-2;
    color: var(--eui-text-placeholder);
  }
  .signature_footer {
    display: flex;
    flex-direction: column;
    background-color: @color-white-1;
    .am-list-item .am-list-thumb:first-child {
      margin-right: unset;
    }
    .signature_check {
      .font-size-1;
      margin-left: @space-3;
    }
    .signature_buttonList {
      display: flex;
      margin: 8px 0 16px;
      .ant-checkbox-wrapper {
        span {
          .font-size-2;
        }
      }
      .signature_buttonGroup {
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        .signature_buttonLeft {
          display: flex;
          flex-direction: row;
        }
      }
    }
  }
}
