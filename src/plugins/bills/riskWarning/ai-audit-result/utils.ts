import { RiskWarningItem } from '../type'

export const AI_TAG = 'AI'
const FIELD_OF_RISK = 'riskLevel'

export const isAIItem = (item: RiskWarningItem) => {
  return item?.riskTag === AI_TAG
}

export const isPassItem = (item: RiskWarningItem) => {
  return item?.riskLevel === 'PASS'
}

export const isErrorItem = (item: RiskWarningItem) => {
  return item?.riskLevel === 'ERROR' || item?.isOutOfLimitReject
}

export const isWarningItem = (item: RiskWarningItem) => {
  return item?.riskLevel === 'WARNING'
}

export const getAIAuditSummary = (riskData: RiskWarningItem[] = []) => {
  const AIData = riskData.filter(isAIItem)
  const total = AIData.length
  const successCount = AIData.filter(isPassItem).length
  const riskCount = AIData.filter(isWarningItem).length
  const errorCount = AIData.filter(isErrorItem).length

  return {
    total,
    successCount,
    riskCount,
    errorCount
  }
}

export const getRiskItems = (riskData: RiskWarningItem[] = []) => {
  if (riskData.length === 0) return []
  return riskData.filter(item => {
    // 新版中会包含 PASS 的items
    if (item.hasOwnProperty(FIELD_OF_RISK)) {
      return isErrorItem(item) || isWarningItem(item)
    }
    return true
  })
}

/**
 * AI 风险，兼容老版本风险数据
 */
export const polyfillForAIRiskData = (_riskData: any = {}) => {
  const riskItems = getRiskItems(_riskData.riskWarningV2)
  const valueRiskWarning = getRiskItems(_riskData.value?.riskWarning || [])
  return {
    ..._riskData,
    riskWarningV2: riskItems,
    value: {
      ..._riskData.value,
      riskWarning: valueRiskWarning
    }
  }
}
