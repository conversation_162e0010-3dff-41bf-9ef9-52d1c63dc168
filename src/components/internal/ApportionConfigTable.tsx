import React, { useState, useEffect } from 'react'
import { Tooltip, Table } from '@hose/eui'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import styles from './ApportionConfigTable.module.less'
import { Fetch } from '@ekuaibao/fetch'

export const ApportionConfigTable = (props) => {
  const { entityId, specificationId, field } = props
  const [columns, setColumns] = useState([])
  const [dataSource, setDataSource] = useState([])
  useEffect(() => {

    (async () => {
      if (!entityId) {
        setDataSource([])
        return
      }
      const res = await Fetch.GET(`/api/v2/datalink/ledger/getApportionRule/$${entityId}?specificationId=${specificationId}&field=${field}`)
      const data = res.items?.map((item, index) => ({
        key: index,
        field: item?.ledgerConfig?.sumFieldLabel,
        label: item?.apportionConfig?.label
      }))
      setDataSource(data)
      setColumns([
        {
          title: '台账统计名称',
          dataIndex: 'field',
          key: 'field',
        },
        {
          title: <div>
            <span>分摊列展示名称</span>
            <Tooltip title="显示名称可前往「自建应用-业务对象-台账与统计」进行编辑">
              <OutlinedTipsInfo color="var('--eui-icon-n1')" fontSize={14} style={{ marginLeft: '4px' }} />
            </Tooltip>
          </div>,
          dataIndex: 'label',
          key: 'label',
        },
      ])
    })()
  }, [])
  if (dataSource.length == 0) {
    return null
  }
  return (
    <div className={styles.tableWrapper}>
      <div className={styles.header}>已开启手动分摊的台账统计</div>
      <Table
        className={styles.table}
        columns={columns}
        dataSource={dataSource}
        pagination={{
          size: "small",
          hideOnSinglePage: true,
          pageSize: 5
        }}
      />
    </div>
  )
}


export default ApportionConfigTable