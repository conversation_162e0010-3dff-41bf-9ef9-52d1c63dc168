import { BillActionBase } from './base-action'
import { app } from '@ekuaibao/whispered'
import { message as EUIMessage } from '@hose/eui'

/**
 * 申请事项变更
 */
export class AlterAction extends BillActionBase {
  label = i18n.get('变更')

  onAction = () => {
    return this.handleAlter()
  }

  handleAlter = () => {
    const { backlog } = this.props
    const { flowId } = backlog
    const { formType, form, id } = flowId
    const requisitionObj = form?.specificationId?.configs?.find(i => i.ability === 'requisition') || {}
    const mustRequire = !requisitionObj.optionalComment
    return new Promise(async (resolve, reject) => {
      const { message } = await app.open<{ message: string }>('@bills:ChangeApplyModal', { flowId: id, formType, mustRequire })
      const data = {
        flowIds: [id],
        action: {
          name: 'freeflow.alter',
          comment: message
        }
      }
      try {
        const result = await app.invokeService('@bills:flowDoAction', data)
        const value = result?.value || {}
        if (value.success) {
          // 变更申请原因填完之后，单据自动由“已完成”状态变成“待提交”状态
          // 至于变更过程的提交，由提交或者保存相关动作来处理
          resolve(true)
          const resp = await app.invokeService('@bills:get:flow-info', { id })
          app.open('@bills:BillInfoDrawerV2', {
            currentId: id,
            flows: [resp.value],
            from: 'from_myBill',
            showUpDown: false,
            bus: this.props.bus,
            closeAfterAction: false,
          })
        } else {
          const message = value.messages?.join(',')
          EUIMessage.warning(message)
          reject()
        }
      } catch (e) {
        EUIMessage.warning(e.message || e.msg)
        reject()
      }
    })
  }
}
