/*!
 * Copyright 2019 liyang. All rights reserved.
 * @since 2019-12-12 15:09:36
 */

import React, { PureComponent } from 'react'
import { Checkbox, Popover } from 'antd'
import style from './InvoiceListItem.module.less'
import Money from './../../../../../elements/puppet/Money'
import SVG_SUS_INVOICE from './../../../../../images/invoice_sus.svg'
import SVG_ERR_INVOICE from './../../../../../images/invoice_err.svg'
import { InvoiceDetailItem } from './../invoice/InvoiceDetailItemContainer'
import { totalInvoceMoney, getTaxAmount } from '../../../../../lib/InvoiceUtil'
import { invoiceMetaile } from '@ekuaibao/lib/lib/invoice/invoiceHelper'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnCheckdetailTrack, fnCheckinvoiceTrack } from '../../../../../elements/invoice-form/utils/invoiceTrack'
import {
  getInvoiceSignature,
  getInvoiceStatus,
  getIsCheckerInvoice,
  invoiceStatusStr
} from '../../../../../elements/InvoiceCard/InvoiceItem'
import { InvoiceFormIF, InvoiceMasterIF, StaffIF } from '@ekuaibao/ekuaibao_types'
import EKBIcon from '../../../../../elements/ekbIcon'
import { get } from 'lodash'
import { array2object } from '@ekuaibao/helpers'
import { T } from '@ekuaibao/i18n'
import InvoiceUploadView from '../../../views/InvoiceUploadView'
import classNames from 'classnames'
import RichMessage from '../RichMessage'
import { isElectronicAirAndTrain } from '../invoice/ImportInvoiceUtils'
import { convertForeignToStandard } from '../../../../../components/utils/fnCurrencyObj'
import { Tooltip, Button } from '@hose/eui'
import { OutlinedTipsInfo } from '@hose/eui-icons'
import { app } from '@ekuaibao/whispered'
import { enableOtherInvoiceByDimension } from '../../../../../lib/featbit/feat-switch'


interface IProps {
  onEdit(item: any): any
  item: any
  checked: boolean
  onCheckedChange(e: boolean, item: any, moneyMap: { [key: string]: string }, ischeck: boolean): void
  bus: any
  source: any
  checkedChildMap: any
  getOncheckedMapItem(e: boolean, item: any, id: string, flag: boolean): void
  countTotalMoney(data: any): void
  cleanCheckChildMap(): void
  userInfo?: StaffIF
  fnGroupIsDisabled(id: string, data: any): void
  index: number
  parentIndex: number
  onRetryCheckerInvoiceClick: (parentIndex: number, index: number, item: any) => void
  onUploadInvoiceMetaile?: (parentIndex: number, index: number, invoice: InvoiceIF) => void
}
interface IState {
  checked: boolean
  checkedMap: {
    [key: string]: any
  }
  isOpen: boolean
  source: any
  details: any[]
}
@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data.staff
}))
export default class InvoiceListItem extends PureComponent<IProps, IState> {
  count: number = 0
  childCount: number = 0
  constructor(props) {
    super(props)
    this.state = {
      checked: false,
      checkedMap: {},
      isOpen: false,
      source: this.props.source,
      details: this.props.source.details
    }
  }

  componentWillMount() {
    const { bus } = this.props
    bus.on('group:select:all:detail', this.handleCheckAllInvoice)
    bus.on('invoice:list:update:disabled', this.handleUpdeateDisabled)
    this.fnGetIsDisabled()
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus.un('group:select:all:detail', this.handleCheckAllInvoice)
    bus.un('invoice:list:update:disabled', this.handleUpdeateDisabled)
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.source !== nextProps.source) {
      this.setState({
        source: nextProps.source,
        details: nextProps?.source?.details
      })
    }
  }

  fnGetIsDisabled = () => {
    const { source } = this.state
    const { master, details, status } = source
    const {
      item: { id },
      fnGroupIsDisabled
    } = this.props
    const { entityId, form } = master
    let activeItme = null

    if (entityId === 'system_发票主体' && status !== 'UNEXAMINED' && !isElectronicAirAndTrain(form)) {
      activeItme = details.filter(item => !!item.active)
      activeItme = !!activeItme.length ? { length: activeItme.length } : { length: 0 }
    } else {
      activeItme = master.active && source.ischeck ? { length: 1 } : { length: 0 }
    }
    const resActiceMap = new Map()
    resActiceMap[id] = activeItme
    fnGroupIsDisabled(id, activeItme)
  }

  handleUpdeateDisabled = changeDetails => {
    const { checkedMap, details } = this.state
    const { cleanCheckChildMap } = this.props
    const map: any = array2object(changeDetails)
    details.forEach(item => {
      const changeItem = map[item.id]
      if (changeItem && !changeItem.active) {
        delete checkedMap[item.id]
        item.active = changeItem.active
      }
    })
    cleanCheckChildMap && cleanCheckChildMap()
    this.setState({
      details: details.slice(),
      checkedMap
    })
  }

  fnFilterListType = type => {
    const Overseas = ['Invoice', 'Receipt', 'Pay Order', 'Other']
    if(Overseas.includes(type)){
       return 'list-violet'
    }
    switch (type) {
      case i18n.get('铁路客票'):
        return 'list-blue'
      case i18n.get('机票行程单'):
        return 'list-blue'
      case i18n.get('客运汽车票'):
        return 'list-blue'
      case i18n.get('其他票据'):
        return 'list-other'
      case i18n.get('机打电子发票'):
      case i18n.get('机打纸质发票'):
      case i18n.get('机打发票'):
        return 'list-green'
      case i18n.get('医疗发票'):
        return 'list-green'
      case i18n.get('非税收入类票据'):
      case i18n.get('非税收入通用票据'):
      case i18n.get('非税收入一般缴款书'):
      case i18n.get('其他财政票据'):
        return 'list-violet'
      default:
        return 'list-orange'
    }
  }

  fnFilterInvoiceState = (type, master) => {
    const { item } = this.props
    const { sourceData } = item
    const { entityId } = master
    const isShow = entityId === 'system_发票主体'
      // entityId === 'system_发票主体' ||
      // (entityId === 'system_非税收入类票据' && master.form?.E_system_非税收入类票据_发票类别 === '非税收入通用票据') //@i18n-ignore
    if (!isShow) {
      return <></>
    }
    switch (type) {
      case 'NO_VISIBLE':
        return (
          <Popover content={<RichMessage message={sourceData.message} />}>
            <EKBIcon name="#EDico-plaint-circle" className="warning-icon" />
          </Popover>
        )
      case 'NO_RESULT':
        return (
          <Popover content={<RichMessage message={sourceData.message} />}>
            {/* <Icon type="exclamation-circle" className="list-result" /> */}
            <img src={SVG_ERR_INVOICE} />
          </Popover>
        )
      case 'SUCCESS':
        return <img src={SVG_SUS_INVOICE} />
      default:
        return <></>
    }
  }

  handleEdit = () => {
    const { item, onEdit } = this.props
    const { checked } = this.state
    onEdit(item).then(res => {
      if (checked) {
        this.handleCheckAllInvoice(checked, false)
      }
    })
  }

  handleCheckAllInvoice = (checked, isAsyc) => {
    if (isAsyc) {
      const { hasActive } = this.fnGetActive()
      if (!hasActive) {
        return
      }
    }
    let { checkedMap } = this.state
    const { source } = this.state
    const { onCheckedChange, getOncheckedMapItem, userInfo } = this.props
    const { master, details } = source
    const { id } = master
    let totalMoney = '0'
    let cDetails = details
    if (checked) {
      if (!!details.length) {
        cDetails = details.filter(detail => {
          if (detail.active) {
            checkedMap[detail.id] = detail
          }
          return detail.active
        })
        totalMoney = !!cDetails.length ? totalInvoceMoney(cDetails) : '0'
      } else {
        if (master.active) {
          totalMoney = getTaxAmount(master)
          checkedMap[master.id] = master
        }
      }
      this.count = this.count + 1
      //埋点
      fnCheckinvoiceTrack(userInfo, this.count)
    } else {
      totalMoney = '0'
      checkedMap = {}
      cDetails = []
    }
    const resMap = {
      master: master,
      details: cDetails,
      originalData: {
        master: master,
        details: details
      }
    }
    getOncheckedMapItem(checked, resMap, id, false)
    const itemMoney = { [id]: totalMoney }
    this.setState({ checkedMap: Object.assign({}, checkedMap), checked }, () => {
      onCheckedChange(checked, id, itemMoney, checked)
    })
  }

  handleItemOnCheckedChange = (detail, e) => {
    const { checked } = e.target
    const { onCheckedChange, getOncheckedMapItem, userInfo } = this.props
    const { source } = this.state
    const { master, details } = source
    const { id } = master
    const { checkedMap } = this.state
    let totalMoney = '0'
    if (checked) {
      checkedMap[detail.id] = detail
      if (!!details.length) {
        const checkedData = Object.values(checkedMap)
        totalMoney = !!checkedData.length ? totalInvoceMoney(Object.values(checkedMap)) : '0'
      } else {
        totalMoney = master.active ? getTaxAmount(master) : '0'
      }
      this.childCount = this.childCount + 1
      //埋点
      fnCheckdetailTrack(userInfo, this.childCount)
    } else {
      delete checkedMap[detail.id]
      const checkedData = Object.values(checkedMap)
      if (!!details.length) {
        totalMoney = !!checkedData.length ? totalInvoceMoney(Object.values(checkedMap)) : '0'
      } else {
        totalMoney = getTaxAmount(master)
      }
    }
    const resMap = {
      master: master,
      details: [detail],
      originalData: {
        master: master,
        details: details
      }
    }
    getOncheckedMapItem(checked, resMap, id, true)
    const checkListId = Object.keys(checkedMap)
    const itemMoney = { [id]: totalMoney }
    const validList = details.filter(line => line.active)
    const ischecked = checkListId.length === validList.length
    this.setState({ checkedMap: Object.assign({}, checkedMap), checked: ischecked }, () => {
      onCheckedChange(ischecked, id, itemMoney, !!checkListId.length)
    })
  }
  handleOpen = () => {
    this.setState({
      isOpen: !this.state.isOpen
    })
  }

  fnGetActive = () => {
    const { item } = this.props
    const { source, details, checked } = this.state
    const { master } = source
    const { entityId, form} = master
    let hasActiveData = []
    let hasActive = false
    let canCheck = false
    const { ischeck, active, status } = item
    if (entityId === 'system_发票主体') {
      const isSpecial = status === 'UNEXAMINED' || isElectronicAirAndTrain(form)
      
      hasActiveData = details.filter(item => !!item.active)
      if (ischeck) {
        hasActive = isSpecial ? true : !!hasActiveData.length
      }
      canCheck = hasActive
    } else {
      hasActive = ischeck && active
      canCheck = active
    }
    const _checked = canCheck ? checked : false
    return { _checked, hasActive }
  }

  // 新增：编辑按钮显示逻辑判断方法
  shouldShowEditButton = () => {
    const { item } = this.props
    const { source } = this.state
    const { master } = source
    const fileName = get(master, 'form.E_system_发票主体_图片', '')
    const isOfd = fileName?.toLowerCase()?.endsWith('.ofd')
    
    // OFD 文件不显示编辑按钮
    if (isOfd) {
      return false
    }
    
    // 新增：查验失败且国税官方服务繁忙时不显示编辑按钮
    // 和后端约定好的，有这两个字符代表不正常，不显示编辑按钮
    if (source && source.status !== 'SUCCESS' && source.message && 
        (source.message.includes('国税') || source.message.includes('税务局系统'))) {
      return false
    }
    
    // 根据发票类型判断
    if (item.type === 'Invoice') {
      // Invoice 类型：只有在 EDIT 或 UNEXAMINED 状态下才显示
      return source?.status === 'EDIT' || source?.status === 'UNEXAMINED'
    } else {
      // 非 Invoice 类型：NO_RESULT 状态下不显示，其他状态显示
      return source?.status !== 'NO_RESULT'
    }
  }

  // 新增：渲染编辑按钮
  renderEditButton = () => {
    if (!this.shouldShowEditButton()) {
      return null
    }

    const { master } = this.state.source
    const isInvoice = master.entityId === 'system_发票主体'
    const isCheckerInvoice = getIsCheckerInvoice(master?.form)
    const { metaileText } = invoiceMetaile(master)
    const { currency } = app.getState()['@bills'].dimentionCurrencyInfo || {}
    const isdimentionCurrency = currency && enableOtherInvoiceByDimension()
    
    // 样式类名计算直接在这里处理
    const className = classNames('list-edit', { 
      'ml-4': metaileText?.length || (isInvoice && !isCheckerInvoice) 
    })
    
    return (
      <Button
        category='text'
        theme='highlight'
        className={className}
        onClick={() => this.handleEdit()}
      >
        {i18n.get('编辑')}
        {isdimentionCurrency && (
          <Tooltip title={i18n.get('为确保多币种金额正确请检查信息')}>
            <OutlinedTipsInfo className="ml-4" />
          </Tooltip>
        )}
      </Button>
    )
  }

  render() {
    const { item, onRetryCheckerInvoiceClick, index, parentIndex, onUploadInvoiceMetaile } = this.props
    const { checkedMap, isOpen, source, details } = this.state
    const { ischeck: itemIscheck } = item
    const { master } = source
    const checkListId = Object.keys(checkedMap)
    // @i18n-ignore
    const isInvoice = master.entityId === 'system_发票主体'
    const isNontaxInvoice = master.entityId === 'system_非税收入类票据'
    // @i18n-ignore
    const isCheckerInvoice = getIsCheckerInvoice(master?.form)
    const status = getInvoiceStatus(master?.form)
    const realTagClass = isCheckerInvoice && status !== 'INVOICE_NULLIFY' ? 'real-card' : 'no-real-card'
    const { metaileText, shoMetaileAction, metaileColor, metaileBgColor } = invoiceMetaile(master)
    const fileName = get(master, 'form.E_system_发票主体_图片', '')
    const isOfd = fileName?.toLowerCase()?.endsWith('.ofd')
    const isOverseas = item.type === 'Overseas'
    const isCurrency = get(master, 'form.E_system_海外发票_currency', '')
    const moneyValue = isOverseas ? convertForeignToStandard(item.moneyForeign) : item.money
    const  { currency } = app.getState()['@bills'].dimentionCurrencyInfo || {}
    const isdimentionCurrency = currency && enableOtherInvoiceByDimension()
    const openActions = (
      <Button  
        category='text'
        theme='highlight' className="open" onClick={this.handleOpen}>
        {isOpen ? i18n.get('收起') : i18n.get('展开')}
      </Button>
    )
    const _edit_result = item.status === 'EDIT' ? (item.message !== null ? 'NO_RESULT' : item.status) : item.status
    const { _checked, hasActive } = this.fnGetActive()
    return (
      <div className={style['list-items']}>
        <div className="item-title">
          <div className="item-title-wrapper">
            <Checkbox
              checked={_checked}
              disabled={!hasActive}
              onChange={e => this.handleCheckAllInvoice(e.target.checked, false)}
            >
              <span className="file-title ml-16">{item.title}</span>
            </Checkbox>
            <span className="ml-16">{this.fnFilterInvoiceState(_edit_result, master)}</span>
            {isInvoice && (
              <div className={`${realTagClass} ml-4`}>
                <T name={isCheckerInvoice ? invoiceStatusStr(master?.form) : '未验真'} />
              </div>
            )}
            {metaileText?.length ? (
              <div style={{ color: metaileColor, backgroundColor: metaileBgColor }} className="metaile-tag ml-4">
                <T name={metaileText} />
              </div>
            ) : null}
          </div>
          <div className="item-right">
            <Money currencySize={15}  valueSize={15} color="#1d2b3d"  showSymbol={isOverseas ? isCurrency : true} value={moneyValue} 
             isShowForeign={!isOverseas && moneyValue?.foreignNumCode}
             onlyForeign={!isOverseas && moneyValue?.foreignNumCode} />
          </div>
        </div>
        <div className="item-body">
          <div className="list-type-item mb-8 mt-8">
            <div className={`list-type ${this.fnFilterListType(item.tagName)}`}>{item && item.tagName}</div>
            {getInvoiceSignature(master, 'ml-4 mr-4')}
            <div className="item-subtitle">{item.subTitle}</div>
            <div className="action-wrapper">
              <div className="action-wrapper-row">
                {shoMetaileAction ? (
                  <InvoiceUploadView
                    invoiceId={master?.id}
                    children={<div className="checker-invoice">{i18n.get('上传原文件')}</div>}
                    onResult={invoice => onUploadInvoiceMetaile(parentIndex, index, invoice)}
                  />
                ) : null}
                {isInvoice && !isCheckerInvoice && (
                  <Button
                    category='text'
                    theme='highlight'
                    className={classNames({ 'ml-4': metaileText?.length })}
                    onClick={() => onRetryCheckerInvoiceClick(parentIndex, index, item)}
                  >
                    {i18n.get('点击验真')}
                  </Button>
                )}
                {/* 优化后的编辑按钮渲染 */}
                {this.renderEditButton()}
              </div>
              {isNontaxInvoice ? (
                <></>
              ) : item.type === 'Invoice' && item.status !== 'EDIT' && item.status !== 'UNEXAMINED' && details?.length > 0 ? (
                openActions
              ) : (
                <></>
              )}
            </div>
          </div>
          {source && source.status !== 'SUCCESS' && (
            <div className={`type-error mb-8 ${_edit_result}`}>
              <RichMessage message={source.message} theme="light" />
            </div>
          )}
          {source && source.bindValidateMessage && (
            <div className={`type-error mb-8`}>
              <RichMessage message={source.bindValidateMessage} theme="light" />
            </div>
          )}
          {isOpen &&
            details &&
            details.map(item => {
              return (
                <InvoiceDetailItem
                  key={item.id}
                  detail={item}
                  ischeck={itemIscheck}
                  checkListId={checkListId}
                  handleItemOnCheckedChange={this.handleItemOnCheckedChange}
                />
              )
            })}
        </div>
      </div>
    )
  }
}
