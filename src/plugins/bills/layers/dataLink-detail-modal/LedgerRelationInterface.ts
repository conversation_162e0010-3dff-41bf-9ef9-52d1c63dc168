/*
 * @Author: joyBoy
 * @Date: 2023-12-06 18:13:06
 * @LastEditors: wangweidong <EMAIL>
 * @LastEditTime: 2024-03-28 17:14:48
 * @FilePath: /web/src/plugins/bills/layers/dataLink-detail-modal/LedgerRelationInterface.ts
 */
import { EntityListDataItem, EntityLayoutTemplate } from '@ekuaibao/ekuaibao_types'

export interface LedgerInterface {
  id: string
  name: string
  totalAmount: any
  statisticsSource: StatisticsSourceEnum
  isCalculation: boolean
  fieldType: 'NUMBER' | 'MONEY'
  unit?: string
  sumFieldEnLabel?: string
}

export interface State {
  count: number
  currentLedger: LedgerInterface
}

export enum StatisticsSourceEnum {
  MASTER = 'MASTER',
  DETAILS = 'DETAILS',
  DATA_LINK = 'DATA_LINK',
  INVOICE = 'INVOICE'
}

export interface LedgerListItemInterface {
  background?: string
  icon?: string
  title: string
  desc: string
  amount: any
  sumFieldName: string
  active: boolean
  flowId?: string
  detailId?: string
  unit?: string
  fieldType: 'NUMBER' | 'MONEY'
  totalNum?: string
  originalApportionAmount?: any
  originalApportionTotalNum?: string
  fromApporation?: boolean
  manualApporationLabel?: string
}

export interface LedgerEntityListItem {
  item: EntityListDataItem
  template: EntityLayoutTemplate
}

export interface LedgerListViewInterface {
  item: LedgerListItemInterface
  onClick: any
  style: any
}
