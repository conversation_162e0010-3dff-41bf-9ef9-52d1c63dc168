@import '~@ekuaibao/eui-styles/less/token.less';

.bank-card-wrap {
  border-radius: 8px;
  position: relative;
  border: 1px solid var(--eui-line-divider-default, rgba(29, 33, 41, 0.1));
  &:hover {
    box-shadow: var(--eui-shadow-down-3);
  }
  &.card-selected {
    border-color: var(--eui-primary-pri-500);
  }
  // 不同状态
  &.bank-status .bank-card {
    background: linear-gradient(98deg, #fff8f8 0%, #ffe5e5 100%);
    .bank-name,
    .bank-card-no {
      color: var(--eui-function-danger-900, #4d000a);
    }
  }

  &.wx-status .bank-card {
    background: linear-gradient(98deg, rgb(245, 255, 249) 0%, rgb(216, 245, 228) 100%);
    .bank-name,
    .bank-card-no {
      color: var(--eui-function-success-900, #004d1c);
    }
  }

  &.public-status .bank-card {
    background: linear-gradient(98deg, rgb(241, 253, 253) 0%, rgb(214, 248, 246) 99.53%);
    .bank-name,
    .bank-card-no {
      color: var(--eui-decorative-cyan-900, #00424d);
    }
  }

  &.account-status .bank-card {
    background: linear-gradient(98deg, rgb(245, 249, 255) 0%, rgb(216, 232, 255) 100%);
    .bank-name,
    .bank-card-no {
      color: var(--eui-function-info-900, #001a4d);
    }
  }

  &.disable-status .bank-card {
    background: linear-gradient(98deg, #f5f5f5 0%, #e5e6e8 99.53%);
    color: var(--eui-text-disabled, rgba(29, 33, 41, 0.3));
    filter: grayscale(100%);
    .bank-name,
    .bank-card-no,
    .bank-remark,
    .bank-card-footer .bank-branch,
    .bank-card-footer .bank-card-owner,
    .eui-tag {
      color: var(--eui-text-disabled, rgba(29, 33, 41, 0.3));
    }
    .bank-card-footer:hover {
      .bank-card-info .top-action {
        visibility: hidden;
      }
    }
  }

  .card-item-mask {
    position: absolute;
    pointer-events: none;
    border-radius: 8px;
    top: 0;
    left: -1px;
    right: -1px;
    bottom: 0;
    // background: var(--eui-transparent-n900-10, rgba(29, 33, 41, 0.1));
    z-index: 1;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 0;
      height: 0;
      border-bottom: 25px solid var(--eui-primary-pri-500, #2555ff);
      border-left: 25px solid transparent;
      border-bottom-right-radius: 6px;
    }
  }
  .mask-icon {
    position: absolute;
    pointer-events: none;
    z-index: 2;
    bottom: 0px;
    right: 0px;
    color: #fff;
  }
  .bank-item-top-bg {
    position: absolute;
    right: 0;
    pointer-events: none;
    height: 70px;
    bottom: 44px;
  }

  .bank-card {
    position: relative;
    padding: 12px 16px 0px 16px;
    border-radius: 8px;
    .bank-card-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .bank-name {
        font-weight: 500;
        flex: 1;
        font: var(--eui-font-body-b1);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 12px;
      }

      .top-action {
        display: flex;
        margin-left: 12px;

        .default-action {
          color: var(--eui-primary-pri-500, #2555ff);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          margin: 2px 4px;
          visibility: hidden;
          cursor: pointer;
        }

        .collect-action {
          width: 24px;
          height: 24px;
          flex-shrink: 0;
          line-height: 24px;
          border-radius: 6px;
          margin-left: 8px;
          text-align: center;
          cursor: pointer;
          visibility: hidden;
          &.visible {
            visibility: visible;
          }
          &:hover {
            background: var(--eui-fill-hover, rgba(29, 33, 41, 0.05));
          }
          .eui-icon-OutlinedGeneralCollection {
            color: var(--eui-primary-pri-500, #2555ff);
          }
        }
      }
    }

    .bank-card-no {
      color: var(--eui-text-title, rgba(29, 33, 41, 0.9));
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      margin: 4px 0;
    }

    .bank-card-tag {
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
    }
    .bank-remark {
      width: 106px;
      color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.5));
      font-size: 12px;
      font-style: normal;
      z-index: 2;
      font-weight: 400;
    }

    .bank-card-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: -16px;
      margin-right: -16px;
      height: 44px;
      padding: 0 16px;
      background-color: #fff;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;

      .bank-card-icon {
        flex: 1;
        display: flex;
        align-items: center;
        margin-right: 12px;
        .bank-icon {
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          margin-right: 4px;
        }
      }
      .bank-branch {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      
      .bank-card-owner {
        color: var(--eui-text-caption, rgba(29, 33, 41, 0.7));
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }

    &:hover {
      .top-action .default-action,
      .top-action .collect-action {
        visibility: visible;
      }
    }
  }
  .card-selected-item {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 16px;
    height: 16px;
    border-bottom-right-radius: 4px;
    border-width: 8px;
    border-style: solid;
    border-color: #ffffff var(--eui-primary-pri-500) var(--eui-primary-pri-500) #ffffff;
    font-size: 12px;
    color: #fff;
    display: none;
  }
}
